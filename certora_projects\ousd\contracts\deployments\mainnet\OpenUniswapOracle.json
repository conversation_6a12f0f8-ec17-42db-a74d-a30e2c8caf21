{"abi": [{"constant": true, "inputs": [], "name": "governor", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "string", "name": "symbol", "type": "string"}], "name": "tokEthPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "string", "name": "symbol", "type": "string"}], "name": "debugPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "string", "name": "symbol", "type": "string"}], "name": "tokUsdPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "claimGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "ethPriceOracle_", "type": "address"}], "name": "registerEthPriceOracle", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "string", "name": "symbol", "type": "string"}], "name": "getSwapConfig", "outputs": [{"components": [{"internalType": "bool", "name": "ethOnFirst", "type": "bool"}, {"internalType": "address", "name": "swap", "type": "address"}, {"internalType": "uint256", "name": "blockTimestampLast", "type": "uint256"}, {"internalType": "uint256", "name": "latestBlockTimestampLast", "type": "uint256"}, {"internalType": "uint256", "name": "priceCumulativeLast", "type": "uint256"}, {"internalType": "uint256", "name": "latestPriceCumulativeLast", "type": "uint256"}, {"internalType": "uint256", "name": "baseUnit", "type": "uint256"}], "internalType": "struct OpenUniswapOracle.SwapConfig", "name": "", "type": "tuple"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "bytes32[]", "name": "symbolHashes", "type": "bytes32[]"}], "name": "updatePriceWindows", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "ethUsdPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "ethPriceOracle", "outputs": [{"internalType": "contract IPriceOracle", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "PERIOD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "isGovernor", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "string", "name": "symbol", "type": "string"}], "name": "openPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_newGovernor", "type": "address"}], "name": "transferGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "pair_", "type": "address"}], "name": "registerPair", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "string", "name": "symbol", "type": "string"}], "name": "price", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "ethPriceOracle_", "type": "address"}, {"internalType": "address", "name": "ethToken_", "type": "address"}], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "PendingGovernorshipTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "GovernorshipTransferred", "type": "event"}], "receipt": {"to": null, "from": "******************************************", "contractAddress": "******************************************", "transactionIndex": 26, "gasUsed": "2229836", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000001001000000048000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000800000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000400000020000000200000000000000000000000000000000000000000000000000000000001", "blockHash": "0x0dbefb38f58ba41ac4f9f84a30f418745e5efb036b2d74eb72296baa7c671190", "transactionHash": "0x9765281b1891f5a7552b337f6511f8c4ce13ac70f885fe827bc25977d97b1514", "logs": [{"transactionIndex": 26, "blockNumber": 11038444, "transactionHash": "0x9765281b1891f5a7552b337f6511f8c4ce13ac70f885fe827bc25977d97b1514", "address": "******************************************", "topics": ["0xc7c0c772add429241571afb3805861fb3cfa2af374534088b76cdb4325a87e9a", "0x0000000000000000000000000000000000000000000000000000000000000000", "0x000000000000000000000000aed9fdc9681d61edb5f8b8e421f5cee8d7f4b04f"], "data": "0x", "logIndex": 51, "blockHash": "0x0dbefb38f58ba41ac4f9f84a30f418745e5efb036b2d74eb72296baa7c671190"}], "blockNumber": 11038444, "cumulativeGasUsed": "6338725", "status": 1, "byzantium": true}, "address": "******************************************", "args": ["0x922018674c12a7f0d394ebeef9b58f186cde13c1", "0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2"], "solcInputHash": "0xe198a4f714a9d5197026cf78b295a6fd26dbd68e473094a20c650315d538ef64", "metadata": "{\"compiler\":{\"version\":\"0.5.11+commit.c082d0b4\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"constant\":true,\"inputs\":[],\"name\":\"governor\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"}],\"name\":\"tokEthPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"}],\"name\":\"debugPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"}],\"name\":\"tokUsdPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"claimGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"ethPriceOracle_\",\"type\":\"address\"}],\"name\":\"registerEthPriceOracle\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"}],\"name\":\"getSwapConfig\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"ethOnFirst\",\"type\":\"bool\"},{\"internalType\":\"address\",\"name\":\"swap\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"blockTimestampLast\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"latestBlockTimestampLast\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"priceCumulativeLast\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"latestPriceCumulativeLast\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"baseUnit\",\"type\":\"uint256\"}],\"internalType\":\"struct OpenUniswapOracle.SwapConfig\",\"name\":\"\",\"type\":\"tuple\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"symbolHashes\",\"type\":\"bytes32[]\"}],\"name\":\"updatePriceWindows\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"ethUsdPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"ethPriceOracle\",\"outputs\":[{\"internalType\":\"contract IPriceOracle\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"PERIOD\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"isGovernor\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"}],\"name\":\"openPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_newGovernor\",\"type\":\"address\"}],\"name\":\"transferGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"pair_\",\"type\":\"address\"}],\"name\":\"registerPair\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"}],\"name\":\"price\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"ethPriceOracle_\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"ethToken_\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"PendingGovernorshipTransfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"GovernorshipTransferred\",\"type\":\"event\"}],\"devdoc\":{\"methods\":{\"claimGovernance()\":{\"details\":\"Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor.\"},\"governor()\":{\"details\":\"Returns the address of the current Governor.\"},\"isGovernor()\":{\"details\":\"Returns true if the caller is the current Governor.\"},\"transferGovernance(address)\":{\"details\":\"Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete\",\"params\":{\"_newGovernor\":\"Address of the new Governor\"}}}},\"userdoc\":{\"methods\":{}}},\"settings\":{\"compilationTarget\":{\"contracts/oracle/OpenUniswapOracle.sol\":\"OpenUniswapOracle\"},\"evmVersion\":\"petersburg\",\"libraries\":{},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/upgrades/contracts/Initializable.sol\":{\"keccak256\":\"0x9bfec92e36234ecc99b5d37230acb6cd1f99560233753162204104a4897e8721\",\"urls\":[\"bzz-raw://5cf7c208583d4d046d75bd99f5507412ab01cce9dd9f802ce9768a416d93ea2f\",\"dweb:/ipfs/QmcQS1BBMPpVEkXP3qzwSjxHNrqDek8YeR7xbVWDC9ApC7\"]},\"contracts/governance/Governable.sol\":{\"keccak256\":\"0x342fa1b2e2cbe8d2d904c31e5a2b182446d3737db2f6704d3f247c6c733084ec\",\"urls\":[\"bzz-raw://93918076cf5ef49658f8dd78ac7aafe30a1e233cc49e70a642ee9559b78c3f28\",\"dweb:/ipfs/Qmcca1ncSQnxSRXs5FZHzH9dwfZ1KcuUfcAiBwgkbzfTeW\"]},\"contracts/governance/InitializableGovernable.sol\":{\"keccak256\":\"0x104955cf6b2f2a4d91418398ae2c0ede6db9dcbdc1fe7f612742ba49d513af66\",\"urls\":[\"bzz-raw://446071847d8c5cb69bf8ebd05a3a4ef08d873b21a4863d6f738d0cee77cba222\",\"dweb:/ipfs/QmerqnGBbePY89Qq63FDoDcJULNSu6sxa2k9czGyDVPynZ\"]},\"contracts/interfaces/IEthUsdOracle.sol\":{\"keccak256\":\"0xa21cf65c92ec429862e2e5f73bf045de05d2c3a3537e45ef748949ef9015dac5\",\"urls\":[\"bzz-raw://d40f5ec9ae264c37a5b05507653deb4b903fe6737622b0ed4e294ffbb9e34ff1\",\"dweb:/ipfs/QmSJLrHJSisCHsP6nmJGs7WDLzzxnbX4gN66d2B5mRc6te\"]},\"contracts/interfaces/IPriceOracle.sol\":{\"keccak256\":\"0x943aaa48eecafcbe186d21d3e4d7a2dbe356e45611b4d9794f0df63ba9abdec7\",\"urls\":[\"bzz-raw://57e656b1f0efb9ad24b5df2ec870a4c9401b597b865733917734184a93cbdcfa\",\"dweb:/ipfs/QmdxoCYkeXipmFRinXV1WjWX52MDQJbM26VwmmYjvMHsfQ\"]},\"contracts/interfaces/uniswap/IUniswapV2Pair.sol\":{\"keccak256\":\"0xf722b9b3a04647d5a596b92dbc9aa6208aa999a51a4380197d2762be9591a436\",\"urls\":[\"bzz-raw://127a6bfbefbbc07ce0e3c72c02280fb097c062f4ca33a2ec97edb8cc916e36c7\",\"dweb:/ipfs/QmZ9TffGaf9uXgsbxK5ns9UfNhpkngeqWS2LNdhCbN133X\"]},\"contracts/oracle/OpenUniswapOracle.sol\":{\"keccak256\":\"0x1b0a65b5937b8e84c47cd087624a939a3dd47d8f848057ff49eca939b6807799\",\"urls\":[\"bzz-raw://76c8f2979a750f2536d0038b48e38c112e69d8462df0a62befb537bba03f0702\",\"dweb:/ipfs/QmVeKsmsHZ38BXKYjMiEAkD9PJvLGUHURzpgF1wx1DYpe9\"]},\"contracts/oracle/UniswapLib.sol\":{\"keccak256\":\"0xadb8c093df54922a9e7e7b5f6e481e95c6a1c39bbe6378f909226e6d456d0223\",\"urls\":[\"bzz-raw://9e521d6561cedb30246f04848c8b4444b55ea29a3d2c4faab5e82150aeb62a77\",\"dweb:/ipfs/QmdjvVN631yAuZkKQdgjNybmjGuTwes5A8fzCQHvaHonhY\"]}},\"version\":1}", "bytecode": "0x60806040523480156200001157600080fd5b506040516200287e3803806200287e8339818101604052620000379190810190620001b3565b62000048336200013c60201b60201c565b620000586200016b60201b60201c565b73ffffffffffffffffffffffffffffffffffffffff16600073ffffffffffffffffffffffffffffffffffffffff167fc7c0c772add429241571afb3805861fb3cfa2af374534088b76cdb4325a87e9a60405160405180910390a381603460006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff16021790555080603560006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff160217905550505062000242565b60007f7bea13895fa79d2831e0a9e28edede30099005a50d652d8957cf8a607ee6ca4a60001b90508181555050565b6000807f7bea13895fa79d2831e0a9e28edede30099005a50d652d8957cf8a607ee6ca4a60001b9050805491505090565b600081519050620001ad8162000228565b92915050565b60008060408385031215620001c757600080fd5b6000620001d7858286016200019c565b9250506020620001ea858286016200019c565b9150509250929050565b6000620002018262000208565b9050919050565b600073ffffffffffffffffffffffffffffffffffffffff82169050919050565b6200023381620001f4565b81146200023f57600080fd5b50565b61262c80620002526000396000f3fe608060405234801561001057600080fd5b50600436106101005760003560e01c80639478ab8c11610097578063d182801411610066578063d1828014146102a0578063d38bfff4146102d0578063dc314b44146102ec578063fe2c61981461030857610100565b80639478ab8c14610228578063ac0e47f514610246578063b4d1d79514610264578063c7af33521461028257610100565b80635d36b190116100d35780635d36b190146101b6578063606082dd146101c05780636c47e98a146101dc57806379ae25f41461020c57610100565b80630c340a24146101055780632d9ff2961461012357806353d683d61461015357806357d6931414610186575b600080fd5b61010d610338565b60405161011a9190612203565b60405180910390f35b61013d60048036036101389190810190611ca5565b610347565b60405161014a9190612395565b60405180910390f35b61016d60048036036101689190810190611ca5565b6104b1565b60405161017d94939291906123b0565b60405180910390f35b6101a0600480360361019b9190810190611ca5565b610707565b6040516101ad9190612395565b60405180910390f35b6101be6107be565b005b6101da60048036036101d59190810190611c0e565b61083e565b005b6101f660048036036101f19190810190611ca5565b6108c9565b604051610203919061237a565b60405180910390f35b61022660048036036102219190810190611c60565b6109c8565b005b610230610a21565b60405161023d9190612395565b60405180910390f35b61024e610b08565b60405161025b9190612239565b60405180910390f35b61026c610b2e565b6040516102799190612395565b60405180910390f35b61028a610b33565b604051610297919061221e565b60405180910390f35b6102ba60048036036102b59190810190611ca5565b610b70565b6040516102c79190612395565b60405180910390f35b6102ea60048036036102e59190810190611c0e565b610c27565b005b61030660048036036103019190810190611c0e565b610cdb565b005b610322600480360361031d9190810190611ca5565b6110fc565b60405161032f9190612395565b60405180910390f35b60006103426113ce565b905090565b600080838360405160200161035d9291906121bc565b6040516020818303038152906040528051906020012090506000603360008381526020019081526020016000209050600080600061039a846113ff565b9250925092508183116103e2576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016103d99061231a565b60405180910390fd5b804211610424576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161041b906122fa565b60405180910390fd5b60008142039050610433611a22565b604051806020016040528083886003015488038161044d57fe5b047bffffffffffffffffffffffffffffffffffffffffffffffffffffffff168152509050600061047c8261146a565b90506b204fce5e3e250261100000006104998289600501546114a6565b816104a057fe5b049850505050505050505092915050565b600080600080600086866040516020016104cc9291906121bc565b6040516020818303038152906040528051906020012090506000603460009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1663fe2c61986040518060400160405280600381526020017f45544800000000000000000000000000000000000000000000000000000000008152506040518263ffffffff1660e01b81526004016105769190612278565b60206040518083038186803b15801561058e57600080fd5b505afa1580156105a2573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052506105c69190810190611d7a565b9050600060336000848152602001908152602001600020905060006105ea82611516565b905081600301548111610632576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016106299061231a565b60405180910390fd5b81600101544211610678576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161066f906122fa565b60405180910390fd5b600082600101544203905061068b611a22565b60405180602001604052808386600301548603816106a557fe5b047bffffffffffffffffffffffffffffffffffffffffffffffffffffffff16815250905060006106d48261146a565b905060006106e282886114a6565b9050856003015485038483839b509b509b509b50505050505050505092959194509250565b6000603460009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1663fe2c619884846040518363ffffffff1660e01b8152600401610766929190612254565b60206040518083038186803b15801561077e57600080fd5b505afa158015610792573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052506107b69190810190611d7a565b905092915050565b6107c6611579565b73ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff1614610833576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161082a9061233a565b60405180910390fd5b61083c336115aa565b565b610846610b33565b610885576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161087c9061229a565b60405180910390fd5b80603460006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff16021790555050565b6108d1611a53565b600083836040516020016108e69291906121bc565b604051602081830303815290604052805190602001209050603360008281526020019081526020016000206040518060e00160405290816000820160009054906101000a900460ff161515151581526020016000820160019054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020016001820154815260200160028201548152602001600382015481526020016004820154815260200160058201548152505091505092915050565b60008090505b82829050811015610a1c576000603360008585858181106109eb57fe5b9050602002013581526020019081526020016000209050610a0b816113ff565b5050505080806001019150506109ce565b505050565b6000603460009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1663fe2c61986040518060400160405280600381526020017f45544800000000000000000000000000000000000000000000000000000000008152506040518263ffffffff1660e01b8152600401610ab39190612278565b60206040518083038186803b158015610acb57600080fd5b505afa158015610adf573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250610b039190810190611d7a565b905090565b603460009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1681565b607881565b6000610b3d6113ce565b73ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff1614905090565b6000603460009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1663fe2c619884846040518363ffffffff1660e01b8152600401610bcf929190612254565b60206040518083038186803b158015610be757600080fd5b505afa158015610bfb573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250610c1f9190810190611d7a565b905092915050565b610c2f610b33565b610c6e576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610c659061229a565b60405180910390fd5b610c7781611687565b8073ffffffffffffffffffffffffffffffffffffffff16610c966113ce565b73ffffffffffffffffffffffffffffffffffffffff167fa39cc5eb22d0f34d8beaefee8a3f17cc229c1a1d1ef87a5ad47313487b1c4f0d60405160405180910390a350565b610ce3610b33565b610d22576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610d199061229a565b60405180910390fd5b600081905060008060019050603560009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168373ffffffffffffffffffffffffffffffffffffffff16630dfe16816040518163ffffffff1660e01b815260040160206040518083038186803b158015610dad57600080fd5b505afa158015610dc1573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250610de59190810190611c37565b73ffffffffffffffffffffffffffffffffffffffff161415610e86578273ffffffffffffffffffffffffffffffffffffffff1663d21220a76040518163ffffffff1660e01b815260040160206040518083038186803b158015610e4757600080fd5b505afa158015610e5b573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250610e7f9190810190611c37565b9150610f0b565b8273ffffffffffffffffffffffffffffffffffffffff16630dfe16816040518163ffffffff1660e01b815260040160206040518083038186803b158015610ecc57600080fd5b505afa158015610ee0573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250610f049190810190611c37565b9150600090505b600082905060608173ffffffffffffffffffffffffffffffffffffffff166395d89b416040518163ffffffff1660e01b815260040160006040518083038186803b158015610f5857600080fd5b505afa158015610f6c573d6000803e3d6000fd5b505050506040513d6000823e3d601f19601f82011682018060405250610f959190810190611cea565b905060006033600083604051602001610fae91906121ec565b6040516020818303038152906040528051906020012081526020019081526020016000209050838160000160006101000a81548160ff021916908315150217905550868160000160016101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff1602179055508273ffffffffffffffffffffffffffffffffffffffff1663313ce5676040518163ffffffff1660e01b815260040160206040518083038186803b15801561107957600080fd5b505afa15801561108d573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052506110b19190810190611da3565b60ff16600a0a81600501819055506110c881611516565b8160030181905550428160010181905550806001015481600201819055508060030154816004018190555050505050505050565b60008083836040516020016111129291906121bc565b6040516020818303038152906040528051906020012090506000603460009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1663fe2c61986040518060400160405280600381526020017f45544800000000000000000000000000000000000000000000000000000000008152506040518263ffffffff1660e01b81526004016111bc9190612278565b60206040518083038186803b1580156111d457600080fd5b505afa1580156111e8573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525061120c9190810190611d7a565b9050816040518060400160405280600381526020017f455448000000000000000000000000000000000000000000000000000000000081525060405160200161125591906121d5565b60405160208183030381529060405280519060200120141561127b5780925050506113c8565b6000603360008481526020019081526020016000209050600061129d82611516565b9050816003015481116112e5576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016112dc9061231a565b60405180910390fd5b8160010154421161132b576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401611322906122fa565b60405180910390fd5b600082600101544203905061133e611a22565b604051806020016040528083866003015486038161135857fe5b047bffffffffffffffffffffffffffffffffffffffffffffffffffffffff16815250905060006113878261146a565b9050600061139582886114a6565b90506ec097ce7bc90715b34b9f10000000006113b58288600501546114a6565b816113bc57fe5b04985050505050505050505b92915050565b6000807f7bea13895fa79d2831e0a9e28edede30099005a50d652d8957cf8a607ee6ca4a60001b9050805491505090565b60008060008061140e85611516565b90506000856002015442039050607881106114505785600201548660010181905550856004015486600301819055504286600201819055508186600401819055505b818660030154876001015494509450945050509193909250565b60006612725dd1d243ab82600001517bffffffffffffffffffffffffffffffffffffffffffffffffffffffff168161149e57fe5b049050919050565b6000808314156114b95760009050611510565b60008284029050828482816114ca57fe5b041461150b576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401611502906122ba565b60405180910390fd5b809150505b92915050565b60008060006115488460000160019054906101000a900473ffffffffffffffffffffffffffffffffffffffff166116b6565b50915091508360000160009054906101000a900460ff161561156e578092505050611574565b81925050505b919050565b6000807f44c4d30b2eaad5130ad70c3ba6972730566f3e6359ab83e800d905c61b1c51db60001b9050805491505090565b600073ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff16141561161a576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401611611906122da565b60405180910390fd5b8073ffffffffffffffffffffffffffffffffffffffff166116396113ce565b73ffffffffffffffffffffffffffffffffffffffff167fc7c0c772add429241571afb3805861fb3cfa2af374534088b76cdb4325a87e9a60405160405180910390a3611684816118e4565b50565b60007f44c4d30b2eaad5130ad70c3ba6972730566f3e6359ab83e800d905c61b1c51db60001b90508181555050565b60008060006116c3611913565b90508373ffffffffffffffffffffffffffffffffffffffff16635909c0d56040518163ffffffff1660e01b815260040160206040518083038186803b15801561170b57600080fd5b505afa15801561171f573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052506117439190810190611d7a565b92508373ffffffffffffffffffffffffffffffffffffffff16635a3d54936040518163ffffffff1660e01b815260040160206040518083038186803b15801561178b57600080fd5b505afa15801561179f573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052506117c39190810190611d7a565b915060008060008673ffffffffffffffffffffffffffffffffffffffff16630902f1ac6040518163ffffffff1660e01b815260040160606040518083038186803b15801561181057600080fd5b505afa158015611824573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052506118489190810190611d2b565b9250925092508363ffffffff168163ffffffff16146118da57600081850390508063ffffffff166118798486611929565b600001517bffffffffffffffffffffffffffffffffffffffffffffffffffffffff1602870196508063ffffffff166118b18585611929565b600001517bffffffffffffffffffffffffffffffffffffffffffffffffffffffff160286019550505b5050509193909250565b60007f7bea13895fa79d2831e0a9e28edede30099005a50d652d8957cf8a607ee6ca4a60001b90508181555050565b6000640100000000428161192357fe5b06905090565b611931611a22565b6000826dffffffffffffffffffffffffffff1611611984576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161197b9061235a565b60405180910390fd5b6040518060200160405280836dffffffffffffffffffffffffffff166070866dffffffffffffffffffffffffffff167bffffffffffffffffffffffffffffffffffffffffffffffffffffffff16901b7bffffffffffffffffffffffffffffffffffffffffffffffffffffffff16816119f857fe5b047bffffffffffffffffffffffffffffffffffffffffffffffffffffffff16815250905092915050565b604051806020016040528060007bffffffffffffffffffffffffffffffffffffffffffffffffffffffff1681525090565b6040518060e00160405280600015158152602001600073ffffffffffffffffffffffffffffffffffffffff16815260200160008152602001600081526020016000815260200160008152602001600081525090565b600081359050611ab781612576565b92915050565b600081519050611acc81612576565b92915050565b60008083601f840112611ae457600080fd5b8235905067ffffffffffffffff811115611afd57600080fd5b602083019150836020820283011115611b1557600080fd5b9250929050565b60008083601f840112611b2e57600080fd5b8235905067ffffffffffffffff811115611b4757600080fd5b602083019150836001820283011115611b5f57600080fd5b9250929050565b600082601f830112611b7757600080fd5b8151611b8a611b8582612422565b6123f5565b91508082526020830160208301858383011115611ba657600080fd5b611bb1838284612532565b50505092915050565b600081519050611bc98161258d565b92915050565b600081519050611bde816125a4565b92915050565b600081519050611bf3816125bb565b92915050565b600081519050611c08816125d2565b92915050565b600060208284031215611c2057600080fd5b6000611c2e84828501611aa8565b91505092915050565b600060208284031215611c4957600080fd5b6000611c5784828501611abd565b91505092915050565b60008060208385031215611c7357600080fd5b600083013567ffffffffffffffff811115611c8d57600080fd5b611c9985828601611ad2565b92509250509250929050565b60008060208385031215611cb857600080fd5b600083013567ffffffffffffffff811115611cd257600080fd5b611cde85828601611b1c565b92509250509250929050565b600060208284031215611cfc57600080fd5b600082015167ffffffffffffffff811115611d1657600080fd5b611d2284828501611b66565b91505092915050565b600080600060608486031215611d4057600080fd5b6000611d4e86828701611bba565b9350506020611d5f86828701611bba565b9250506040611d7086828701611be4565b9150509250925092565b600060208284031215611d8c57600080fd5b6000611d9a84828501611bcf565b91505092915050565b600060208284031215611db557600080fd5b6000611dc384828501611bf9565b91505092915050565b611dd581612480565b82525050565b611de481612480565b82525050565b611df381612492565b82525050565b611e0281612492565b82525050565b611e11816124ff565b82525050565b6000611e238385612464565b9350611e30838584612523565b611e3983612565565b840190509392505050565b6000611e508385612475565b9350611e5d838584612523565b82840190509392505050565b6000611e7482612459565b611e7e8185612475565b9350611e8e818560208601612532565b80840191505092915050565b6000611ea58261244e565b611eaf8185612464565b9350611ebf818560208601612532565b611ec881612565565b840191505092915050565b6000611ede8261244e565b611ee88185612475565b9350611ef8818560208601612532565b80840191505092915050565b6000611f11601a83612464565b91507f43616c6c6572206973206e6f742074686520476f7665726e6f720000000000006000830152602082019050919050565b6000611f51601783612464565b91507f6d756c7469706c69636174696f6e206f766572666c6f770000000000000000006000830152602082019050919050565b6000611f91601a83612464565b91507f4e657720476f7665726e6f7220697320616464726573732830290000000000006000830152602082019050919050565b6000611fd1601a83612464565b91507f6e6f77206d75737420636f6d65206166746572206265666f72650000000000006000830152602082019050919050565b6000612011602383612464565b91507f546865726520686173206265656e206e6f2063756d756c61746976652063686160008301527f6e676500000000000000000000000000000000000000000000000000000000006020830152604082019050919050565b6000612077603083612464565b91507f4f6e6c79207468652070656e64696e6720476f7665726e6f722063616e20636f60008301527f6d706c6574652074686520636c61696d000000000000000000000000000000006020830152604082019050919050565b60006120dd601783612464565b91507f4669786564506f696e743a204449565f42595f5a45524f0000000000000000006000830152602082019050919050565b60e0820160008201516121266000850182611dea565b5060208201516121396020850182611dcc565b50604082015161214c604085018261219e565b50606082015161215f606085018261219e565b506080820151612172608085018261219e565b5060a082015161218560a085018261219e565b5060c082015161219860c085018261219e565b50505050565b6121a7816124d8565b82525050565b6121b6816124d8565b82525050565b60006121c9828486611e44565b91508190509392505050565b60006121e18284611ed3565b915081905092915050565b60006121f88284611e69565b915081905092915050565b60006020820190506122186000830184611ddb565b92915050565b60006020820190506122336000830184611df9565b92915050565b600060208201905061224e6000830184611e08565b92915050565b6000602082019050818103600083015261226f818486611e17565b90509392505050565b600060208201905081810360008301526122928184611e9a565b905092915050565b600060208201905081810360008301526122b381611f04565b9050919050565b600060208201905081810360008301526122d381611f44565b9050919050565b600060208201905081810360008301526122f381611f84565b9050919050565b6000602082019050818103600083015261231381611fc4565b9050919050565b6000602082019050818103600083015261233381612004565b9050919050565b600060208201905081810360008301526123538161206a565b9050919050565b60006020820190508181036000830152612373816120d0565b9050919050565b600060e08201905061238f6000830184612110565b92915050565b60006020820190506123aa60008301846121ad565b92915050565b60006080820190506123c560008301876121ad565b6123d260208301866121ad565b6123df60408301856121ad565b6123ec60608301846121ad565b95945050505050565b6000604051905081810181811067ffffffffffffffff8211171561241857600080fd5b8060405250919050565b600067ffffffffffffffff82111561243957600080fd5b601f19601f8301169050602081019050919050565b600081519050919050565b600081519050919050565b600082825260208201905092915050565b600081905092915050565b600061248b826124b8565b9050919050565b60008115159050919050565b60006dffffffffffffffffffffffffffff82169050919050565b600073ffffffffffffffffffffffffffffffffffffffff82169050919050565b6000819050919050565b600063ffffffff82169050919050565b600060ff82169050919050565b600061250a82612511565b9050919050565b600061251c826124b8565b9050919050565b82818337600083830152505050565b60005b83811015612550578082015181840152602081019050612535565b8381111561255f576000848401525b50505050565b6000601f19601f8301169050919050565b61257f81612480565b811461258a57600080fd5b50565b6125968161249e565b81146125a157600080fd5b50565b6125ad816124d8565b81146125b857600080fd5b50565b6125c4816124e2565b81146125cf57600080fd5b50565b6125db816124f2565b81146125e657600080fd5b5056fea365627a7a72315820f27c4e986419725a8fe15bf1826363b70d9aedc3cbf103a282b6060df5f2fe4b6c6578706572696d656e74616cf564736f6c634300050b0040", "deployedBytecode": "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", "devdoc": {"methods": {"claimGovernance()": {"details": "Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor."}, "governor()": {"details": "Returns the address of the current Governor."}, "isGovernor()": {"details": "Returns true if the caller is the current Governor."}, "transferGovernance(address)": {"details": "Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete", "params": {"_newGovernor": "Address of the new Governor"}}}}, "userdoc": {"methods": {}}, "gasEstimates": {"creation": {"codeDepositCost": "1954400", "executionCost": "infinite", "totalCost": "infinite"}, "external": {"PERIOD()": "396", "claimGovernance()": "22374", "debugPrice(string)": "infinite", "ethPriceOracle()": "702", "ethUsdPrice()": "infinite", "getSwapConfig(string)": "infinite", "governor()": "664", "isGovernor()": "703", "openPrice(string)": "infinite", "price(string)": "infinite", "registerEthPriceOracle(address)": "21090", "registerPair(address)": "infinite", "tokEthPrice(string)": "infinite", "tokUsdPrice(string)": "infinite", "transferGovernance(address)": "infinite", "updatePriceWindows(bytes32[])": "infinite"}, "internal": {"currentCumulativePrice(struct OpenUniswapOracle.SwapConfig storage pointer)": "infinite", "mul(uint256,uint256)": "infinite", "pokePriceWindow(struct OpenUniswapOracle.SwapConfig storage pointer)": "infinite"}}}