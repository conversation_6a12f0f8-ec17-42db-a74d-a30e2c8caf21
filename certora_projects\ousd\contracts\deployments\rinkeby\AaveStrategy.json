{"address": "0xD5dEd719edDa89083fA12BF6e0d0B42FD3ce0B6A", "abi": [{"constant": false, "inputs": [], "name": "collectRewardToken", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "governor", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_asset", "type": "address"}, {"internalType": "address", "name": "_pToken", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>dd<PERSON>", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "assetToPToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_asset", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "transferToken", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "rewardT<PERSON><PERSON><PERSON>ress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_asset", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "deposit", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "rewardLiquidationThreshold", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "claimGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "_asset", "type": "address"}], "name": "checkBalance", "outputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_platformAddress", "type": "address"}, {"internalType": "address", "name": "_vault<PERSON><PERSON>ress", "type": "address"}, {"internalType": "address", "name": "_rewardT<PERSON><PERSON><PERSON>ress", "type": "address"}, {"internalType": "address[]", "name": "_assets", "type": "address[]"}, {"internalType": "address[]", "name": "_pTokens", "type": "address[]"}], "name": "initialize", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [], "name": "withdrawAll", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "_assetIndex", "type": "uint256"}], "name": "removePToken", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_rewardT<PERSON><PERSON><PERSON>ress", "type": "address"}], "name": "setRewardTokenAddress", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "_asset", "type": "address"}], "name": "supportsAsset", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "safeApproveAllTokens", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "isGovernor", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "_threshold", "type": "uint256"}], "name": "setRewardLiquidationThreshold", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_newGovernor", "type": "address"}], "name": "transferGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_recipient", "type": "address"}, {"internalType": "address", "name": "_asset", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "withdraw", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "platformAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_asset", "type": "address"}, {"indexed": false, "internalType": "address", "name": "_pToken", "type": "address"}], "name": "PTokenAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_asset", "type": "address"}, {"indexed": false, "internalType": "address", "name": "_pToken", "type": "address"}], "name": "PTokenRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_asset", "type": "address"}, {"indexed": false, "internalType": "address", "name": "_pToken", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_asset", "type": "address"}, {"indexed": false, "internalType": "address", "name": "_pToken", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "RewardTokenCollected", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "PendingGovernorshipTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "GovernorshipTransferred", "type": "event"}], "transactionHash": "0xbe9852adbd49e4c706259bdc969e07f9c9893723ef44dc13bd6b55bb3a717b39", "receipt": {"to": null, "from": "0xD85A569F3C26f81070544451131c742283360400", "contractAddress": "0xD5dEd719edDa89083fA12BF6e0d0B42FD3ce0B6A", "transactionIndex": 5, "gasUsed": "1811831", "logsBloom": "0x00000000400000000000000000000000000000000000000000000000000000000000000004000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000800000000000000000000000000000000000004000000000000000000000000000004000000000000000000000000000000000400000000000000000000800000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000020000000000000000000000000000000000000000400000000000000000000000000", "blockHash": "0x3d2e8b82c566a47866e946a789409db7f52ef143fa187459572e37ed904222c4", "transactionHash": "0xbe9852adbd49e4c706259bdc969e07f9c9893723ef44dc13bd6b55bb3a717b39", "logs": [{"transactionIndex": 5, "blockNumber": 7803528, "transactionHash": "0xbe9852adbd49e4c706259bdc969e07f9c9893723ef44dc13bd6b55bb3a717b39", "address": "0xD5dEd719edDa89083fA12BF6e0d0B42FD3ce0B6A", "topics": ["0xc7c0c772add429241571afb3805861fb3cfa2af374534088b76cdb4325a87e9a", "0x0000000000000000000000000000000000000000000000000000000000000000", "0x000000000000000000000000d85a569f3c26f81070544451131c742283360400"], "data": "0x", "logIndex": 1, "blockHash": "0x3d2e8b82c566a47866e946a789409db7f52ef143fa187459572e37ed904222c4"}], "blockNumber": 7803528, "cumulativeGasUsed": "2365912", "status": 1, "byzantium": true}, "args": [], "solcInputHash": "d641aaf0ed5d604d73e8f53c6d96978e", "metadata": "{\"compiler\":{\"version\":\"0.5.11+commit.22be8592.mod\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"constant\":false,\"inputs\":[],\"name\":\"collectRewardToken\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"governor\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_pToken\",\"type\":\"address\"}],\"name\":\"setPTokenAddress\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"assetToPToken\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"transferToken\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"rewardTokenAddress\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"vaultAddress\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"deposit\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"rewardLiquidationThreshold\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"claimGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"}],\"name\":\"checkBalance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_platformAddress\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_vaultAddress\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_rewardTokenAddress\",\"type\":\"address\"},{\"internalType\":\"address[]\",\"name\":\"_assets\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"_pTokens\",\"type\":\"address[]\"}],\"name\":\"initialize\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"withdrawAll\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_assetIndex\",\"type\":\"uint256\"}],\"name\":\"removePToken\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_rewardTokenAddress\",\"type\":\"address\"}],\"name\":\"setRewardTokenAddress\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"}],\"name\":\"supportsAsset\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"safeApproveAllTokens\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"isGovernor\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_threshold\",\"type\":\"uint256\"}],\"name\":\"setRewardLiquidationThreshold\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_newGovernor\",\"type\":\"address\"}],\"name\":\"transferGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_recipient\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"withdraw\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"platformAddress\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"_pToken\",\"type\":\"address\"}],\"name\":\"PTokenAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"_pToken\",\"type\":\"address\"}],\"name\":\"PTokenRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"_pToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"Deposit\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"_pToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"Withdrawal\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"RewardTokenCollected\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"PendingGovernorshipTransfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"GovernorshipTransferred\",\"type\":\"event\"}],\"devdoc\":{\"methods\":{\"checkBalance(address)\":{\"details\":\"Get the total asset value held in the platform\",\"params\":{\"_asset\":\"Address of the asset\"},\"return\":\"balance    Total value of the asset in the platform\"},\"claimGovernance()\":{\"details\":\"Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor.\"},\"collectRewardToken()\":{\"details\":\"Collect accumulated reward token (COMP) and send to Vault.\"},\"deposit(address,uint256)\":{\"details\":\"Deposit asset into Aave\",\"params\":{\"_amount\":\"Amount of asset to deposit\",\"_asset\":\"Address of asset to deposit\"},\"return\":\"amountDeposited Amount of asset that was deposited\"},\"governor()\":{\"details\":\"Returns the address of the current Governor.\"},\"initialize(address,address,address,address[],address[])\":{\"details\":\"Internal initialize function, to set up initial internal state\",\"params\":{\"_assets\":\"Addresses of initial supported assets\",\"_pTokens\":\"Platform Token corresponding addresses\",\"_platformAddress\":\"jGeneric platform address\",\"_rewardTokenAddress\":\"Address of reward token for platform\",\"_vaultAddress\":\"Address of the Vault\"}},\"isGovernor()\":{\"details\":\"Returns true if the caller is the current Governor.\"},\"removePToken(uint256)\":{\"details\":\"Remove a supported asset by passing its index.     This method can only be called by the system Governor\",\"params\":{\"_assetIndex\":\"Index of the asset to be removed\"}},\"safeApproveAllTokens()\":{\"details\":\"Approve the spending of all assets by their corresponding aToken,     if for some reason is it necessary.\"},\"setPTokenAddress(address,address)\":{\"details\":\"Provide support for asset by passing its pToken address.     This method can only be called by the system Governor\",\"params\":{\"_asset\":\"Address for the asset\",\"_pToken\":\"Address for the corresponding platform token\"}},\"setRewardLiquidationThreshold(uint256)\":{\"details\":\"Set the reward token liquidation threshold.\",\"params\":{\"_threshold\":\"Threshold amount in decimals of reward token that will cause the Vault to claim and withdrawAll on allocate() calls.\"}},\"setRewardTokenAddress(address)\":{\"details\":\"Set the reward token address.\",\"params\":{\"_rewardTokenAddress\":\"Address of the reward token\"}},\"supportsAsset(address)\":{\"details\":\"Retuns bool indicating whether asset is supported by strategy\",\"params\":{\"_asset\":\"Address of the asset\"}},\"transferGovernance(address)\":{\"details\":\"Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete\",\"params\":{\"_newGovernor\":\"Address of the new Governor\"}},\"transferToken(address,uint256)\":{\"details\":\"Transfer token to governor. Intended for recovering tokens stuck in     strategy contracts, i.e. mistaken sends.\",\"params\":{\"_amount\":\"Amount of the asset to transfer\",\"_asset\":\"Address for the asset\"}},\"withdraw(address,address,uint256)\":{\"details\":\"Withdraw asset from Aave\",\"params\":{\"_amount\":\"Amount of asset to withdraw\",\"_asset\":\"Address of asset to withdraw\",\"_recipient\":\"Address to receive withdrawn asset\"},\"return\":\"amountWithdrawn Amount of asset that was withdrawn\"},\"withdrawAll()\":{\"details\":\"Remove all assets from platform and send them to Vault contract.\"}}},\"userdoc\":{\"methods\":{}}},\"settings\":{\"compilationTarget\":{\"contracts/strategies/AaveStrategy.sol\":\"AaveStrategy\"},\"evmVersion\":\"petersburg\",\"libraries\":{},\"metadata\":{\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts/math/SafeMath.sol\":{\"content\":\"pragma solidity ^0.5.0;\\n\\n/**\\n * @dev Wrappers over Solidity's arithmetic operations with added overflow\\n * checks.\\n *\\n * Arithmetic operations in Solidity wrap on overflow. This can easily result\\n * in bugs, because programmers usually assume that an overflow raises an\\n * error, which is the standard behavior in high level programming languages.\\n * `SafeMath` restores this intuition by reverting the transaction when an\\n * operation overflows.\\n *\\n * Using this library instead of the unchecked operations eliminates an entire\\n * class of bugs, so it's recommended to use it always.\\n */\\nlibrary SafeMath {\\n    /**\\n     * @dev Returns the addition of two unsigned integers, reverting on\\n     * overflow.\\n     *\\n     * Counterpart to Solidity's `+` operator.\\n     *\\n     * Requirements:\\n     * - Addition cannot overflow.\\n     */\\n    function add(uint256 a, uint256 b) internal pure returns (uint256) {\\n        uint256 c = a + b;\\n        require(c >= a, \\\"SafeMath: addition overflow\\\");\\n\\n        return c;\\n    }\\n\\n    /**\\n     * @dev Returns the subtraction of two unsigned integers, reverting on\\n     * overflow (when the result is negative).\\n     *\\n     * Counterpart to Solidity's `-` operator.\\n     *\\n     * Requirements:\\n     * - Subtraction cannot overflow.\\n     */\\n    function sub(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return sub(a, b, \\\"SafeMath: subtraction overflow\\\");\\n    }\\n\\n    /**\\n     * @dev Returns the subtraction of two unsigned integers, reverting with custom message on\\n     * overflow (when the result is negative).\\n     *\\n     * Counterpart to Solidity's `-` operator.\\n     *\\n     * Requirements:\\n     * - Subtraction cannot overflow.\\n     *\\n     * _Available since v2.4.0._\\n     */\\n    function sub(uint256 a, uint256 b, string memory errorMessage) internal pure returns (uint256) {\\n        require(b <= a, errorMessage);\\n        uint256 c = a - b;\\n\\n        return c;\\n    }\\n\\n    /**\\n     * @dev Returns the multiplication of two unsigned integers, reverting on\\n     * overflow.\\n     *\\n     * Counterpart to Solidity's `*` operator.\\n     *\\n     * Requirements:\\n     * - Multiplication cannot overflow.\\n     */\\n    function mul(uint256 a, uint256 b) internal pure returns (uint256) {\\n        // Gas optimization: this is cheaper than requiring 'a' not being zero, but the\\n        // benefit is lost if 'b' is also tested.\\n        // See: https://github.com/OpenZeppelin/openzeppelin-contracts/pull/522\\n        if (a == 0) {\\n            return 0;\\n        }\\n\\n        uint256 c = a * b;\\n        require(c / a == b, \\\"SafeMath: multiplication overflow\\\");\\n\\n        return c;\\n    }\\n\\n    /**\\n     * @dev Returns the integer division of two unsigned integers. Reverts on\\n     * division by zero. The result is rounded towards zero.\\n     *\\n     * Counterpart to Solidity's `/` operator. Note: this function uses a\\n     * `revert` opcode (which leaves remaining gas untouched) while Solidity\\n     * uses an invalid opcode to revert (consuming all remaining gas).\\n     *\\n     * Requirements:\\n     * - The divisor cannot be zero.\\n     */\\n    function div(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return div(a, b, \\\"SafeMath: division by zero\\\");\\n    }\\n\\n    /**\\n     * @dev Returns the integer division of two unsigned integers. Reverts with custom message on\\n     * division by zero. The result is rounded towards zero.\\n     *\\n     * Counterpart to Solidity's `/` operator. Note: this function uses a\\n     * `revert` opcode (which leaves remaining gas untouched) while Solidity\\n     * uses an invalid opcode to revert (consuming all remaining gas).\\n     *\\n     * Requirements:\\n     * - The divisor cannot be zero.\\n     *\\n     * _Available since v2.4.0._\\n     */\\n    function div(uint256 a, uint256 b, string memory errorMessage) internal pure returns (uint256) {\\n        // Solidity only automatically asserts when dividing by 0\\n        require(b > 0, errorMessage);\\n        uint256 c = a / b;\\n        // assert(a == b * c + a % b); // There is no case in which this doesn't hold\\n\\n        return c;\\n    }\\n\\n    /**\\n     * @dev Returns the remainder of dividing two unsigned integers. (unsigned integer modulo),\\n     * Reverts when dividing by zero.\\n     *\\n     * Counterpart to Solidity's `%` operator. This function uses a `revert`\\n     * opcode (which leaves remaining gas untouched) while Solidity uses an\\n     * invalid opcode to revert (consuming all remaining gas).\\n     *\\n     * Requirements:\\n     * - The divisor cannot be zero.\\n     */\\n    function mod(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return mod(a, b, \\\"SafeMath: modulo by zero\\\");\\n    }\\n\\n    /**\\n     * @dev Returns the remainder of dividing two unsigned integers. (unsigned integer modulo),\\n     * Reverts with custom message when dividing by zero.\\n     *\\n     * Counterpart to Solidity's `%` operator. This function uses a `revert`\\n     * opcode (which leaves remaining gas untouched) while Solidity uses an\\n     * invalid opcode to revert (consuming all remaining gas).\\n     *\\n     * Requirements:\\n     * - The divisor cannot be zero.\\n     *\\n     * _Available since v2.4.0._\\n     */\\n    function mod(uint256 a, uint256 b, string memory errorMessage) internal pure returns (uint256) {\\n        require(b != 0, errorMessage);\\n        return a % b;\\n    }\\n}\\n\",\"keccak256\":\"0x640b6dee7a4b830bdfd52b5031a07fc2b12209f5b2e29e5d364a7d37f69d8076\"},\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"content\":\"pragma solidity ^0.5.0;\\n\\n/**\\n * @dev Interface of the ERC20 standard as defined in the EIP. Does not include\\n * the optional functions; to access them see {ERC20Detailed}.\\n */\\ninterface IERC20 {\\n    /**\\n     * @dev Returns the amount of tokens in existence.\\n     */\\n    function totalSupply() external view returns (uint256);\\n\\n    /**\\n     * @dev Returns the amount of tokens owned by `account`.\\n     */\\n    function balanceOf(address account) external view returns (uint256);\\n\\n    /**\\n     * @dev Moves `amount` tokens from the caller's account to `recipient`.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transfer(address recipient, uint256 amount) external returns (bool);\\n\\n    /**\\n     * @dev Returns the remaining number of tokens that `spender` will be\\n     * allowed to spend on behalf of `owner` through {transferFrom}. This is\\n     * zero by default.\\n     *\\n     * This value changes when {approve} or {transferFrom} are called.\\n     */\\n    function allowance(address owner, address spender) external view returns (uint256);\\n\\n    /**\\n     * @dev Sets `amount` as the allowance of `spender` over the caller's tokens.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * IMPORTANT: Beware that changing an allowance with this method brings the risk\\n     * that someone may use both the old and the new allowance by unfortunate\\n     * transaction ordering. One possible solution to mitigate this race\\n     * condition is to first reduce the spender's allowance to 0 and set the\\n     * desired value afterwards:\\n     * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\\n     *\\n     * Emits an {Approval} event.\\n     */\\n    function approve(address spender, uint256 amount) external returns (bool);\\n\\n    /**\\n     * @dev Moves `amount` tokens from `sender` to `recipient` using the\\n     * allowance mechanism. `amount` is then deducted from the caller's\\n     * allowance.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transferFrom(address sender, address recipient, uint256 amount) external returns (bool);\\n\\n    /**\\n     * @dev Emitted when `value` tokens are moved from one account (`from`) to\\n     * another (`to`).\\n     *\\n     * Note that `value` may be zero.\\n     */\\n    event Transfer(address indexed from, address indexed to, uint256 value);\\n\\n    /**\\n     * @dev Emitted when the allowance of a `spender` for an `owner` is set by\\n     * a call to {approve}. `value` is the new allowance.\\n     */\\n    event Approval(address indexed owner, address indexed spender, uint256 value);\\n}\\n\",\"keccak256\":\"0xe5bb0f57cff3e299f360052ba50f1ea0fff046df2be070b6943e0e3c3fdad8a9\"},\"@openzeppelin/contracts/token/ERC20/SafeERC20.sol\":{\"content\":\"pragma solidity ^0.5.0;\\n\\nimport \\\"./IERC20.sol\\\";\\nimport \\\"../../math/SafeMath.sol\\\";\\nimport \\\"../../utils/Address.sol\\\";\\n\\n/**\\n * @title SafeERC20\\n * @dev Wrappers around ERC20 operations that throw on failure (when the token\\n * contract returns false). Tokens that return no value (and instead revert or\\n * throw on failure) are also supported, non-reverting calls are assumed to be\\n * successful.\\n * To use this library you can add a `using SafeERC20 for ERC20;` statement to your contract,\\n * which allows you to call the safe operations as `token.safeTransfer(...)`, etc.\\n */\\nlibrary SafeERC20 {\\n    using SafeMath for uint256;\\n    using Address for address;\\n\\n    function safeTransfer(IERC20 token, address to, uint256 value) internal {\\n        callOptionalReturn(token, abi.encodeWithSelector(token.transfer.selector, to, value));\\n    }\\n\\n    function safeTransferFrom(IERC20 token, address from, address to, uint256 value) internal {\\n        callOptionalReturn(token, abi.encodeWithSelector(token.transferFrom.selector, from, to, value));\\n    }\\n\\n    function safeApprove(IERC20 token, address spender, uint256 value) internal {\\n        // safeApprove should only be called when setting an initial allowance,\\n        // or when resetting it to zero. To increase and decrease it, use\\n        // 'safeIncreaseAllowance' and 'safeDecreaseAllowance'\\n        // solhint-disable-next-line max-line-length\\n        require((value == 0) || (token.allowance(address(this), spender) == 0),\\n            \\\"SafeERC20: approve from non-zero to non-zero allowance\\\"\\n        );\\n        callOptionalReturn(token, abi.encodeWithSelector(token.approve.selector, spender, value));\\n    }\\n\\n    function safeIncreaseAllowance(IERC20 token, address spender, uint256 value) internal {\\n        uint256 newAllowance = token.allowance(address(this), spender).add(value);\\n        callOptionalReturn(token, abi.encodeWithSelector(token.approve.selector, spender, newAllowance));\\n    }\\n\\n    function safeDecreaseAllowance(IERC20 token, address spender, uint256 value) internal {\\n        uint256 newAllowance = token.allowance(address(this), spender).sub(value, \\\"SafeERC20: decreased allowance below zero\\\");\\n        callOptionalReturn(token, abi.encodeWithSelector(token.approve.selector, spender, newAllowance));\\n    }\\n\\n    /**\\n     * @dev Imitates a Solidity high-level call (i.e. a regular function call to a contract), relaxing the requirement\\n     * on the return value: the return value is optional (but if data is returned, it must not be false).\\n     * @param token The token targeted by the call.\\n     * @param data The call data (encoded using abi.encode or one of its variants).\\n     */\\n    function callOptionalReturn(IERC20 token, bytes memory data) private {\\n        // We need to perform a low level call here, to bypass Solidity's return data size checking mechanism, since\\n        // we're implementing it ourselves.\\n\\n        // A Solidity high level call has three parts:\\n        //  1. The target address is checked to verify it contains contract code\\n        //  2. The call itself is made, and success asserted\\n        //  3. The return value is decoded, which in turn checks the size of the returned data.\\n        // solhint-disable-next-line max-line-length\\n        require(address(token).isContract(), \\\"SafeERC20: call to non-contract\\\");\\n\\n        // solhint-disable-next-line avoid-low-level-calls\\n        (bool success, bytes memory returndata) = address(token).call(data);\\n        require(success, \\\"SafeERC20: low-level call failed\\\");\\n\\n        if (returndata.length > 0) { // Return data is optional\\n            // solhint-disable-next-line max-line-length\\n            require(abi.decode(returndata, (bool)), \\\"SafeERC20: ERC20 operation did not succeed\\\");\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0x6f2c9955d65c522b80f4b8792f076512d2df947d2112cbc4d98a4781ed42ede2\"},\"@openzeppelin/contracts/utils/Address.sol\":{\"content\":\"pragma solidity ^0.5.5;\\n\\n/**\\n * @dev Collection of functions related to the address type\\n */\\nlibrary Address {\\n    /**\\n     * @dev Returns true if `account` is a contract.\\n     *\\n     * [IMPORTANT]\\n     * ====\\n     * It is unsafe to assume that an address for which this function returns\\n     * false is an externally-owned account (EOA) and not a contract.\\n     *\\n     * Among others, `isContract` will return false for the following \\n     * types of addresses:\\n     *\\n     *  - an externally-owned account\\n     *  - a contract in construction\\n     *  - an address where a contract will be created\\n     *  - an address where a contract lived, but was destroyed\\n     * ====\\n     */\\n    function isContract(address account) internal view returns (bool) {\\n        // According to EIP-1052, 0x0 is the value returned for not-yet created accounts\\n        // and 0xc5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470 is returned\\n        // for accounts without code, i.e. `keccak256('')`\\n        bytes32 codehash;\\n        bytes32 accountHash = 0xc5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470;\\n        // solhint-disable-next-line no-inline-assembly\\n        assembly { codehash := extcodehash(account) }\\n        return (codehash != accountHash && codehash != 0x0);\\n    }\\n\\n    /**\\n     * @dev Converts an `address` into `address payable`. Note that this is\\n     * simply a type cast: the actual underlying value is not changed.\\n     *\\n     * _Available since v2.4.0._\\n     */\\n    function toPayable(address account) internal pure returns (address payable) {\\n        return address(uint160(account));\\n    }\\n\\n    /**\\n     * @dev Replacement for Solidity's `transfer`: sends `amount` wei to\\n     * `recipient`, forwarding all available gas and reverting on errors.\\n     *\\n     * https://eips.ethereum.org/EIPS/eip-1884[EIP1884] increases the gas cost\\n     * of certain opcodes, possibly making contracts go over the 2300 gas limit\\n     * imposed by `transfer`, making them unable to receive funds via\\n     * `transfer`. {sendValue} removes this limitation.\\n     *\\n     * https://diligence.consensys.net/posts/2019/09/stop-using-soliditys-transfer-now/[Learn more].\\n     *\\n     * IMPORTANT: because control is transferred to `recipient`, care must be\\n     * taken to not create reentrancy vulnerabilities. Consider using\\n     * {ReentrancyGuard} or the\\n     * https://solidity.readthedocs.io/en/v0.5.11/security-considerations.html#use-the-checks-effects-interactions-pattern[checks-effects-interactions pattern].\\n     *\\n     * _Available since v2.4.0._\\n     */\\n    function sendValue(address payable recipient, uint256 amount) internal {\\n        require(address(this).balance >= amount, \\\"Address: insufficient balance\\\");\\n\\n        // solhint-disable-next-line avoid-call-value\\n        (bool success, ) = recipient.call.value(amount)(\\\"\\\");\\n        require(success, \\\"Address: unable to send value, recipient may have reverted\\\");\\n    }\\n}\\n\",\"keccak256\":\"0x1a8e5072509c5ea7365eb1d48030b9be865140c8fb779968da0a459a0e174a11\"},\"@openzeppelin/upgrades/contracts/Initializable.sol\":{\"content\":\"pragma solidity >=0.4.24 <0.7.0;\\n\\n\\n/**\\n * @title Initializable\\n *\\n * @dev Helper contract to support initializer functions. To use it, replace\\n * the constructor with a function that has the `initializer` modifier.\\n * WARNING: Unlike constructors, initializer functions must be manually\\n * invoked. This applies both to deploying an Initializable contract, as well\\n * as extending an Initializable contract via inheritance.\\n * WARNING: When used with inheritance, manual care must be taken to not invoke\\n * a parent initializer twice, or ensure that all initializers are idempotent,\\n * because this is not dealt with automatically as with constructors.\\n */\\ncontract Initializable {\\n\\n  /**\\n   * @dev Indicates that the contract has been initialized.\\n   */\\n  bool private initialized;\\n\\n  /**\\n   * @dev Indicates that the contract is in the process of being initialized.\\n   */\\n  bool private initializing;\\n\\n  /**\\n   * @dev Modifier to use in the initializer function of a contract.\\n   */\\n  modifier initializer() {\\n    require(initializing || isConstructor() || !initialized, \\\"Contract instance has already been initialized\\\");\\n\\n    bool isTopLevelCall = !initializing;\\n    if (isTopLevelCall) {\\n      initializing = true;\\n      initialized = true;\\n    }\\n\\n    _;\\n\\n    if (isTopLevelCall) {\\n      initializing = false;\\n    }\\n  }\\n\\n  /// @dev Returns true if and only if the function is running in the constructor\\n  function isConstructor() private view returns (bool) {\\n    // extcodesize checks the size of the code stored in an address, and\\n    // address returns the current address. Since the code is still not\\n    // deployed when running a constructor, any checks on its code size will\\n    // yield zero, making it an effective way to detect if a contract is\\n    // under construction or not.\\n    address self = address(this);\\n    uint256 cs;\\n    assembly { cs := extcodesize(self) }\\n    return cs == 0;\\n  }\\n\\n  // Reserved storage space to allow for layout changes in the future.\\n  uint256[50] private ______gap;\\n}\\n\",\"keccak256\":\"0x9bfec92e36234ecc99b5d37230acb6cd1f99560233753162204104a4897e8721\"},\"contracts/governance/Governable.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\n/**\\n * @title OUSD Governable Contract\\n * @dev Copy of the openzeppelin Ownable.sol contract with nomenclature change\\n *      from owner to governor and renounce methods removed. Does not use\\n *      Context.sol like Ownable.sol does for simplification.\\n * <AUTHOR> Protocol Inc\\n */\\ncontract Governable {\\n    // Storage position of the owner and pendingOwner of the contract\\n    // keccak256(\\\"OUSD.governor\\\");\\n    bytes32\\n        private constant governorPosition = 0x7bea13895fa79d2831e0a9e28edede30099005a50d652d8957cf8a607ee6ca4a;\\n\\n    // keccak256(\\\"OUSD.pending.governor\\\");\\n    bytes32\\n        private constant pendingGovernorPosition = 0x44c4d30b2eaad5130ad70c3ba6972730566f3e6359ab83e800d905c61b1c51db;\\n\\n    // keccak256(\\\"OUSD.reentry.status\\\");\\n    bytes32\\n        private constant reentryStatusPosition = 0x53bf423e48ed90e97d02ab0ebab13b2a235a6bfbe9c321847d5c175333ac4535;\\n\\n    // See OpenZeppelin ReentrancyGuard implementation\\n    uint256 constant _NOT_ENTERED = 1;\\n    uint256 constant _ENTERED = 2;\\n\\n    event PendingGovernorshipTransfer(\\n        address indexed previousGovernor,\\n        address indexed newGovernor\\n    );\\n\\n    event GovernorshipTransferred(\\n        address indexed previousGovernor,\\n        address indexed newGovernor\\n    );\\n\\n    /**\\n     * @dev Initializes the contract setting the deployer as the initial Governor.\\n     */\\n    constructor() internal {\\n        _setGovernor(msg.sender);\\n        emit GovernorshipTransferred(address(0), _governor());\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current Governor.\\n     */\\n    function governor() public view returns (address) {\\n        return _governor();\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current Governor.\\n     */\\n    function _governor() internal view returns (address governorOut) {\\n        bytes32 position = governorPosition;\\n        assembly {\\n            governorOut := sload(position)\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the address of the pending Governor.\\n     */\\n    function _pendingGovernor()\\n        internal\\n        view\\n        returns (address pendingGovernor)\\n    {\\n        bytes32 position = pendingGovernorPosition;\\n        assembly {\\n            pendingGovernor := sload(position)\\n        }\\n    }\\n\\n    /**\\n     * @dev Throws if called by any account other than the Governor.\\n     */\\n    modifier onlyGovernor() {\\n        require(isGovernor(), \\\"Caller is not the Governor\\\");\\n        _;\\n    }\\n\\n    /**\\n     * @dev Returns true if the caller is the current Governor.\\n     */\\n    function isGovernor() public view returns (bool) {\\n        return msg.sender == _governor();\\n    }\\n\\n    function _setGovernor(address newGovernor) internal {\\n        bytes32 position = governorPosition;\\n        assembly {\\n            sstore(position, newGovernor)\\n        }\\n    }\\n\\n    /**\\n     * @dev Prevents a contract from calling itself, directly or indirectly.\\n     * Calling a `nonReentrant` function from another `nonReentrant`\\n     * function is not supported. It is possible to prevent this from happening\\n     * by making the `nonReentrant` function external, and make it call a\\n     * `private` function that does the actual work.\\n     */\\n    modifier nonReentrant() {\\n        bytes32 position = reentryStatusPosition;\\n        uint256 _reentry_status;\\n        assembly {\\n            _reentry_status := sload(position)\\n        }\\n\\n        // On the first call to nonReentrant, _notEntered will be true\\n        require(_reentry_status != _ENTERED, \\\"Reentrant call\\\");\\n\\n        // Any calls to nonReentrant after this point will fail\\n        assembly {\\n            sstore(position, _ENTERED)\\n        }\\n\\n        _;\\n\\n        // By storing the original value once again, a refund is triggered (see\\n        // https://eips.ethereum.org/EIPS/eip-2200)\\n        assembly {\\n            sstore(position, _NOT_ENTERED)\\n        }\\n    }\\n\\n    function _setPendingGovernor(address newGovernor) internal {\\n        bytes32 position = pendingGovernorPosition;\\n        assembly {\\n            sstore(position, newGovernor)\\n        }\\n    }\\n\\n    /**\\n     * @dev Transfers Governance of the contract to a new account (`newGovernor`).\\n     * Can only be called by the current Governor. Must be claimed for this to complete\\n     * @param _newGovernor Address of the new Governor\\n     */\\n    function transferGovernance(address _newGovernor) external onlyGovernor {\\n        _setPendingGovernor(_newGovernor);\\n        emit PendingGovernorshipTransfer(_governor(), _newGovernor);\\n    }\\n\\n    /**\\n     * @dev Claim Governance of the contract to a new account (`newGovernor`).\\n     * Can only be called by the new Governor.\\n     */\\n    function claimGovernance() external {\\n        require(\\n            msg.sender == _pendingGovernor(),\\n            \\\"Only the pending Governor can complete the claim\\\"\\n        );\\n        _changeGovernor(msg.sender);\\n    }\\n\\n    /**\\n     * @dev Change Governance of the contract to a new account (`newGovernor`).\\n     * @param _newGovernor Address of the new Governor\\n     */\\n    function _changeGovernor(address _newGovernor) internal {\\n        require(_newGovernor != address(0), \\\"New Governor is address(0)\\\");\\n        emit GovernorshipTransferred(_governor(), _newGovernor);\\n        _setGovernor(_newGovernor);\\n    }\\n}\\n\",\"keccak256\":\"0x3e51ea48102945bf4b305bf9722a07514a585a29555d92f8c84352d1a4cfcee1\"},\"contracts/strategies/AaveStrategy.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\n/**\\n * @title OUSD Aave Strategy\\n * @notice Investment strategy for investing stablecoins via Aave\\n * <AUTHOR> Protocol Inc\\n */\\nimport \\\"./IAave.sol\\\";\\nimport {\\n    IERC20,\\n    InitializableAbstractStrategy\\n} from \\\"../utils/InitializableAbstractStrategy.sol\\\";\\n\\ncontract AaveStrategy is InitializableAbstractStrategy {\\n    uint16 constant referralCode = 92;\\n\\n    /**\\n     * @dev Deposit asset into Aave\\n     * @param _asset Address of asset to deposit\\n     * @param _amount Amount of asset to deposit\\n     * @return amountDeposited Amount of asset that was deposited\\n     */\\n    function deposit(address _asset, uint256 _amount)\\n        external\\n        onlyVault\\n        nonReentrant\\n    {\\n        require(_amount > 0, \\\"Must deposit something\\\");\\n\\n        IAaveAToken aToken = _getATokenFor(_asset);\\n        emit Deposit(_asset, address(aToken), _amount);\\n        _getLendingPool().deposit(_asset, _amount, referralCode);\\n    }\\n\\n    /**\\n     * @dev Withdraw asset from Aave\\n     * @param _recipient Address to receive withdrawn asset\\n     * @param _asset Address of asset to withdraw\\n     * @param _amount Amount of asset to withdraw\\n     * @return amountWithdrawn Amount of asset that was withdrawn\\n     */\\n    function withdraw(\\n        address _recipient,\\n        address _asset,\\n        uint256 _amount\\n    ) external onlyVault nonReentrant {\\n        require(_amount > 0, \\\"Must withdraw something\\\");\\n        require(_recipient != address(0), \\\"Must specify recipient\\\");\\n\\n        IAaveAToken aToken = _getATokenFor(_asset);\\n        emit Withdrawal(_asset, address(aToken), _amount);\\n        aToken.redeem(_amount);\\n        IERC20(_asset).safeTransfer(_recipient, _amount);\\n    }\\n\\n    /**\\n     * @dev Remove all assets from platform and send them to Vault contract.\\n     */\\n    function withdrawAll() external onlyVaultOrGovernor nonReentrant {\\n        for (uint256 i = 0; i < assetsMapped.length; i++) {\\n            // Redeem entire balance of aToken\\n            IAaveAToken aToken = _getATokenFor(assetsMapped[i]);\\n            uint256 balance = aToken.balanceOf(address(this));\\n            if (balance > 0) {\\n                aToken.redeem(balance);\\n                // Transfer entire balance to Vault\\n                IERC20 asset = IERC20(assetsMapped[i]);\\n                asset.safeTransfer(\\n                    vaultAddress,\\n                    asset.balanceOf(address(this))\\n                );\\n            }\\n        }\\n    }\\n\\n    /**\\n     * @dev Get the total asset value held in the platform\\n     * @param _asset      Address of the asset\\n     * @return balance    Total value of the asset in the platform\\n     */\\n    function checkBalance(address _asset)\\n        external\\n        view\\n        returns (uint256 balance)\\n    {\\n        // Balance is always with token aToken decimals\\n        IAaveAToken aToken = _getATokenFor(_asset);\\n        balance = aToken.balanceOf(address(this));\\n    }\\n\\n    /**\\n     * @dev Retuns bool indicating whether asset is supported by strategy\\n     * @param _asset Address of the asset\\n     */\\n    function supportsAsset(address _asset) external view returns (bool) {\\n        return assetToPToken[_asset] != address(0);\\n    }\\n\\n    /**\\n     * @dev Approve the spending of all assets by their corresponding aToken,\\n     *      if for some reason is it necessary.\\n     */\\n    function safeApproveAllTokens() external onlyGovernor nonReentrant {\\n        uint256 assetCount = assetsMapped.length;\\n        address lendingPoolVault = _getLendingPoolCore();\\n        // approve the pool to spend the bAsset\\n        for (uint256 i = 0; i < assetCount; i++) {\\n            address asset = assetsMapped[i];\\n            // Safe approval\\n            IERC20(asset).safeApprove(lendingPoolVault, 0);\\n            IERC20(asset).safeApprove(lendingPoolVault, uint256(-1));\\n        }\\n    }\\n\\n    /**\\n     * @dev Internal method to respond to the addition of new asset / aTokens\\n     *      We need to approve the aToken and give it permission to spend the asset\\n     * @param _asset Address of the asset to approve\\n     * @param _aToken This aToken has the approval approval\\n     */\\n    function _abstractSetPToken(address _asset, address _aToken) internal {\\n        address lendingPoolVault = _getLendingPoolCore();\\n        IERC20(_asset).safeApprove(lendingPoolVault, 0);\\n        IERC20(_asset).safeApprove(lendingPoolVault, uint256(-1));\\n    }\\n\\n    /**\\n     * @dev Get the aToken wrapped in the ICERC20 interface for this asset.\\n     *      Fails if the pToken doesn't exist in our mappings.\\n     * @param _asset Address of the asset\\n     * @return Corresponding aToken to this asset\\n     */\\n    function _getATokenFor(address _asset) internal view returns (IAaveAToken) {\\n        address aToken = assetToPToken[_asset];\\n        require(aToken != address(0), \\\"aToken does not exist\\\");\\n        return IAaveAToken(aToken);\\n    }\\n\\n    /**\\n     * @dev Get the current address of the Aave lending pool, which is the gateway to\\n     *      depositing.\\n     * @return Current lending pool implementation\\n     */\\n    function _getLendingPool() internal view returns (IAaveLendingPool) {\\n        address lendingPool = ILendingPoolAddressesProvider(platformAddress)\\n            .getLendingPool();\\n        require(lendingPool != address(0), \\\"Lending pool does not exist\\\");\\n        return IAaveLendingPool(lendingPool);\\n    }\\n\\n    /**\\n     * @dev Get the current address of the Aave lending pool core, which stores all the\\n     *      reserve tokens in its vault.\\n     * @return Current lending pool core address\\n     */\\n    function _getLendingPoolCore() internal view returns (address payable) {\\n        address payable lendingPoolCore = ILendingPoolAddressesProvider(\\n            platformAddress\\n        )\\n            .getLendingPoolCore();\\n        require(\\n            lendingPoolCore != address(uint160(address(0))),\\n            \\\"Lending pool core does not exist\\\"\\n        );\\n        return lendingPoolCore;\\n    }\\n}\\n\",\"keccak256\":\"0x4832f3fdcbc91909a0f3728931ce6a75d3068a2027b2bd74a8f750f9f35363df\"},\"contracts/strategies/IAave.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\n/**\\n * @dev Interface for Aaves A Token\\n * Documentation: https://developers.aave.com/#atokens\\n */\\ninterface IAaveAToken {\\n    /**\\n     * @notice Non-standard ERC20 function to redeem an _amount of aTokens for the underlying\\n     * asset, burning the aTokens during the process.\\n     * @param _amount Amount of aTokens\\n     */\\n    function redeem(uint256 _amount) external;\\n\\n    /**\\n     * @notice returns the current total aToken balance of _user all interest collected included.\\n     * To obtain the user asset principal balance with interests excluded , ERC20 non-standard\\n     * method principalBalanceOf() can be used.\\n     */\\n    function balanceOf(address _user) external view returns (uint256);\\n}\\n\\n/**\\n * @dev Interface for Aaves Lending Pool\\n * Documentation: https://developers.aave.com/#lendingpool\\n */\\ninterface IAaveLendingPool {\\n    /**\\n     * @notice Deposits a certain _amount of an asset specified by the _reserve parameter.\\n     * @dev The caller receives a certain amount of corresponding aTokens in exchange.\\n     * The amount of aTokens received depends on the corresponding aToken exchange rate.\\n     * LendingPoolCore must be approved to spend this reserve\\n     */\\n    function deposit(\\n        address _reserve,\\n        uint256 _amount,\\n        uint16 _referralCode\\n    ) external;\\n}\\n\\n/**\\n * @dev Interface for Aaves Lending Pool\\n * Documentation: https://developers.aave.com/#lendingpooladdressesprovider\\n */\\ninterface ILendingPoolAddressesProvider {\\n    /**\\n     * @notice Get the current address for Aave LendingPool\\n     * @dev Lending pool is the core contract on which to call deposit\\n     */\\n    function getLendingPool() external view returns (address);\\n\\n    /**\\n     * @notice Get the address for lendingPoolCore\\n     * @dev IMPORTANT - this is where _reserve must be approved before deposit\\n     */\\n    function getLendingPoolCore() external view returns (address payable);\\n}\\n\",\"keccak256\":\"0x1c8a38b23b72f6dad0b8eafb18ae847f6873c6c464cd16a1a12c8fe5703aed57\"},\"contracts/utils/InitializableAbstractStrategy.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\nimport {\\n    Initializable\\n} from \\\"@openzeppelin/upgrades/contracts/Initializable.sol\\\";\\nimport { IERC20 } from \\\"@openzeppelin/contracts/token/ERC20/IERC20.sol\\\";\\nimport { SafeERC20 } from \\\"@openzeppelin/contracts/token/ERC20/SafeERC20.sol\\\";\\nimport { SafeMath } from \\\"@openzeppelin/contracts/math/SafeMath.sol\\\";\\n\\nimport { Governable } from \\\"../governance/Governable.sol\\\";\\n\\ncontract InitializableAbstractStrategy is Initializable, Governable {\\n    using SafeERC20 for IERC20;\\n    using SafeMath for uint256;\\n\\n    event PTokenAdded(address indexed _asset, address _pToken);\\n    event PTokenRemoved(address indexed _asset, address _pToken);\\n    event Deposit(address indexed _asset, address _pToken, uint256 _amount);\\n    event Withdrawal(address indexed _asset, address _pToken, uint256 _amount);\\n    event RewardTokenCollected(address recipient, uint256 amount);\\n\\n    // Core address for the given platform\\n    address public platformAddress;\\n\\n    address public vaultAddress;\\n\\n    // asset => pToken (Platform Specific Token Address)\\n    mapping(address => address) public assetToPToken;\\n\\n    // Full list of all assets supported here\\n    address[] internal assetsMapped;\\n\\n    // Reward token address\\n    address public rewardTokenAddress;\\n    uint256 public rewardLiquidationThreshold;\\n\\n    /**\\n     * @dev Internal initialize function, to set up initial internal state\\n     * @param _platformAddress jGeneric platform address\\n     * @param _vaultAddress Address of the Vault\\n     * @param _rewardTokenAddress Address of reward token for platform\\n     * @param _assets Addresses of initial supported assets\\n     * @param _pTokens Platform Token corresponding addresses\\n     */\\n    function initialize(\\n        address _platformAddress,\\n        address _vaultAddress,\\n        address _rewardTokenAddress,\\n        address[] calldata _assets,\\n        address[] calldata _pTokens\\n    ) external onlyGovernor initializer {\\n        InitializableAbstractStrategy._initialize(\\n            _platformAddress,\\n            _vaultAddress,\\n            _rewardTokenAddress,\\n            _assets,\\n            _pTokens\\n        );\\n    }\\n\\n    /**\\n     * @dev Collect accumulated reward token (COMP) and send to Vault.\\n     */\\n    function collectRewardToken() external onlyVault nonReentrant {\\n        IERC20 rewardToken = IERC20(rewardTokenAddress);\\n        uint256 balance = rewardToken.balanceOf(address(this));\\n        emit RewardTokenCollected(vaultAddress, balance);\\n        rewardToken.safeTransfer(vaultAddress, balance);\\n    }\\n\\n    function _initialize(\\n        address _platformAddress,\\n        address _vaultAddress,\\n        address _rewardTokenAddress,\\n        address[] memory _assets,\\n        address[] memory _pTokens\\n    ) internal {\\n        platformAddress = _platformAddress;\\n        vaultAddress = _vaultAddress;\\n        rewardTokenAddress = _rewardTokenAddress;\\n        uint256 assetCount = _assets.length;\\n        require(assetCount == _pTokens.length, \\\"Invalid input arrays\\\");\\n        for (uint256 i = 0; i < assetCount; i++) {\\n            _setPTokenAddress(_assets[i], _pTokens[i]);\\n        }\\n    }\\n\\n    /**\\n     * @dev Single asset variant of the internal initialize.\\n     */\\n    function _initialize(\\n        address _platformAddress,\\n        address _vaultAddress,\\n        address _rewardTokenAddress,\\n        address _asset,\\n        address _pToken\\n    ) internal {\\n        platformAddress = _platformAddress;\\n        vaultAddress = _vaultAddress;\\n        rewardTokenAddress = _rewardTokenAddress;\\n        _setPTokenAddress(_asset, _pToken);\\n    }\\n\\n    /**\\n     * @dev Verifies that the caller is the Vault.\\n     */\\n    modifier onlyVault() {\\n        require(msg.sender == vaultAddress, \\\"Caller is not the Vault\\\");\\n        _;\\n    }\\n\\n    /**\\n     * @dev Verifies that the caller is the Vault or Governor.\\n     */\\n    modifier onlyVaultOrGovernor() {\\n        require(\\n            msg.sender == vaultAddress || msg.sender == governor(),\\n            \\\"Caller is not the Vault or Governor\\\"\\n        );\\n        _;\\n    }\\n\\n    /**\\n     * @dev Set the reward token address.\\n     * @param _rewardTokenAddress Address of the reward token\\n     */\\n    function setRewardTokenAddress(address _rewardTokenAddress)\\n        external\\n        onlyGovernor\\n    {\\n        rewardTokenAddress = _rewardTokenAddress;\\n    }\\n\\n    /**\\n     * @dev Set the reward token liquidation threshold.\\n     * @param _threshold Threshold amount in decimals of reward token that will\\n     * cause the Vault to claim and withdrawAll on allocate() calls.\\n     */\\n    function setRewardLiquidationThreshold(uint256 _threshold)\\n        external\\n        onlyGovernor\\n    {\\n        rewardLiquidationThreshold = _threshold;\\n    }\\n\\n    /**\\n     * @dev Provide support for asset by passing its pToken address.\\n     *      This method can only be called by the system Governor\\n     * @param _asset    Address for the asset\\n     * @param _pToken   Address for the corresponding platform token\\n     */\\n    function setPTokenAddress(address _asset, address _pToken)\\n        external\\n        onlyGovernor\\n    {\\n        _setPTokenAddress(_asset, _pToken);\\n    }\\n\\n    /**\\n     * @dev Remove a supported asset by passing its index.\\n     *      This method can only be called by the system Governor\\n     * @param _assetIndex Index of the asset to be removed\\n     */\\n    function removePToken(uint256 _assetIndex) external onlyGovernor {\\n        require(_assetIndex < assetsMapped.length, \\\"Invalid index\\\");\\n        address asset = assetsMapped[_assetIndex];\\n        address pToken = assetToPToken[asset];\\n\\n        if (_assetIndex < assetsMapped.length - 1) {\\n            assetsMapped[_assetIndex] = assetsMapped[assetsMapped.length - 1];\\n        }\\n        assetsMapped.pop();\\n        assetToPToken[asset] = address(0);\\n\\n        emit PTokenRemoved(asset, pToken);\\n    }\\n\\n    /**\\n     * @dev Provide support for asset by passing its pToken address.\\n     *      Add to internal mappings and execute the platform specific,\\n     * abstract method `_abstractSetPToken`\\n     * @param _asset    Address for the asset\\n     * @param _pToken   Address for the corresponding platform token\\n     */\\n    function _setPTokenAddress(address _asset, address _pToken) internal {\\n        require(assetToPToken[_asset] == address(0), \\\"pToken already set\\\");\\n        require(\\n            _asset != address(0) && _pToken != address(0),\\n            \\\"Invalid addresses\\\"\\n        );\\n\\n        assetToPToken[_asset] = _pToken;\\n        assetsMapped.push(_asset);\\n\\n        emit PTokenAdded(_asset, _pToken);\\n\\n        _abstractSetPToken(_asset, _pToken);\\n    }\\n\\n    /**\\n     * @dev Transfer token to governor. Intended for recovering tokens stuck in\\n     *      strategy contracts, i.e. mistaken sends.\\n     * @param _asset Address for the asset\\n     * @param _amount Amount of the asset to transfer\\n     */\\n    function transferToken(address _asset, uint256 _amount)\\n        public\\n        onlyGovernor\\n    {\\n        IERC20(_asset).safeTransfer(governor(), _amount);\\n    }\\n\\n    /***************************************\\n                 Abstract\\n    ****************************************/\\n\\n    function _abstractSetPToken(address _asset, address _pToken) internal;\\n\\n    function safeApproveAllTokens() external;\\n\\n    /**\\n     * @dev Deposit a amount of asset into the platform\\n     * @param _asset               Address for the asset\\n     * @param _amount              Units of asset to deposit\\n     */\\n    function deposit(address _asset, uint256 _amount) external;\\n\\n    /**\\n     * @dev Withdraw an amount of asset from the platform.\\n     * @param _recipient         Address to which the asset should be sent\\n     * @param _asset             Address of the asset\\n     * @param _amount            Units of asset to withdraw\\n     */\\n    function withdraw(\\n        address _recipient,\\n        address _asset,\\n        uint256 _amount\\n    ) external;\\n\\n    /**\\n     * @dev Withdraw all assets from strategy sending assets to Vault.\\n     */\\n    function withdrawAll() external;\\n\\n    /**\\n     * @dev Get the total asset value held in the platform.\\n     *      This includes any interest that was generated since depositing.\\n     * @param _asset      Address of the asset\\n     * @return balance    Total value of the asset in the platform\\n     */\\n    function checkBalance(address _asset)\\n        external\\n        view\\n        returns (uint256 balance);\\n\\n    /**\\n     * @dev Check if an asset is supported.\\n     * @param _asset    Address of the asset\\n     * @return bool     Whether asset is supported\\n     */\\n    function supportsAsset(address _asset) external view returns (bool);\\n}\\n\",\"keccak256\":\"0xcc2a446777edd6ee5fcba89896640b34f7e1017fa5ace8883b8a92f6e97d7e43\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"methods": {"checkBalance(address)": {"details": "Get the total asset value held in the platform", "params": {"_asset": "Address of the asset"}, "return": "balance    Total value of the asset in the platform"}, "claimGovernance()": {"details": "Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor."}, "collectRewardToken()": {"details": "Collect accumulated reward token (COMP) and send to Vault."}, "deposit(address,uint256)": {"details": "Deposit asset into Aave", "params": {"_amount": "Amount of asset to deposit", "_asset": "Address of asset to deposit"}, "return": "amountDeposited Amount of asset that was deposited"}, "governor()": {"details": "Returns the address of the current Governor."}, "initialize(address,address,address,address[],address[])": {"details": "Internal initialize function, to set up initial internal state", "params": {"_assets": "Addresses of initial supported assets", "_pTokens": "Platform Token corresponding addresses", "_platformAddress": "jGeneric platform address", "_rewardTokenAddress": "Address of reward token for platform", "_vaultAddress": "Address of the Vault"}}, "isGovernor()": {"details": "Returns true if the caller is the current Governor."}, "removePToken(uint256)": {"details": "Remove a supported asset by passing its index.     This method can only be called by the system Governor", "params": {"_assetIndex": "Index of the asset to be removed"}}, "safeApproveAllTokens()": {"details": "Approve the spending of all assets by their corresponding aToken,     if for some reason is it necessary."}, "setPTokenAddress(address,address)": {"details": "Provide support for asset by passing its pToken address.     This method can only be called by the system Governor", "params": {"_asset": "Address for the asset", "_pToken": "Address for the corresponding platform token"}}, "setRewardLiquidationThreshold(uint256)": {"details": "Set the reward token liquidation threshold.", "params": {"_threshold": "Threshold amount in decimals of reward token that will cause the Vault to claim and withdrawAll on allocate() calls."}}, "setRewardTokenAddress(address)": {"details": "Set the reward token address.", "params": {"_rewardTokenAddress": "Address of the reward token"}}, "supportsAsset(address)": {"details": "Retuns bool indicating whether asset is supported by strategy", "params": {"_asset": "Address of the asset"}}, "transferGovernance(address)": {"details": "Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete", "params": {"_newGovernor": "Address of the new Governor"}}, "transferToken(address,uint256)": {"details": "Transfer token to governor. Intended for recovering tokens stuck in     strategy contracts, i.e. mistaken sends.", "params": {"_amount": "Amount of the asset to transfer", "_asset": "Address for the asset"}}, "withdraw(address,address,uint256)": {"details": "Withdraw asset from Aave", "params": {"_amount": "Amount of asset to withdraw", "_asset": "Address of asset to withdraw", "_recipient": "Address to receive withdrawn asset"}, "return": "amountWithdrawn Amount of asset that was withdrawn"}, "withdrawAll()": {"details": "Remove all assets from platform and send them to Vault contract."}}}, "userdoc": {"methods": {}}}