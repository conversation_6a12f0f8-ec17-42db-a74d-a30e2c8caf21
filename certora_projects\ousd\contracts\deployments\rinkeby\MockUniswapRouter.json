{"abi": [{"constant": false, "inputs": [{"internalType": "uint256", "name": "amountIn", "type": "uint256"}, {"internalType": "uint256", "name": "amountOutMin", "type": "uint256"}, {"internalType": "address[]", "name": "path", "type": "address[]"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}], "name": "swapExactTokensForTokens", "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_token0", "type": "address"}, {"internalType": "address", "name": "_token1", "type": "address"}], "name": "initialize", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "WETH", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}], "receipt": {"to": null, "from": "******************************************", "contractAddress": "******************************************", "transactionIndex": 15, "gasUsed": "533763", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x9968c5dd75aa6d688d0e3be0bc77cee61c850ebe0f95005393c81f58e47eed32", "transactionHash": "0x10c3ef86e4bf87b1bf6f0a0aa3dfc9e66955f1fc1b996d143a5d2bbeecf609b6", "logs": [], "blockNumber": 7366337, "cumulativeGasUsed": "2830611", "status": 1, "byzantium": true}, "address": "******************************************", "args": [], "solcInputHash": "0x66cc47dc6f28d3ac0ff0da895351bafef45fa46b7c3ee3141d5ac501f988f363", "metadata": "{\"compiler\":{\"version\":\"0.5.11+commit.c082d0b4\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amountIn\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amountOutMin\",\"type\":\"uint256\"},{\"internalType\":\"address[]\",\"name\":\"path\",\"type\":\"address[]\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"}],\"name\":\"swapExactTokensForTokens\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"amounts\",\"type\":\"uint256[]\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_token0\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_token1\",\"type\":\"address\"}],\"name\":\"initialize\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"WETH\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"methods\":{}},\"userdoc\":{\"methods\":{}}},\"settings\":{\"compilationTarget\":{\"contracts/mocks/MockUniswapRouter.sol\":\"MockUniswapRouter\"},\"evmVersion\":\"petersburg\",\"libraries\":{},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts/math/SafeMath.sol\":{\"keccak256\":\"0x640b6dee7a4b830bdfd52b5031a07fc2b12209f5b2e29e5d364a7d37f69d8076\",\"urls\":[\"bzz-raw://31113152e1ddb78fe7a4197f247591ca894e93f916867beb708d8e747b6cc74f\",\"dweb:/ipfs/QmbZaJyXdpsYGykVhHH9qpVGQg9DGCxE2QufbCUy3daTgq\"]},\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xe5bb0f57cff3e299f360052ba50f1ea0fff046df2be070b6943e0e3c3fdad8a9\",\"urls\":[\"bzz-raw://59fd025151435da35faa8093a5c7a17de02de9d08ad27275c5cdf05050820d91\",\"dweb:/ipfs/QmQMvwEcPhoRXzbXyrdoeRtvLoifUW9Qh7Luho7bmUPRkc\"]},\"contracts/interfaces/IBasicToken.sol\":{\"keccak256\":\"0x01eab42b6d54fa5389598e0663c24680ecc017e2da848e8ea1c40aeaa8225eef\",\"urls\":[\"bzz-raw://02670b5ea9f966c1f989a3a78ecca8d6c9898a8ff1f9886c287e0f669706afb1\",\"dweb:/ipfs/QmbdjDcqbP1fwe5AZG1o6HLwVbMxvUXJXrVtddNB5hCLMS\"]},\"contracts/interfaces/uniswap/IUniswapV2Router02.sol\":{\"keccak256\":\"0x95317742afaf40ff2b8909ffd6b6785d25ab4ff01a636b14df68e950a050f2dd\",\"urls\":[\"bzz-raw://f2869254642dd2942d319656920c80fed8d988a8914ff3cd572b9e72de8deea7\",\"dweb:/ipfs/QmWVXWaZUpc7H7c5XhfjrHiy6eWfQS4ojCdJNvLn9CMUTC\"]},\"contracts/mocks/MockUniswapRouter.sol\":{\"keccak256\":\"0xdd7d1763ea1625bf05efada5858f0ccbb09d5bf39f49a03027955fe450b04df8\",\"urls\":[\"bzz-raw://72db2c8301935d7a288cbe564796e23a1d4e4fe0d21a2624787d27f6b9ebf663\",\"dweb:/ipfs/QmbQbWn52YbjJfkoxSFijoU8jjku2PdwwJQQ3W5jkzMzKo\"]},\"contracts/utils/Helpers.sol\":{\"keccak256\":\"0xd2ca92e0af883dc1aec5b22caced274e59829e0e30a9e955dcc48b8d921f5cdc\",\"urls\":[\"bzz-raw://90a369ed17c35dfbb4bc6dd98d767b703f35687c28b18f34153744f494fb1ef2\",\"dweb:/ipfs/QmSe65R6r8RUgGvP1LinrH4ZycsgMQpKdcG83RG5NwmqRN\"]},\"contracts/utils/StableMath.sol\":{\"keccak256\":\"0xa77fccf850feb6d54ba3a6530f92554caef8a67a1ceb573d4f8a5d1bf64ff9d2\",\"urls\":[\"bzz-raw://207ae7a5751d4e280d1c3a024a9a13d811d9b498f3e59c4c039029061826b9b5\",\"dweb:/ipfs/QmXq9LsYNggAVEWCKFxSdba5VxnXYWbfpcoR4UDkx3ouby\"]}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"methods": {}}, "userdoc": {"methods": {}}, "gasEstimates": {"creation": {"codeDepositCost": "443600", "executionCost": "20747", "totalCost": "464347"}, "external": {"WETH()": "475", "initialize(address,address)": "40803", "swapExactTokensForTokens(uint256,uint256,address[],address,uint256)": "infinite"}}}