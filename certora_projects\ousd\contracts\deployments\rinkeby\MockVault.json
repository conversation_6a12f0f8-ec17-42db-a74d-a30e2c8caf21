{"abi": [{"constant": false, "inputs": [], "name": "pauseDeposits", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "depositPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address[]", "name": "_assets", "type": "address[]"}, {"internalType": "uint256[]", "name": "_amounts", "type": "uint256[]"}], "name": "mintMultiple", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [], "name": "unpauseRebase", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "redeemFeeBps", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "governor", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_asset", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "transferToken", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_addr", "type": "address"}], "name": "removeStrategy", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "name": "priceAssetUSDRedeem", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "string", "name": "symbol", "type": "string"}], "name": "priceUSDRedeem", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "getAllAssets", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "redeemAll", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "getStrategyCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_priceProvider", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_asset", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "mint", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "name": "priceAssetUSDMint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_priceProvider", "type": "address"}, {"internalType": "address", "name": "_ousd", "type": "address"}], "name": "initialize", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_asset", "type": "address"}], "name": "supportAsset", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address[]", "name": "_strategyAddresses", "type": "address[]"}, {"internalType": "uint256[]", "name": "_weights", "type": "uint256[]"}], "name": "setStrategyWeights", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "rebasePaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "claimGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "_asset", "type": "address"}], "name": "checkBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "unpauseDeposits", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "calculateRedeemOutputs", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "string", "name": "symbol", "type": "string"}], "name": "priceUSDMint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "_vault<PERSON><PERSON>er", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "_asset", "type": "address"}], "name": "isSupportedAsset", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "autoAllocateThreshold", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "getAssetCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "allocate", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [], "name": "rebase", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "_threshold", "type": "uint256"}], "name": "setAutoAllocateThreshold", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "priceProvider", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "_totalValue", "type": "uint256"}], "name": "setTotalValue", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [], "name": "pauseRebase", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "isGovernor", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "getAPR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_addr", "type": "address"}, {"internalType": "uint256", "name": "_targetWeight", "type": "uint256"}], "name": "addStrategy", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_newGovernor", "type": "address"}], "name": "transferGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [], "name": "totalValue", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "redeem", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "_redeemFeeBps", "type": "uint256"}], "name": "setRedeemFeeBps", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "_asset", "type": "address"}], "name": "AssetSupported", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "_addr", "type": "address"}], "name": "StrategyAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "_addr", "type": "address"}], "name": "StrategyRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "_addr", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_value", "type": "uint256"}], "name": "Mint", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "_addr", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_value", "type": "uint256"}], "name": "Redeem", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address[]", "name": "_strategyAddresses", "type": "address[]"}, {"indexed": false, "internalType": "uint256[]", "name": "weights", "type": "uint256[]"}], "name": "StrategyWeightsUpdated", "type": "event"}, {"anonymous": false, "inputs": [], "name": "DepositsPaused", "type": "event"}, {"anonymous": false, "inputs": [], "name": "DepositsUnpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "PendingGovernorshipTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "GovernorshipTransferred", "type": "event"}], "receipt": {"to": null, "from": "0xe0293212b84df3900BC24f9321e7721Aa7E50A3b", "contractAddress": "0x13D66E55726B6cFd8cbE4e1011E91AfF340Bf1E2", "transactionIndex": 12, "gasUsed": "5193598", "logsBloom": "0x00000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000008100000000000000000020000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000800000000000000000000000000000000000004000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000200020000000000000000000000020000000000000000000000000000000000000000000", "blockHash": "0x2d2cb97ee11e60efe13c0982b4399f175a875ef60a05edacb59651a0e290d893", "transactionHash": "0x9eba98e4539d63b477862d54719e4b0bcc54a9b4257cbdca7bbe1bd04d7b001e", "logs": [{"transactionIndex": 12, "blockNumber": 7216809, "transactionHash": "0x9eba98e4539d63b477862d54719e4b0bcc54a9b4257cbdca7bbe1bd04d7b001e", "address": "0x13D66E55726B6cFd8cbE4e1011E91AfF340Bf1E2", "topics": ["0xc7c0c772add429241571afb3805861fb3cfa2af374534088b76cdb4325a87e9a", "0x0000000000000000000000000000000000000000000000000000000000000000", "0x000000000000000000000000e0293212b84df3900bc24f9321e7721aa7e50a3b"], "data": "0x", "logIndex": 12, "blockHash": "0x2d2cb97ee11e60efe13c0982b4399f175a875ef60a05edacb59651a0e290d893"}], "blockNumber": 7216809, "cumulativeGasUsed": "6042392", "status": 1, "byzantium": true}, "address": "0x13D66E55726B6cFd8cbE4e1011E91AfF340Bf1E2", "args": [], "solcInputHash": "0xb3650c08a4e2e14ecccb8f0a47293db3f4f322627843b6172e27a33d674d0b0a", "metadata": "{\"compiler\":{\"version\":\"0.5.11+commit.c082d0b4\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"constant\":false,\"inputs\":[],\"name\":\"pauseDeposits\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"depositPaused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"_assets\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"_amounts\",\"type\":\"uint256[]\"}],\"name\":\"mintMultiple\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"unpauseRebase\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"redeemFeeBps\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"governor\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"transferToken\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_addr\",\"type\":\"address\"}],\"name\":\"removeStrategy\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"priceAssetUSDRedeem\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"vaultBuffer\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"}],\"name\":\"priceUSDRedeem\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"getAllAssets\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"redeemAll\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"getStrategyCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_priceProvider\",\"type\":\"address\"}],\"name\":\"setPriceProvider\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"mint\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"priceAssetUSDMint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_priceProvider\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_ousd\",\"type\":\"address\"}],\"name\":\"initialize\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"}],\"name\":\"supportAsset\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"_strategyAddresses\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"_weights\",\"type\":\"uint256[]\"}],\"name\":\"setStrategyWeights\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"rebasePaused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"claimGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"}],\"name\":\"checkBalance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"unpauseDeposits\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"calculateRedeemOutputs\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"\",\"type\":\"uint256[]\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"}],\"name\":\"priceUSDMint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_vaultBuffer\",\"type\":\"uint256\"}],\"name\":\"setVaultBuffer\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"}],\"name\":\"isSupportedAsset\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"autoAllocateThreshold\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"getAssetCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"allocate\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"rebase\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_threshold\",\"type\":\"uint256\"}],\"name\":\"setAutoAllocateThreshold\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"priceProvider\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_totalValue\",\"type\":\"uint256\"}],\"name\":\"setTotalValue\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"pauseRebase\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"isGovernor\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"getAPR\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_addr\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_targetWeight\",\"type\":\"uint256\"}],\"name\":\"addStrategy\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_newGovernor\",\"type\":\"address\"}],\"name\":\"transferGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"totalValue\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"redeem\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_redeemFeeBps\",\"type\":\"uint256\"}],\"name\":\"setRedeemFeeBps\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"}],\"name\":\"AssetSupported\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"_addr\",\"type\":\"address\"}],\"name\":\"StrategyAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"_addr\",\"type\":\"address\"}],\"name\":\"StrategyRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"_addr\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_value\",\"type\":\"uint256\"}],\"name\":\"Mint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"_addr\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_value\",\"type\":\"uint256\"}],\"name\":\"Redeem\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"_strategyAddresses\",\"type\":\"address[]\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"weights\",\"type\":\"uint256[]\"}],\"name\":\"StrategyWeightsUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"DepositsPaused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"DepositsUnpaused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"PendingGovernorshipTransfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"GovernorshipTransferred\",\"type\":\"event\"}],\"devdoc\":{\"methods\":{\"addStrategy(address,uint256)\":{\"details\":\"Add a strategy to the Vault.\",\"params\":{\"_addr\":\"Address of the strategy to add\",\"_targetWeight\":\"Target percentage of asset allocation to strategy\"}},\"allocate()\":{\"details\":\"Allocate unallocated funds on Vault to strategies.*\"},\"checkBalance(address)\":{\"params\":{\"_asset\":\"Address of asset\"},\"return\":\"uint256 Balance of asset in decimals of asset\"},\"claimGovernance()\":{\"details\":\"Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor.\"},\"getAPR()\":{\"details\":\"Get the total APR of the Vault and all Strategies.\"},\"getAllAssets()\":{\"details\":\"Return all asset addresses in order\"},\"getAssetCount()\":{\"details\":\"Return the number of assets suppported by the Vault.\"},\"getStrategyCount()\":{\"details\":\"Return the number of strategies active on the Vault.\"},\"governor()\":{\"details\":\"Returns the address of the current Governor.\"},\"isGovernor()\":{\"details\":\"Returns true if the caller is the current Governor.\"},\"isSupportedAsset(address)\":{\"details\":\"Determines if an asset is supported by the vault.\",\"params\":{\"_asset\":\"Address of the asset\"}},\"mint(address,uint256)\":{\"details\":\"Deposit a supported asset and mint OUSD.\",\"params\":{\"_amount\":\"Amount of the asset being deposited\",\"_asset\":\"Address of the asset being deposited\"}},\"mintMultiple(address[],uint256[])\":{\"details\":\"Mint for multiple assets in the same call.\",\"params\":{\"_amounts\":\"Amount of each asset at the same index in the _assets                to deposit.\",\"_assets\":\"Addresses of assets being deposited\"}},\"pauseDeposits()\":{\"details\":\"Set the deposit paused flag to true to prevent deposits.\"},\"pauseRebase()\":{\"details\":\"Set the deposit paused flag to true to prevent rebasing.\"},\"priceAssetUSDMint(address)\":{\"details\":\"Returns the total price in 18 digit USD for a given asset.     Using Min since min is what we use for mint pricing\",\"params\":{\"asset\":\"Address of the asset\"},\"return\":\"uint256 USD price of 1 of the asset\"},\"priceAssetUSDRedeem(address)\":{\"details\":\"Returns the total price in 18 digit USD for a given asset.     Using Max since max is what we use for redeem pricing\",\"params\":{\"asset\":\"Address of the asset\"},\"return\":\"uint256 USD price of 1 of the asset\"},\"priceUSDMint(string)\":{\"details\":\"Returns the total price in 18 digit USD for a given asset.     Using Min since min is what we use for mint pricing\",\"params\":{\"symbol\":\"String symbol of the asset\"},\"return\":\"uint256 USD price of 1 of the asset\"},\"priceUSDRedeem(string)\":{\"details\":\"Returns the total price in 18 digit USD for a given asset.     Using Max since max is what we use for redeem pricing\",\"params\":{\"symbol\":\"String symbol of the asset\"},\"return\":\"uint256 USD price of 1 of the asset\"},\"rebase()\":{\"details\":\"Calculate the total value of assets held by the Vault and all        strategies and update the supply of oUSD\"},\"redeem(uint256)\":{\"details\":\"Withdraw a supported asset and burn OUSD.\",\"params\":{\"_amount\":\"Amount of OUSD to burn\"}},\"removeStrategy(address)\":{\"details\":\"Remove a strategy from the Vault. Removes all invested assets and returns them to the Vault.\",\"params\":{\"_addr\":\"Address of the strategy to remove\"}},\"setAutoAllocateThreshold(uint256)\":{\"details\":\"Sets the minimum amount of OUSD in a mint to trigger an automatic allocation of funds afterwords.\",\"params\":{\"_threshold\":\"OUSD amount with 18 fixed decimals.\"}},\"setPriceProvider(address)\":{\"details\":\"Set address of price provider.\",\"params\":{\"_priceProvider\":\"Address of price provider\"}},\"setRedeemFeeBps(uint256)\":{\"details\":\"Set a fee in basis points to be charged for a redeem.\",\"params\":{\"_redeemFeeBps\":\"Basis point fee to be charged\"}},\"setStrategyWeights(address[],uint256[])\":{\"params\":{\"_strategyAddresses\":\"Array of strategy addresses\",\"_weights\":\"Array of corresponding weights, with 18 decimals. For ex. 100%=1e18, 30%=3e17.\"}},\"setVaultBuffer(uint256)\":{\"details\":\"Set a buffer of assets to keep in the Vault to handle most redemptions without needing to spend gas unwinding assets from a Strategy.\",\"params\":{\"_vaultBuffer\":\"Percentage using 18 decimals. 100% = 1e18.\"}},\"supportAsset(address)\":{\"details\":\"Add a supported asset to the contract, i.e. one that can be        to mint OUSD.\",\"params\":{\"_asset\":\"Address of asset\"}},\"transferGovernance(address)\":{\"details\":\"Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete\",\"params\":{\"_newGovernor\":\"Address of the new Governor\"}},\"transferToken(address,uint256)\":{\"details\":\"Transfer token to governor. Intended for recovering tokens stuck in     contract, i.e. mistaken sends.\",\"params\":{\"_amount\":\"Amount of the asset to transfer\",\"_asset\":\"Address for the asset\"}},\"unpauseDeposits()\":{\"details\":\"Set the deposit paused flag to false to enable deposits.\"},\"unpauseRebase()\":{\"details\":\"Set the deposit paused flag to true to allow rebasing.\"}}},\"userdoc\":{\"methods\":{\"allocate()\":{\"notice\":\"Allocate unallocated funds on Vault to strategies.\"},\"calculateRedeemOutputs(uint256)\":{\"notice\":\"Calculate the outputs for a redeem function, i.e. the mix of coins that will be returned\"},\"checkBalance(address)\":{\"notice\":\"Get the balance of an asset held in Vault and all strategies.\"},\"redeemAll()\":{\"notice\":\"Withdraw a supported asset and burn all OUSD.\"},\"setStrategyWeights(address[],uint256[])\":{\"notice\":\"Set the weights for multiple strategies.\"}}}},\"settings\":{\"compilationTarget\":{\"contracts/mocks/MockVault.sol\":\"MockVault\"},\"evmVersion\":\"petersburg\",\"libraries\":{},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts/GSN/Context.sol\":{\"keccak256\":\"0x90a3995645af7562d84b9d69363ffa5ae7217714ab61e951bf7bc450f40e4061\",\"urls\":[\"bzz-raw://216ef9d6b614db4eb46970b4e84903f2534a45572dd30a79f0041f1a5830f436\",\"dweb:/ipfs/QmNPrJ4MWKUAWzKXpUqeyKRUfosaoANZAqXgvepdrCwZAG\"]},\"@openzeppelin/contracts/math/SafeMath.sol\":{\"keccak256\":\"0x640b6dee7a4b830bdfd52b5031a07fc2b12209f5b2e29e5d364a7d37f69d8076\",\"urls\":[\"bzz-raw://31113152e1ddb78fe7a4197f247591ca894e93f916867beb708d8e747b6cc74f\",\"dweb:/ipfs/QmbZaJyXdpsYGykVhHH9qpVGQg9DGCxE2QufbCUy3daTgq\"]},\"@openzeppelin/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0xb15af804e2bc97db51e4e103f13de9fe13f87e6b835d7a88c897966c0e58506e\",\"urls\":[\"bzz-raw://1e8cff8437557fc915a3bed968fcd8f2df9809599e665ef69c2c9ce628548055\",\"dweb:/ipfs/QmP5spYP8vs2jvLF8zNrXUbqB79hMsoEvMHiLcBxerWKcm\"]},\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xe5bb0f57cff3e299f360052ba50f1ea0fff046df2be070b6943e0e3c3fdad8a9\",\"urls\":[\"bzz-raw://59fd025151435da35faa8093a5c7a17de02de9d08ad27275c5cdf05050820d91\",\"dweb:/ipfs/QmQMvwEcPhoRXzbXyrdoeRtvLoifUW9Qh7Luho7bmUPRkc\"]},\"@openzeppelin/contracts/token/ERC20/SafeERC20.sol\":{\"keccak256\":\"0x6f2c9955d65c522b80f4b8792f076512d2df947d2112cbc4d98a4781ed42ede2\",\"urls\":[\"bzz-raw://7d8ec81683520c06baeef3f7e06cd82bd6fd5fa611f26857f475f6c829540aff\",\"dweb:/ipfs/QmTDkFzKnrpiV1UKnSoiZAHPuguWzokrr4pFbSPvyaSo56\"]},\"@openzeppelin/contracts/utils/Address.sol\":{\"keccak256\":\"0x1a8e5072509c5ea7365eb1d48030b9be865140c8fb779968da0a459a0e174a11\",\"urls\":[\"bzz-raw://03335b7b07c7c8c8d613cfdd8ec39a0b5ec133ee510bf2fe6cc5a496767bef4b\",\"dweb:/ipfs/Qmebp4nzPja645c9yXSdJkGq96oU3am3LUnG2K3R7XxyKf\"]},\"@openzeppelin/upgrades/contracts/Initializable.sol\":{\"keccak256\":\"0x9bfec92e36234ecc99b5d37230acb6cd1f99560233753162204104a4897e8721\",\"urls\":[\"bzz-raw://5cf7c208583d4d046d75bd99f5507412ab01cce9dd9f802ce9768a416d93ea2f\",\"dweb:/ipfs/QmcQS1BBMPpVEkXP3qzwSjxHNrqDek8YeR7xbVWDC9ApC7\"]},\"contracts/governance/Governable.sol\":{\"keccak256\":\"0x5e2d551cd7cf929d1920ad400b41e52595745e70aaaf07520f6221c3b6e2b1d0\",\"urls\":[\"bzz-raw://57858d7add96c5252e925d890d34c824dd4c5f8c4373b36931cc40f4a0a92778\",\"dweb:/ipfs/QmQ4TFtsYdT663gK4K2tuvfw4GNhRwfbYREQdjtUXVzEqi\"]},\"contracts/interfaces/IBasicToken.sol\":{\"keccak256\":\"0x01eab42b6d54fa5389598e0663c24680ecc017e2da848e8ea1c40aeaa8225eef\",\"urls\":[\"bzz-raw://02670b5ea9f966c1f989a3a78ecca8d6c9898a8ff1f9886c287e0f669706afb1\",\"dweb:/ipfs/QmbdjDcqbP1fwe5AZG1o6HLwVbMxvUXJXrVtddNB5hCLMS\"]},\"contracts/interfaces/IMinMaxOracle.sol\":{\"keccak256\":\"0x4bfced1f800eb386e7ff0becaf9d0cc64cc2774011a32054747e4d1b5687d1af\",\"urls\":[\"bzz-raw://9941f87b9b890d92441146bb6116a7d940c8fdb3e91d39e7d04a2f88e466ffef\",\"dweb:/ipfs/QmdWx7iKot6oykgJSLizkpKVi9aG8bYXqsyhjJ74oYEdaF\"]},\"contracts/interfaces/IStrategy.sol\":{\"keccak256\":\"0x60cff0cee8dc7b11b84966ad940225a9f5018171822906d54fa9b427029639c7\",\"urls\":[\"bzz-raw://98fb11f727d53f24b4d2793f440b7377a9a780f32d8201ff63cffcbd6341666a\",\"dweb:/ipfs/QmQxAprAJrC2H6whwzmCVHQLVfgzivbxzyHUsByFAHpWGS\"]},\"contracts/mocks/MockVault.sol\":{\"keccak256\":\"0x3f9e5614915eb85a63e69d4dc7efd174af1b27f0347f0cb6077b269c2a803d7f\",\"urls\":[\"bzz-raw://248795142bfbf64573c224414fb816e8a7bb5890ed317d61785bc7ab405555df\",\"dweb:/ipfs/QmVuxmTJRRwFSG5us4vihVZ6VUADbhknpHmLF6MDCRrvWk\"]},\"contracts/token/OUSD.sol\":{\"keccak256\":\"0xec48c5524dd5edbe631ded22358136e58e2d6b52917c0e8aa6ee86b1ab65967c\",\"urls\":[\"bzz-raw://7c16badd819d0fbf4bf15c899584212828904ff85d03dc1ed171603fd7c5237b\",\"dweb:/ipfs/QmSuhwgdCin2T4UQbtmLwtijSJ4MXKKwQimcn9mo3hbznY\"]},\"contracts/utils/Helpers.sol\":{\"keccak256\":\"0xd2ca92e0af883dc1aec5b22caced274e59829e0e30a9e955dcc48b8d921f5cdc\",\"urls\":[\"bzz-raw://90a369ed17c35dfbb4bc6dd98d767b703f35687c28b18f34153744f494fb1ef2\",\"dweb:/ipfs/QmSe65R6r8RUgGvP1LinrH4ZycsgMQpKdcG83RG5NwmqRN\"]},\"contracts/utils/InitializableERC20Detailed.sol\":{\"keccak256\":\"0x03e571fc248ab7adb352342aca77471333fa3ec8937d69493904fd3bedf6aea6\",\"urls\":[\"bzz-raw://d962ec29e0806512a8207d1451a6ff9013f293262e62c244b7d832ad64955918\",\"dweb:/ipfs/Qma2QYGaC9G6PR18es3cGjENmKwZs8tHsSNbkycwv9HrGi\"]},\"contracts/utils/InitializableToken.sol\":{\"keccak256\":\"0xab6e1ccb80eab2ce6d7aaebf3e5e44e6d082753e88576f3e5bc3ae3976b84071\",\"urls\":[\"bzz-raw://2b0230198e27b36f5297d25aff2ea80edf8cd846220958670115ed84515ae770\",\"dweb:/ipfs/QmakGg4WHz5jgZGT78kyvNnNyj5evKuxNkQr1JXikoKcpA\"]},\"contracts/utils/StableMath.sol\":{\"keccak256\":\"0xa77fccf850feb6d54ba3a6530f92554caef8a67a1ceb573d4f8a5d1bf64ff9d2\",\"urls\":[\"bzz-raw://207ae7a5751d4e280d1c3a024a9a13d811d9b498f3e59c4c039029061826b9b5\",\"dweb:/ipfs/QmXq9LsYNggAVEWCKFxSdba5VxnXYWbfpcoR4UDkx3ouby\"]},\"contracts/vault/Vault.sol\":{\"keccak256\":\"0x222bf5d6063f9a9196c0a8e8c04fcbd86c148d66947d6cace2501f418c4b4138\",\"urls\":[\"bzz-raw://e084dee61117178d8c0851a770bfae42a44cb14d02f6da34a0976f3724e65156\",\"dweb:/ipfs/QmS96bA4rL6hGNm85tUvdwCtMjB2XsRRLswUCWm8rbaUJM\"]}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"methods": {"addStrategy(address,uint256)": {"details": "Add a strategy to the Vault.", "params": {"_addr": "Address of the strategy to add", "_targetWeight": "Target percentage of asset allocation to strategy"}}, "allocate()": {"details": "Allocate unallocated funds on Vault to strategies.*"}, "checkBalance(address)": {"params": {"_asset": "Address of asset"}, "return": "uint256 Balance of asset in decimals of asset"}, "claimGovernance()": {"details": "Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor."}, "getAPR()": {"details": "Get the total APR of the Vault and all Strategies."}, "getAllAssets()": {"details": "Return all asset addresses in order"}, "getAssetCount()": {"details": "Return the number of assets suppported by the Vault."}, "getStrategyCount()": {"details": "Return the number of strategies active on the Vault."}, "governor()": {"details": "Returns the address of the current Governor."}, "isGovernor()": {"details": "Returns true if the caller is the current Governor."}, "isSupportedAsset(address)": {"details": "Determines if an asset is supported by the vault.", "params": {"_asset": "Address of the asset"}}, "mint(address,uint256)": {"details": "Deposit a supported asset and mint OUSD.", "params": {"_amount": "Amount of the asset being deposited", "_asset": "Address of the asset being deposited"}}, "mintMultiple(address[],uint256[])": {"details": "Mint for multiple assets in the same call.", "params": {"_amounts": "Amount of each asset at the same index in the _assets                to deposit.", "_assets": "Addresses of assets being deposited"}}, "pauseDeposits()": {"details": "Set the deposit paused flag to true to prevent deposits."}, "pauseRebase()": {"details": "Set the deposit paused flag to true to prevent rebasing."}, "priceAssetUSDMint(address)": {"details": "Returns the total price in 18 digit USD for a given asset.     Using Min since min is what we use for mint pricing", "params": {"asset": "Address of the asset"}, "return": "uint256 USD price of 1 of the asset"}, "priceAssetUSDRedeem(address)": {"details": "Returns the total price in 18 digit USD for a given asset.     Using Max since max is what we use for redeem pricing", "params": {"asset": "Address of the asset"}, "return": "uint256 USD price of 1 of the asset"}, "priceUSDMint(string)": {"details": "Returns the total price in 18 digit USD for a given asset.     Using Min since min is what we use for mint pricing", "params": {"symbol": "String symbol of the asset"}, "return": "uint256 USD price of 1 of the asset"}, "priceUSDRedeem(string)": {"details": "Returns the total price in 18 digit USD for a given asset.     Using Max since max is what we use for redeem pricing", "params": {"symbol": "String symbol of the asset"}, "return": "uint256 USD price of 1 of the asset"}, "rebase()": {"details": "Calculate the total value of assets held by the Vault and all        strategies and update the supply of oUSD"}, "redeem(uint256)": {"details": "Withdraw a supported asset and burn OUSD.", "params": {"_amount": "Amount of OUSD to burn"}}, "removeStrategy(address)": {"details": "Remove a strategy from the Vault. Removes all invested assets and returns them to the Vault.", "params": {"_addr": "Address of the strategy to remove"}}, "setAutoAllocateThreshold(uint256)": {"details": "Sets the minimum amount of OUSD in a mint to trigger an automatic allocation of funds afterwords.", "params": {"_threshold": "OUSD amount with 18 fixed decimals."}}, "setPriceProvider(address)": {"details": "Set address of price provider.", "params": {"_priceProvider": "Address of price provider"}}, "setRedeemFeeBps(uint256)": {"details": "Set a fee in basis points to be charged for a redeem.", "params": {"_redeemFeeBps": "Basis point fee to be charged"}}, "setStrategyWeights(address[],uint256[])": {"params": {"_strategyAddresses": "Array of strategy addresses", "_weights": "Array of corresponding weights, with 18 decimals. For ex. 100%=1e18, 30%=3e17."}}, "setVaultBuffer(uint256)": {"details": "Set a buffer of assets to keep in the Vault to handle most redemptions without needing to spend gas unwinding assets from a Strategy.", "params": {"_vaultBuffer": "Percentage using 18 decimals. 100% = 1e18."}}, "supportAsset(address)": {"details": "Add a supported asset to the contract, i.e. one that can be        to mint OUSD.", "params": {"_asset": "Address of asset"}}, "transferGovernance(address)": {"details": "Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete", "params": {"_newGovernor": "Address of the new Governor"}}, "transferToken(address,uint256)": {"details": "Transfer token to governor. Intended for recovering tokens stuck in     contract, i.e. mistaken sends.", "params": {"_amount": "Amount of the asset to transfer", "_asset": "Address for the asset"}}, "unpauseDeposits()": {"details": "Set the deposit paused flag to false to enable deposits."}, "unpauseRebase()": {"details": "Set the deposit paused flag to true to allow rebasing."}}}, "userdoc": {"methods": {"allocate()": {"notice": "Allocate unallocated funds on Vault to strategies."}, "calculateRedeemOutputs(uint256)": {"notice": "Calculate the outputs for a redeem function, i.e. the mix of coins that will be returned"}, "checkBalance(address)": {"notice": "Get the balance of an asset held in Vault and all strategies."}, "redeemAll()": {"notice": "Withdraw a supported asset and burn all OUSD."}, "setStrategyWeights(address[],uint256[])": {"notice": "Set the weights for multiple strategies."}}}, "gasEstimates": {"creation": {"codeDepositCost": "4731000", "executionCost": "infinite", "totalCost": "infinite"}, "external": {"addStrategy(address,uint256)": "83135", "allocate()": "infinite", "autoAllocateThreshold()": "494", "calculateRedeemOutputs(uint256)": "infinite", "checkBalance(address)": "infinite", "claimGovernance()": "infinite", "depositPaused()": "572", "getAPR()": "infinite", "getAllAssets()": "infinite", "getAssetCount()": "532", "getStrategyCount()": "533", "governor()": "525", "initialize(address,address)": "infinite", "isGovernor()": "629", "isSupportedAsset(address)": "717", "mint(address,uint256)": "infinite", "mintMultiple(address[],uint256[])": "infinite", "pauseDeposits()": "21608", "pauseRebase()": "20895", "priceAssetUSDMint(address)": "infinite", "priceAssetUSDRedeem(address)": "infinite", "priceProvider()": "520", "priceUSDMint(string)": "infinite", "priceUSDRedeem(string)": "infinite", "rebase()": "infinite", "rebasePaused()": "658", "redeem(uint256)": "infinite", "redeemAll()": "infinite", "redeemFeeBps()": "540", "removeStrategy(address)": "infinite", "setAutoAllocateThreshold(uint256)": "20610", "setPriceProvider(address)": "20958", "setRedeemFeeBps(uint256)": "20719", "setStrategyWeights(address[],uint256[])": "infinite", "setTotalValue(uint256)": "20331", "setVaultBuffer(uint256)": "20610", "supportAsset(address)": "63112", "totalValue()": "523", "transferGovernance(address)": "infinite", "transferToken(address,uint256)": "infinite", "unpauseDeposits()": "21651", "unpauseRebase()": "20897", "vaultBuffer()": "539"}, "internal": {"_totalValue()": "223"}}}