diff -ruN ../src/.gitignore .gitignore
--- ../src/.gitignore	1970-01-01 02:00:00
+++ .gitignore	2023-03-19 12:27:56
@@ -0,0 +1,2 @@
+*
+!.gitignore
diff -ruN ../src/contracts/StakedTokenV2.sol contracts/StakedTokenV2.sol
--- ../src/contracts/StakedTokenV2.sol	2023-03-19 12:10:59
+++ contracts/StakedTokenV2.sol	2023-03-19 12:27:56
@@ -104,6 +104,7 @@
   /// @inheritdoc IStakedTokenV2
   function getTotalRewardsBalance(address staker)
     external
+    override
     view
     returns (uint256)
   {
diff -ruN ../src/contracts/StakedTokenV3.sol contracts/StakedTokenV3.sol
--- ../src/contracts/StakedTokenV3.sol	2023-03-19 12:10:59
+++ contracts/StakedTokenV3.sol	2023-03-19 12:27:56
@@ -162,7 +162,7 @@
   }
 
   /// @inheritdoc IStakedTokenV3
-  function previewStake(uint256 assets) public view returns (uint256) {
+  function previewStake(uint256 assets) public override view returns (uint256) {
     return (assets * _currentExchangeRate) / EXCHANGE_RATE_UNIT;
   }
 
@@ -333,13 +333,14 @@
   /// @inheritdoc IStakedTokenV3
   function setCooldownSeconds(uint256 cooldownSeconds)
     external
+    override
     onlyCooldownAdmin
   {
     _setCooldownSeconds(cooldownSeconds);
   }
 
   /// @inheritdoc IStakedTokenV3
-  function getCooldownSeconds() external view returns (uint256) {
+  function getCooldownSeconds() external override view returns (uint256) {
     return _cooldownSeconds;
   }
 
