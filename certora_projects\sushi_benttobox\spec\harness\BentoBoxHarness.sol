pragma solidity 0.6.12;
pragma experimental ABIEncoderV2;

import "../../contracts/BentoBox.sol";

// Note: Rebasing tokens ARE NOT supported and WILL cause loss of funds
contract BentoBoxHarness is BentoBox {

	// getters to internal 
	function totalTokenAmount(address token) public returns (uint128) {
		return totals[IERC20(token)].elastic;
	}

	function totalTokenShare(address token) public returns (uint128) {
		return totals[IERC20(token)].base;
	}

	// wrap internal function
	function tokenBalanceOf(address token) public returns (uint256) {
		return _tokenBalanceOf(IERC20(token));
	}

	// wrap internal function
	function tokenBalanceOfUser(IERC20 token, address user) public returns (uint256) {
		return token.balanceOf(user);
	}

	function assumeTosNotOther(address[] calldata toParams, address other) private {
        for (uint256 i = 0; i < toParams.length; i++) {
            address to = toParams[i];
			require (to != other);
		}
	}
	
	// to constraint harnessOther == other (from the spec)
	address public harnessOther;
	address public harnessFrom;
	IFlashBorrower public harnessBorrower;
	IERC20 public harnessToken;

	// limit loops to bound of 3
	function transferMultiple(IERC20 token, address from, address[] calldata tos, uint256[] calldata shares) public override {
		require (tos.length <= 3);
	
		// this would not constraint tos for any other rule except noChangeToOthersBalances,
		// because harnessOther is only constraint in noChangeToOthersBalances
		assumeTosNotOther(tos, harnessOther);
		require(from == harnessFrom);

		super.transferMultiple(token, from, tos, shares);
	}

	function flashLoan(IFlashBorrower borrower, address receiver, IERC20 token, uint256 amount, bytes calldata data) override public
	{
		require(harnessToken == token);
		require(harnessBorrower == borrower);
		super.flashLoan(borrower, receiver, token, amount, data );
	}




	function getStrategyTokenBalance(IERC20 token) public returns (uint256) {
		return strategyData[token].balance;
	}

	//for invariants we need a function that simulate the constructor 
	function init_state() public { }

	constructor(IERC20 wethToken_) BentoBox(wethToken_) public { }


	function batch(bytes[] calldata calls, bool revertOnFail) external override payable returns(bool[] memory successes, bytes[] memory results) {
	}


	function deploy(address masterContract, bytes calldata data, bool useCreate2) public override  payable returns (address) {

	}

	function permitToken(IERC20 token, address from, uint256 amount, uint256 deadline, uint8 v, bytes32 r, bytes32 s) external {

	}

	function batchFlashLoan(IBatchFlashBorrower borrower, address[] calldata receivers,
        					IERC20[] calldata tokens, uint256[] calldata amounts,
							bytes calldata data) public virtual override {
	}
	

}