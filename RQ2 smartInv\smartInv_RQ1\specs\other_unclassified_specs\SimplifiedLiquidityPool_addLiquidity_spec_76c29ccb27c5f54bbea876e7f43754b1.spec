pragma solidity 0.8.0;

contract SimplifiedLiquidityPool {mapping(address => mapping(address => uint256)) public liquidityBalance;

function addLiquidity(address,uint256) public  {}

rule LiquidityAdditionConsistency() {
    address $token;
    address $user;
    uint256 $amount;
    uint256 initialBalance = liquidityBalance[$token][$user];
    addLiquidity($token, $amount);

    assert(liquidityBalance[$token][$user] == initialBalance + $amount);
}}