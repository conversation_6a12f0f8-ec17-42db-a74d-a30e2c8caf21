{"address": "0xa39d23bA9Aa6Bb842a2A7f90ac8c0075e53E6507", "abi": [{"constant": true, "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "proposals", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "proposer", "type": "address"}, {"internalType": "uint256", "name": "eta", "type": "uint256"}, {"internalType": "bool", "name": "executed", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "acceptAdmin", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "pauseCapital", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address[]", "name": "targets", "type": "address[]"}, {"internalType": "string[]", "name": "signatures", "type": "string[]"}, {"internalType": "bytes[]", "name": "calldatas", "type": "bytes[]"}, {"internalType": "string", "name": "description", "type": "string"}], "name": "propose", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "pendingAdmin", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "getActions", "outputs": [{"internalType": "address[]", "name": "targets", "type": "address[]"}, {"internalType": "string[]", "name": "signatures", "type": "string[]"}, {"internalType": "bytes[]", "name": "calldatas", "type": "bytes[]"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "state", "outputs": [{"internalType": "enum Governor.ProposalState", "name": "", "type": "uint8"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "cancel", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "pendingAdmin_", "type": "address"}], "name": "setPendingAdmin", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "MAX_OPERATIONS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "delay", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "MAXIMUM_DELAY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "setPendingAdminSign", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "unpauseCapital", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "MINIMUM_DELAY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "GRACE_PERIOD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "proposalCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "queue", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "delay_", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "queuedTransactions", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "admin", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "name": "execute", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin_", "type": "address"}, {"internalType": "uint256", "name": "delay_", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "proposer", "type": "address"}, {"indexed": false, "internalType": "address[]", "name": "targets", "type": "address[]"}, {"indexed": false, "internalType": "string[]", "name": "signatures", "type": "string[]"}, {"indexed": false, "internalType": "bytes[]", "name": "calldatas", "type": "bytes[]"}, {"indexed": false, "internalType": "string", "name": "description", "type": "string"}], "name": "ProposalCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "eta", "type": "uint256"}], "name": "ProposalQueued", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "id", "type": "uint256"}], "name": "ProposalExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "id", "type": "uint256"}], "name": "ProposalCancelled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "newAdmin", "type": "address"}], "name": "NewAdmin", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "newPendingAdmin", "type": "address"}], "name": "NewPendingAdmin", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "newDelay", "type": "uint256"}], "name": "NewDelay", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "txHash", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "target", "type": "address"}, {"indexed": false, "internalType": "string", "name": "signature", "type": "string"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}, {"indexed": false, "internalType": "uint256", "name": "eta", "type": "uint256"}], "name": "CancelTransaction", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "txHash", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "target", "type": "address"}, {"indexed": false, "internalType": "string", "name": "signature", "type": "string"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}, {"indexed": false, "internalType": "uint256", "name": "eta", "type": "uint256"}], "name": "ExecuteTransaction", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "txHash", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "target", "type": "address"}, {"indexed": false, "internalType": "string", "name": "signature", "type": "string"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}, {"indexed": false, "internalType": "uint256", "name": "eta", "type": "uint256"}], "name": "QueueTransaction", "type": "event"}], "transactionHash": "0x42f1404e103b51c703aacefdd8f3ddd87ad13a40218f0aab508c44be6507c5b6", "receipt": {"to": null, "from": "0xD85A569F3C26f81070544451131c742283360400", "contractAddress": "0xa39d23bA9Aa6Bb842a2A7f90ac8c0075e53E6507", "transactionIndex": 2, "gasUsed": "2554346", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x5eb01586b7a93f84a29832efdb9338be42fb494b032b74b50c786108a582b675", "transactionHash": "0x42f1404e103b51c703aacefdd8f3ddd87ad13a40218f0aab508c44be6507c5b6", "logs": [], "blockNumber": 7733923, "cumulativeGasUsed": "3953984", "status": 1, "byzantium": true}, "args": ["0xe0293212b84df3900BC24f9321e7721Aa7E50A3b", 60], "solcInputHash": "19364863fdc82a67c752b1e7be162d9b", "metadata": "{\"compiler\":{\"version\":\"0.5.11+commit.22be8592.mod\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"constant\":true,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"proposals\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"proposer\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"eta\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"executed\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"acceptAdmin\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"}],\"name\":\"pauseCapital\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"targets\",\"type\":\"address[]\"},{\"internalType\":\"string[]\",\"name\":\"signatures\",\"type\":\"string[]\"},{\"internalType\":\"bytes[]\",\"name\":\"calldatas\",\"type\":\"bytes[]\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"}],\"name\":\"propose\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"pendingAdmin\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"proposalId\",\"type\":\"uint256\"}],\"name\":\"getActions\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targets\",\"type\":\"address[]\"},{\"internalType\":\"string[]\",\"name\":\"signatures\",\"type\":\"string[]\"},{\"internalType\":\"bytes[]\",\"name\":\"calldatas\",\"type\":\"bytes[]\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"proposalId\",\"type\":\"uint256\"}],\"name\":\"state\",\"outputs\":[{\"internalType\":\"enum Governor.ProposalState\",\"name\":\"\",\"type\":\"uint8\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"proposalId\",\"type\":\"uint256\"}],\"name\":\"cancel\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"pendingAdmin_\",\"type\":\"address\"}],\"name\":\"setPendingAdmin\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"MAX_OPERATIONS\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"delay\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"MAXIMUM_DELAY\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"setPendingAdminSign\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"}],\"name\":\"unpauseCapital\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"MINIMUM_DELAY\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"GRACE_PERIOD\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"proposalCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"proposalId\",\"type\":\"uint256\"}],\"name\":\"queue\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"delay_\",\"type\":\"uint256\"}],\"name\":\"setDelay\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"queuedTransactions\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"admin\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"proposalId\",\"type\":\"uint256\"}],\"name\":\"execute\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"admin_\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"delay_\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"proposer\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"targets\",\"type\":\"address[]\"},{\"indexed\":false,\"internalType\":\"string[]\",\"name\":\"signatures\",\"type\":\"string[]\"},{\"indexed\":false,\"internalType\":\"bytes[]\",\"name\":\"calldatas\",\"type\":\"bytes[]\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"}],\"name\":\"ProposalCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"eta\",\"type\":\"uint256\"}],\"name\":\"ProposalQueued\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"}],\"name\":\"ProposalExecuted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"}],\"name\":\"ProposalCancelled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newAdmin\",\"type\":\"address\"}],\"name\":\"NewAdmin\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newPendingAdmin\",\"type\":\"address\"}],\"name\":\"NewPendingAdmin\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"newDelay\",\"type\":\"uint256\"}],\"name\":\"NewDelay\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"txHash\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"signature\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"eta\",\"type\":\"uint256\"}],\"name\":\"CancelTransaction\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"txHash\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"signature\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"eta\",\"type\":\"uint256\"}],\"name\":\"ExecuteTransaction\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"txHash\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"signature\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"eta\",\"type\":\"uint256\"}],\"name\":\"QueueTransaction\",\"type\":\"event\"}],\"devdoc\":{\"methods\":{\"cancel(uint256)\":{\"params\":{\"proposalId\":\"id of the proposal\"}},\"execute(uint256)\":{\"params\":{\"proposalId\":\"id of the proposal\"}},\"getActions(uint256)\":{\"params\":{\"proposalId\":\"id of the proposal\"}},\"propose(address[],string[],bytes[],string)\":{\"params\":{\"calldatas\":\"Orderded list of calldata to be passed with each call\",\"description\":\"Description of the governance\",\"signatures\":\"Orderd list of function signatures to be called\",\"targets\":\"Ordered list of targeted addresses\"},\"return\":\"uint256 id of the proposal\"},\"queue(uint256)\":{\"params\":{\"proposalId\":\"id of the proposal to queue\"}},\"state(uint256)\":{\"params\":{\"proposalId\":\"id of the proposal\"},\"return\":\"ProposalState\"}}},\"userdoc\":{\"methods\":{\"cancel(uint256)\":{\"notice\":\"Cancel a proposal.\"},\"execute(uint256)\":{\"notice\":\"Execute a proposal.\"},\"getActions(uint256)\":{\"notice\":\"Get the actions that a proposal will take.\"},\"propose(address[],string[],bytes[],string)\":{\"notice\":\"Propose Governance call(s)\"},\"queue(uint256)\":{\"notice\":\"Queue a proposal for execution\"},\"state(uint256)\":{\"notice\":\"Get the state of a proposal\"}}}},\"settings\":{\"compilationTarget\":{\"contracts/governance/Governor.sol\":\"Governor\"},\"evmVersion\":\"petersburg\",\"libraries\":{},\"metadata\":{\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts/math/SafeMath.sol\":{\"content\":\"pragma solidity ^0.5.0;\\n\\n/**\\n * @dev Wrappers over Solidity's arithmetic operations with added overflow\\n * checks.\\n *\\n * Arithmetic operations in Solidity wrap on overflow. This can easily result\\n * in bugs, because programmers usually assume that an overflow raises an\\n * error, which is the standard behavior in high level programming languages.\\n * `SafeMath` restores this intuition by reverting the transaction when an\\n * operation overflows.\\n *\\n * Using this library instead of the unchecked operations eliminates an entire\\n * class of bugs, so it's recommended to use it always.\\n */\\nlibrary SafeMath {\\n    /**\\n     * @dev Returns the addition of two unsigned integers, reverting on\\n     * overflow.\\n     *\\n     * Counterpart to Solidity's `+` operator.\\n     *\\n     * Requirements:\\n     * - Addition cannot overflow.\\n     */\\n    function add(uint256 a, uint256 b) internal pure returns (uint256) {\\n        uint256 c = a + b;\\n        require(c >= a, \\\"SafeMath: addition overflow\\\");\\n\\n        return c;\\n    }\\n\\n    /**\\n     * @dev Returns the subtraction of two unsigned integers, reverting on\\n     * overflow (when the result is negative).\\n     *\\n     * Counterpart to Solidity's `-` operator.\\n     *\\n     * Requirements:\\n     * - Subtraction cannot overflow.\\n     */\\n    function sub(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return sub(a, b, \\\"SafeMath: subtraction overflow\\\");\\n    }\\n\\n    /**\\n     * @dev Returns the subtraction of two unsigned integers, reverting with custom message on\\n     * overflow (when the result is negative).\\n     *\\n     * Counterpart to Solidity's `-` operator.\\n     *\\n     * Requirements:\\n     * - Subtraction cannot overflow.\\n     *\\n     * _Available since v2.4.0._\\n     */\\n    function sub(uint256 a, uint256 b, string memory errorMessage) internal pure returns (uint256) {\\n        require(b <= a, errorMessage);\\n        uint256 c = a - b;\\n\\n        return c;\\n    }\\n\\n    /**\\n     * @dev Returns the multiplication of two unsigned integers, reverting on\\n     * overflow.\\n     *\\n     * Counterpart to Solidity's `*` operator.\\n     *\\n     * Requirements:\\n     * - Multiplication cannot overflow.\\n     */\\n    function mul(uint256 a, uint256 b) internal pure returns (uint256) {\\n        // Gas optimization: this is cheaper than requiring 'a' not being zero, but the\\n        // benefit is lost if 'b' is also tested.\\n        // See: https://github.com/OpenZeppelin/openzeppelin-contracts/pull/522\\n        if (a == 0) {\\n            return 0;\\n        }\\n\\n        uint256 c = a * b;\\n        require(c / a == b, \\\"SafeMath: multiplication overflow\\\");\\n\\n        return c;\\n    }\\n\\n    /**\\n     * @dev Returns the integer division of two unsigned integers. Reverts on\\n     * division by zero. The result is rounded towards zero.\\n     *\\n     * Counterpart to Solidity's `/` operator. Note: this function uses a\\n     * `revert` opcode (which leaves remaining gas untouched) while Solidity\\n     * uses an invalid opcode to revert (consuming all remaining gas).\\n     *\\n     * Requirements:\\n     * - The divisor cannot be zero.\\n     */\\n    function div(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return div(a, b, \\\"SafeMath: division by zero\\\");\\n    }\\n\\n    /**\\n     * @dev Returns the integer division of two unsigned integers. Reverts with custom message on\\n     * division by zero. The result is rounded towards zero.\\n     *\\n     * Counterpart to Solidity's `/` operator. Note: this function uses a\\n     * `revert` opcode (which leaves remaining gas untouched) while Solidity\\n     * uses an invalid opcode to revert (consuming all remaining gas).\\n     *\\n     * Requirements:\\n     * - The divisor cannot be zero.\\n     *\\n     * _Available since v2.4.0._\\n     */\\n    function div(uint256 a, uint256 b, string memory errorMessage) internal pure returns (uint256) {\\n        // Solidity only automatically asserts when dividing by 0\\n        require(b > 0, errorMessage);\\n        uint256 c = a / b;\\n        // assert(a == b * c + a % b); // There is no case in which this doesn't hold\\n\\n        return c;\\n    }\\n\\n    /**\\n     * @dev Returns the remainder of dividing two unsigned integers. (unsigned integer modulo),\\n     * Reverts when dividing by zero.\\n     *\\n     * Counterpart to Solidity's `%` operator. This function uses a `revert`\\n     * opcode (which leaves remaining gas untouched) while Solidity uses an\\n     * invalid opcode to revert (consuming all remaining gas).\\n     *\\n     * Requirements:\\n     * - The divisor cannot be zero.\\n     */\\n    function mod(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return mod(a, b, \\\"SafeMath: modulo by zero\\\");\\n    }\\n\\n    /**\\n     * @dev Returns the remainder of dividing two unsigned integers. (unsigned integer modulo),\\n     * Reverts with custom message when dividing by zero.\\n     *\\n     * Counterpart to Solidity's `%` operator. This function uses a `revert`\\n     * opcode (which leaves remaining gas untouched) while Solidity uses an\\n     * invalid opcode to revert (consuming all remaining gas).\\n     *\\n     * Requirements:\\n     * - The divisor cannot be zero.\\n     *\\n     * _Available since v2.4.0._\\n     */\\n    function mod(uint256 a, uint256 b, string memory errorMessage) internal pure returns (uint256) {\\n        require(b != 0, errorMessage);\\n        return a % b;\\n    }\\n}\\n\",\"keccak256\":\"0x640b6dee7a4b830bdfd52b5031a07fc2b12209f5b2e29e5d364a7d37f69d8076\"},\"contracts/governance/Governor.sol\":{\"content\":\"pragma solidity 0.5.11;\\npragma experimental ABIEncoderV2;\\n\\nimport \\\"./../timelock/Timelock.sol\\\";\\n\\n// Modeled off of Compound's Governor Alpha\\n//    https://github.com/compound-finance/compound-protocol/blob/master/contracts/Governance/GovernorAlpha.sol\\ncontract Governor is Timelock {\\n    // @notice The total number of proposals\\n    uint256 public proposalCount;\\n\\n    struct Proposal {\\n        // @notice Unique id for looking up a proposal\\n        uint256 id;\\n        // @notice Creator of the proposal\\n        address proposer;\\n        // @notice The timestamp that the proposal will be available for\\n        // execution, set once the vote succeeds\\n        uint256 eta;\\n        // @notice the ordered list of target addresses for calls to be made\\n        address[] targets;\\n        // @notice The ordered list of function signatures to be called\\n        string[] signatures;\\n        // @notice The ordered list of calldata to be passed to each call\\n        bytes[] calldatas;\\n        // @notice Flag marking whether the proposal has been executed\\n        bool executed;\\n    }\\n\\n    // @notice The official record of all proposals ever proposed\\n    mapping(uint256 => Proposal) public proposals;\\n\\n    // @notice An event emitted when a new proposal is created\\n    event ProposalCreated(\\n        uint256 id,\\n        address proposer,\\n        address[] targets,\\n        string[] signatures,\\n        bytes[] calldatas,\\n        string description\\n    );\\n\\n    // @notice An event emitted when a proposal has been queued in the Timelock\\n    event ProposalQueued(uint256 id, uint256 eta);\\n\\n    // @notice An event emitted when a proposal has been executed in the Timelock\\n    event ProposalExecuted(uint256 id);\\n\\n    // @notice An event emitted when a proposal has been cancelled\\n    event ProposalCancelled(uint256 id);\\n\\n    uint256 public constant MAX_OPERATIONS = 16;\\n\\n    // @notice Possible states that a proposal may be in\\n    enum ProposalState { Pending, Queued, Expired, Executed }\\n\\n    /**\\n     * @dev Throws if called by any account other than the Admin.\\n     */\\n    modifier onlyAdmin() {\\n        require(msg.sender == admin, \\\"Caller is not the admin\\\");\\n        _;\\n    }\\n\\n    bytes32 public constant setPendingAdminSign = keccak256(\\n        bytes(\\\"setPendingAdmin(address)\\\")\\n    );\\n\\n    constructor(address admin_, uint256 delay_)\\n        public\\n        Timelock(admin_, delay_)\\n    {}\\n\\n    /**\\n     * @notice Propose Governance call(s)\\n     * @param targets Ordered list of targeted addresses\\n     * @param signatures Orderd list of function signatures to be called\\n     * @param calldatas Orderded list of calldata to be passed with each call\\n     * @param description Description of the governance\\n     * @return uint256 id of the proposal\\n     */\\n    function propose(\\n        address[] memory targets,\\n        string[] memory signatures,\\n        bytes[] memory calldatas,\\n        string memory description\\n    ) public returns (uint256) {\\n        // Allow anyone to propose for now, since only admin can queue the\\n        // transaction it should be harmless, you just need to pay the gas\\n        require(\\n            targets.length == signatures.length &&\\n                targets.length == calldatas.length,\\n            \\\"Governor::propose: proposal function information arity mismatch\\\"\\n        );\\n        require(targets.length != 0, \\\"Governor::propose: must provide actions\\\");\\n        require(\\n            targets.length <= MAX_OPERATIONS,\\n            \\\"Governor::propose: too many actions\\\"\\n        );\\n\\n        for (uint256 i = 0; i < signatures.length; i++) {\\n            require(\\n                keccak256(bytes(signatures[i])) != setPendingAdminSign,\\n                \\\"Governor::propose: setPendingAdmin transaction cannot be proposed or queued\\\"\\n            );\\n        }\\n\\n        proposalCount++;\\n        Proposal memory newProposal = Proposal({\\n            id: proposalCount,\\n            proposer: msg.sender,\\n            eta: 0,\\n            targets: targets,\\n            signatures: signatures,\\n            calldatas: calldatas,\\n            executed: false\\n        });\\n\\n        proposals[newProposal.id] = newProposal;\\n\\n        emit ProposalCreated(\\n            newProposal.id,\\n            msg.sender,\\n            targets,\\n            signatures,\\n            calldatas,\\n            description\\n        );\\n        return newProposal.id;\\n    }\\n\\n    /**\\n     * @notice Queue a proposal for execution\\n     * @param proposalId id of the proposal to queue\\n     */\\n    function queue(uint256 proposalId) public onlyAdmin {\\n        require(\\n            state(proposalId) == ProposalState.Pending,\\n            \\\"Governor::queue: proposal can only be queued if it is pending\\\"\\n        );\\n        Proposal storage proposal = proposals[proposalId];\\n        proposal.eta = block.timestamp.add(delay);\\n\\n        for (uint256 i = 0; i < proposal.targets.length; i++) {\\n            _queueOrRevert(\\n                proposal.targets[i],\\n                proposal.signatures[i],\\n                proposal.calldatas[i],\\n                proposal.eta\\n            );\\n        }\\n\\n        emit ProposalQueued(proposal.id, proposal.eta);\\n    }\\n\\n    /**\\n     * @notice Get the state of a proposal\\n     * @param proposalId id of the proposal\\n     * @return ProposalState\\n     */\\n    function state(uint256 proposalId) public view returns (ProposalState) {\\n        require(\\n            proposalCount >= proposalId && proposalId > 0,\\n            \\\"Governor::state: invalid proposal id\\\"\\n        );\\n        Proposal storage proposal = proposals[proposalId];\\n        if (proposal.executed) {\\n            return ProposalState.Executed;\\n        } else if (proposal.eta == 0) {\\n            return ProposalState.Pending;\\n        } else if (block.timestamp >= proposal.eta.add(GRACE_PERIOD)) {\\n            return ProposalState.Expired;\\n        } else {\\n            return ProposalState.Queued;\\n        }\\n    }\\n\\n    function _queueOrRevert(\\n        address target,\\n        string memory signature,\\n        bytes memory data,\\n        uint256 eta\\n    ) internal {\\n        require(\\n            !queuedTransactions[keccak256(\\n                abi.encode(target, signature, keccak256(data), eta)\\n            )],\\n            \\\"Governor::_queueOrRevert: proposal action already queued at eta\\\"\\n        );\\n        require(\\n            queuedTransactions[queueTransaction(target, signature, data, eta)],\\n            \\\"Governor::_queueOrRevert: failed to queue transaction\\\"\\n        );\\n    }\\n\\n    /**\\n     * @notice Execute a proposal.\\n     * @param proposalId id of the proposal\\n     */\\n    function execute(uint256 proposalId) public {\\n        require(\\n            state(proposalId) == ProposalState.Queued,\\n            \\\"Governor::execute: proposal can only be executed if it is queued\\\"\\n        );\\n        Proposal storage proposal = proposals[proposalId];\\n        proposal.executed = true;\\n        for (uint256 i = 0; i < proposal.targets.length; i++) {\\n            executeTransaction(\\n                proposal.targets[i],\\n                proposal.signatures[i],\\n                proposal.calldatas[i],\\n                proposal.eta\\n            );\\n        }\\n        emit ProposalExecuted(proposalId);\\n    }\\n\\n    /**\\n     * @notice Cancel a proposal.\\n     * @param proposalId id of the proposal\\n     */\\n    function cancel(uint256 proposalId) public onlyAdmin {\\n        ProposalState proposalState = state(proposalId);\\n\\n        require(\\n            proposalState == ProposalState.Queued ||\\n                proposalState == ProposalState.Pending,\\n            \\\"Governor::execute: proposal can only be cancelled if it is queued or pending\\\"\\n        );\\n        Proposal storage proposal = proposals[proposalId];\\n        proposal.eta = 1; // To mark the proposal as `Expired`\\n        for (uint256 i = 0; i < proposal.targets.length; i++) {\\n            cancelTransaction(\\n                proposal.targets[i],\\n                proposal.signatures[i],\\n                proposal.calldatas[i],\\n                proposal.eta\\n            );\\n        }\\n        emit ProposalCancelled(proposalId);\\n    }\\n\\n    /**\\n     * @notice Get the actions that a proposal will take.\\n     * @param proposalId id of the proposal\\n     */\\n    function getActions(uint256 proposalId)\\n        public\\n        view\\n        returns (\\n            address[] memory targets,\\n            string[] memory signatures,\\n            bytes[] memory calldatas\\n        )\\n    {\\n        Proposal storage p = proposals[proposalId];\\n        return (p.targets, p.signatures, p.calldatas);\\n    }\\n}\\n\",\"keccak256\":\"0xac74907b2ac8188141e59953d4234008a0af46d3c46023f974b0c5413779edee\"},\"contracts/timelock/Timelock.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\n/**\\n * @title OUSD Timelock Contract\\n * <AUTHOR> Protocol Inc\\n */\\nimport \\\"@openzeppelin/contracts/math/SafeMath.sol\\\";\\n\\ninterface CapitalPausable {\\n    function pauseCapital() external;\\n\\n    function unpauseCapital() external;\\n}\\n\\ncontract Timelock {\\n    using SafeMath for uint256;\\n\\n    event NewAdmin(address indexed newAdmin);\\n    event NewPendingAdmin(address indexed newPendingAdmin);\\n    event NewDelay(uint256 indexed newDelay);\\n    event CancelTransaction(\\n        bytes32 indexed txHash,\\n        address indexed target,\\n        string signature,\\n        bytes data,\\n        uint256 eta\\n    );\\n    event ExecuteTransaction(\\n        bytes32 indexed txHash,\\n        address indexed target,\\n        string signature,\\n        bytes data,\\n        uint256 eta\\n    );\\n    event QueueTransaction(\\n        bytes32 indexed txHash,\\n        address indexed target,\\n        string signature,\\n        bytes data,\\n        uint256 eta\\n    );\\n\\n    uint256 public constant GRACE_PERIOD = 3 days;\\n    uint256 public constant MINIMUM_DELAY = 1 minutes;\\n    uint256 public constant MAXIMUM_DELAY = 2 days;\\n\\n    address public admin;\\n    address public pendingAdmin;\\n    uint256 public delay;\\n\\n    mapping(bytes32 => bool) public queuedTransactions;\\n\\n    constructor(address admin_, uint256 delay_) public {\\n        require(\\n            delay_ >= MINIMUM_DELAY,\\n            \\\"Timelock::constructor: Delay must exceed minimum delay.\\\"\\n        );\\n        require(\\n            delay_ <= MAXIMUM_DELAY,\\n            \\\"Timelock::setDelay: Delay must not exceed maximum delay.\\\"\\n        );\\n\\n        admin = admin_;\\n        delay = delay_;\\n    }\\n\\n    function setDelay(uint256 delay_) public {\\n        require(\\n            msg.sender == address(this),\\n            \\\"Timelock::setDelay: Call must come from Timelock.\\\"\\n        );\\n        require(\\n            delay_ >= MINIMUM_DELAY,\\n            \\\"Timelock::setDelay: Delay must exceed minimum delay.\\\"\\n        );\\n        require(\\n            delay_ <= MAXIMUM_DELAY,\\n            \\\"Timelock::setDelay: Delay must not exceed maximum delay.\\\"\\n        );\\n        delay = delay_;\\n\\n        emit NewDelay(delay);\\n    }\\n\\n    function acceptAdmin() public {\\n        require(\\n            msg.sender == pendingAdmin,\\n            \\\"Timelock::acceptAdmin: Call must come from pendingAdmin.\\\"\\n        );\\n        admin = msg.sender;\\n        pendingAdmin = address(0);\\n\\n        emit NewAdmin(admin);\\n    }\\n\\n    function setPendingAdmin(address pendingAdmin_) public {\\n        require(\\n            msg.sender == address(this),\\n            \\\"Timelock::setPendingAdmin: Call must come from Timelock.\\\"\\n        );\\n        pendingAdmin = pendingAdmin_;\\n\\n        emit NewPendingAdmin(pendingAdmin);\\n    }\\n\\n    function queueTransaction(\\n        address target,\\n        string memory signature,\\n        bytes memory data,\\n        uint256 eta\\n    ) internal returns (bytes32) {\\n        require(\\n            msg.sender == admin,\\n            \\\"Timelock::queueTransaction: Call must come from admin.\\\"\\n        );\\n        require(\\n            eta >= getBlockTimestamp().add(delay),\\n            \\\"Timelock::queueTransaction: Estimated execution block must satisfy delay.\\\"\\n        );\\n\\n        bytes32 txHash = keccak256(\\n            abi.encode(target, signature, keccak256(data), eta)\\n        );\\n        queuedTransactions[txHash] = true;\\n\\n        emit QueueTransaction(txHash, target, signature, data, eta);\\n        return txHash;\\n    }\\n\\n    function cancelTransaction(\\n        address target,\\n        string memory signature,\\n        bytes memory data,\\n        uint256 eta\\n    ) internal {\\n        require(\\n            msg.sender == admin,\\n            \\\"Timelock::cancelTransaction: Call must come from admin.\\\"\\n        );\\n\\n        bytes32 txHash = keccak256(\\n            abi.encode(target, signature, keccak256(data), eta)\\n        );\\n        queuedTransactions[txHash] = false;\\n\\n        emit CancelTransaction(txHash, target, signature, data, eta);\\n    }\\n\\n    function executeTransaction(\\n        address target,\\n        string memory signature,\\n        bytes memory data,\\n        uint256 eta\\n    ) internal returns (bytes memory) {\\n        require(\\n            msg.sender == admin,\\n            \\\"Timelock::executeTransaction: Call must come from admin.\\\"\\n        );\\n\\n        bytes32 txHash = keccak256(\\n            abi.encode(target, signature, keccak256(data), eta)\\n        );\\n        require(\\n            queuedTransactions[txHash],\\n            \\\"Timelock::executeTransaction: Transaction hasn't been queued.\\\"\\n        );\\n        require(\\n            getBlockTimestamp() >= eta,\\n            \\\"Timelock::executeTransaction: Transaction hasn't surpassed time lock.\\\"\\n        );\\n        require(\\n            getBlockTimestamp() <= eta.add(GRACE_PERIOD),\\n            \\\"Timelock::executeTransaction: Transaction is stale.\\\"\\n        );\\n\\n        queuedTransactions[txHash] = false;\\n\\n        bytes memory callData;\\n\\n        if (bytes(signature).length == 0) {\\n            callData = data;\\n        } else {\\n            callData = abi.encodePacked(\\n                bytes4(keccak256(bytes(signature))),\\n                data\\n            );\\n        }\\n\\n        (bool success, bytes memory returnData) = target.call(callData);\\n        require(\\n            success,\\n            \\\"Timelock::executeTransaction: Transaction execution reverted.\\\"\\n        );\\n\\n        emit ExecuteTransaction(txHash, target, signature, data, eta);\\n\\n        return returnData;\\n    }\\n\\n    function getBlockTimestamp() internal view returns (uint256) {\\n        // solium-disable-next-line security/no-block-members\\n        return block.timestamp;\\n    }\\n\\n    function pauseCapital(address target) external {\\n        require(\\n            msg.sender == admin,\\n            \\\"Timelock::pauseCapital: Call must come from admin.\\\"\\n        );\\n        CapitalPausable(target).pauseCapital();\\n    }\\n\\n    function unpauseCapital(address target) external {\\n        require(\\n            msg.sender == admin,\\n            \\\"Timelock::unpauseCapital: Call must come from admin.\\\"\\n        );\\n        CapitalPausable(target).unpauseCapital();\\n    }\\n}\\n\",\"keccak256\":\"0xb27e38e59a483ba494ce1161a2912041925603ef11f394dae3192791b67bc44b\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"methods": {"cancel(uint256)": {"params": {"proposalId": "id of the proposal"}}, "execute(uint256)": {"params": {"proposalId": "id of the proposal"}}, "getActions(uint256)": {"params": {"proposalId": "id of the proposal"}}, "propose(address[],string[],bytes[],string)": {"params": {"calldatas": "Orderded list of calldata to be passed with each call", "description": "Description of the governance", "signatures": "Orderd list of function signatures to be called", "targets": "Ordered list of targeted addresses"}, "return": "uint256 id of the proposal"}, "queue(uint256)": {"params": {"proposalId": "id of the proposal to queue"}}, "state(uint256)": {"params": {"proposalId": "id of the proposal"}, "return": "ProposalState"}}}, "userdoc": {"methods": {"cancel(uint256)": {"notice": "Cancel a proposal."}, "execute(uint256)": {"notice": "Execute a proposal."}, "getActions(uint256)": {"notice": "Get the actions that a proposal will take."}, "propose(address[],string[],bytes[],string)": {"notice": "Propose Governance call(s)"}, "queue(uint256)": {"notice": "Queue a proposal for execution"}, "state(uint256)": {"notice": "Get the state of a proposal"}}}}