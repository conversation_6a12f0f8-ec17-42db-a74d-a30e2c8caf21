pragma solidity 0.8.0;

contract VulnerableBancor{mapping(address => mapping(address => uint256)) public allowance;
mapping(address => uint256) public balanceOf;
function transferFrom(address,address,uint256) public returns(bool) 
precondition{
balanceOf[from] >= value ? true : false; 
allowance[from][msg.sender] >= value ? true : false;
}

postcondition{
balanceOf[from] == __old__(balanceOf[from]) - value ? true : false; 
balanceOf[to] == __old__(balanceOf[to]) + value ? true : false; 
allowance[from][msg.sender] == __old__(allowance[from][msg.sender]) - value ? true : false;
}
}