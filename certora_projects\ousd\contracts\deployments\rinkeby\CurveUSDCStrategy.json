{"abi": [{"constant": false, "inputs": [], "name": "collectRewardToken", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "governor", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_asset", "type": "address"}, {"internalType": "address", "name": "_pToken", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>dd<PERSON>", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "assetToPToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_asset", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "transferToken", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "rewardT<PERSON><PERSON><PERSON>ress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "liquidate", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_platformAddress", "type": "address"}, {"internalType": "address", "name": "_vault<PERSON><PERSON>ress", "type": "address"}, {"internalType": "address", "name": "_rewardT<PERSON><PERSON><PERSON>ress", "type": "address"}, {"internalType": "address", "name": "_asset", "type": "address"}, {"internalType": "address", "name": "_pToken", "type": "address"}, {"internalType": "address", "name": "_crvGaugeAddress", "type": "address"}, {"internalType": "address", "name": "_crvMinterAddress", "type": "address"}], "name": "initialize", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_asset", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "deposit", "outputs": [{"internalType": "uint256", "name": "amountDeposited", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "rewardLiquidationThreshold", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "claimGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "_asset", "type": "address"}], "name": "checkBalance", "outputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_platformAddress", "type": "address"}, {"internalType": "address", "name": "_vault<PERSON><PERSON>ress", "type": "address"}, {"internalType": "address", "name": "_rewardT<PERSON><PERSON><PERSON>ress", "type": "address"}, {"internalType": "address[]", "name": "_assets", "type": "address[]"}, {"internalType": "address[]", "name": "_pTokens", "type": "address[]"}], "name": "initialize", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_rewardT<PERSON><PERSON><PERSON>ress", "type": "address"}], "name": "setRewardTokenAddress", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "_asset", "type": "address"}], "name": "supportsAsset", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "safeApproveAllTokens", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "isGovernor", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "_threshold", "type": "uint256"}], "name": "setRewardLiquidationThreshold", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_newGovernor", "type": "address"}], "name": "transferGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_recipient", "type": "address"}, {"internalType": "address", "name": "_asset", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "withdraw", "outputs": [{"internalType": "uint256", "name": "amountWithdrawn", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "platformAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "RewardTokenCollected", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_asset", "type": "address"}, {"indexed": false, "internalType": "address", "name": "_pToken", "type": "address"}], "name": "PTokenAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_asset", "type": "address"}, {"indexed": false, "internalType": "address", "name": "_pToken", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_asset", "type": "address"}, {"indexed": false, "internalType": "address", "name": "_pToken", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "PendingGovernorshipTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "GovernorshipTransferred", "type": "event"}], "receipt": {"to": null, "from": "0xD85A569F3C26f81070544451131c742283360400", "contractAddress": "0xCd3E3718934d7B1fE921E014cC7c25e911fCE9e8", "transactionIndex": 4, "gasUsed": "3622170", "logsBloom": "0x00000000400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000800000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000008000000000000000000000000000000000000000000000010000000000000000000000000000000000000000020000000000000000000000000000008000000000400000000000000000000000000", "blockHash": "0xe4531e09767cbc6fbc87c453455052f22685ccfb21d7ff816626f4609d297efc", "transactionHash": "0xbe0b02ee5602fd6be681960ade6ea43dde515d7154a376b432d7cdf615ceab57", "logs": [{"transactionIndex": 4, "blockNumber": 7375835, "transactionHash": "0xbe0b02ee5602fd6be681960ade6ea43dde515d7154a376b432d7cdf615ceab57", "address": "0xCd3E3718934d7B1fE921E014cC7c25e911fCE9e8", "topics": ["0xc7c0c772add429241571afb3805861fb3cfa2af374534088b76cdb4325a87e9a", "0x0000000000000000000000000000000000000000000000000000000000000000", "0x000000000000000000000000d85a569f3c26f81070544451131c742283360400"], "data": "0x", "logIndex": 7, "blockHash": "0xe4531e09767cbc6fbc87c453455052f22685ccfb21d7ff816626f4609d297efc"}], "blockNumber": 7375835, "cumulativeGasUsed": "4127870", "status": 1, "byzantium": true}, "address": "0xCd3E3718934d7B1fE921E014cC7c25e911fCE9e8", "args": [], "solcInputHash": "0xb96a2ce4fb4e8910fdbe72e74d6786fd3df1fe6a549cc5d3e559df16aac11572", "metadata": "{\"compiler\":{\"version\":\"0.5.11+commit.c082d0b4\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"constant\":false,\"inputs\":[],\"name\":\"collectRewardToken\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"governor\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_pToken\",\"type\":\"address\"}],\"name\":\"setPTokenAddress\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"assetToPToken\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"transferToken\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"rewardTokenAddress\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"liquidate\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_platformAddress\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_vaultAddress\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_rewardTokenAddress\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_pToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_crvGaugeAddress\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_crvMinterAddress\",\"type\":\"address\"}],\"name\":\"initialize\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"vaultAddress\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"deposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"amountDeposited\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"rewardLiquidationThreshold\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"claimGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"}],\"name\":\"checkBalance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_platformAddress\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_vaultAddress\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_rewardTokenAddress\",\"type\":\"address\"},{\"internalType\":\"address[]\",\"name\":\"_assets\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"_pTokens\",\"type\":\"address[]\"}],\"name\":\"initialize\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_rewardTokenAddress\",\"type\":\"address\"}],\"name\":\"setRewardTokenAddress\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"}],\"name\":\"supportsAsset\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"safeApproveAllTokens\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"isGovernor\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_threshold\",\"type\":\"uint256\"}],\"name\":\"setRewardLiquidationThreshold\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_newGovernor\",\"type\":\"address\"}],\"name\":\"transferGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_recipient\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"withdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"amountWithdrawn\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"platformAddress\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"RewardTokenCollected\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"_pToken\",\"type\":\"address\"}],\"name\":\"PTokenAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"_pToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"Deposit\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_asset\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"_pToken\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"Withdrawal\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"PendingGovernorshipTransfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"GovernorshipTransferred\",\"type\":\"event\"}],\"devdoc\":{\"methods\":{\"checkBalance(address)\":{\"details\":\"Get the total asset value held in the platform This includes any interest that was generated since depositing We calculate this by calculating a what we would get if we liquidated the allocated percentage of this asset.\",\"params\":{\"_asset\":\"Address of the asset\"},\"return\":\"balance    Total value of the asset in the platform\"},\"claimGovernance()\":{\"details\":\"Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor.\"},\"collectRewardToken()\":{\"details\":\"Collect accumulated CRV and send to Vault.\"},\"deposit(address,uint256)\":{\"details\":\"Deposit asset into the Curve 3Pool\",\"params\":{\"_amount\":\"Amount of asset to deposit\",\"_asset\":\"Address of asset to deposit\"},\"return\":\"amountDeposited Amount of asset that was deposited\"},\"governor()\":{\"details\":\"Returns the address of the current Governor.\"},\"initialize(address,address,address,address,address,address,address)\":{\"params\":{\"_asset\":\"Address of the supported asset\",\"_crvGaugeAddress\":\"Address of the Curve DAO gauge for this pool\",\"_crvMinterAddress\":\"Address of the CRV minter for rewards\",\"_pToken\":\"Correspond platform token addres (i.e. 3Crv)\",\"_platformAddress\":\"Address of the Curve 3pool\",\"_rewardTokenAddress\":\"Address of CRV\",\"_vaultAddress\":\"Address of the vault\"}},\"initialize(address,address,address,address[],address[])\":{\"details\":\"Internal initialize function, to set up initial internal state\",\"params\":{\"_assets\":\"Addresses of initial supported assets\",\"_pTokens\":\"Platform Token corresponding addresses\",\"_platformAddress\":\"jGeneric platform address\",\"_rewardTokenAddress\":\"Address of reward token for platform\",\"_vaultAddress\":\"Address of the Vault\"}},\"isGovernor()\":{\"details\":\"Returns true if the caller is the current Governor.\"},\"liquidate()\":{\"details\":\"Remove all assets from platform and send them to Vault contract.\"},\"safeApproveAllTokens()\":{\"details\":\"Approve the spending of all assets by their corresponding pool tokens,     if for some reason is it necessary.\"},\"setPTokenAddress(address,address)\":{\"details\":\"Provide support for asset by passing its pToken address.     This method can only be called by the system Governor\",\"params\":{\"_asset\":\"Address for the asset\",\"_pToken\":\"Address for the corresponding platform token\"}},\"setRewardLiquidationThreshold(uint256)\":{\"details\":\"Set the reward token liquidation threshold.\",\"params\":{\"_threshold\":\"Threshold amount in decimals of reward token that will cause the Vault to claim and liquidate on allocate() calls.\"}},\"setRewardTokenAddress(address)\":{\"details\":\"Set the reward token address.\",\"params\":{\"_rewardTokenAddress\":\"Address of the reward token\"}},\"supportsAsset(address)\":{\"details\":\"Retuns bool indicating whether asset is supported by strategy\",\"params\":{\"_asset\":\"Address of the asset\"}},\"transferGovernance(address)\":{\"details\":\"Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete\",\"params\":{\"_newGovernor\":\"Address of the new Governor\"}},\"transferToken(address,uint256)\":{\"details\":\"Transfer token to governor. Intended for recovering tokens stuck in     strategy contracts, i.e. mistaken sends.\",\"params\":{\"_amount\":\"Amount of the asset to transfer\",\"_asset\":\"Address for the asset\"}},\"withdraw(address,address,uint256)\":{\"details\":\"Withdraw asset from Curve 3Pool\",\"params\":{\"_amount\":\"Amount of asset to withdraw\",\"_asset\":\"Address of asset to withdraw\",\"_recipient\":\"Address to receive withdrawn asset\"},\"return\":\"amountWithdrawn Amount of asset that was withdrawn\"}}},\"userdoc\":{\"methods\":{\"initialize(address,address,address,address,address,address,address)\":{\"notice\":\"Initializer for setting up strategy internal state. This overrides the InitializableAbstractStrategy initializer as Curve strategies don't fit well within that abstraction.\"}}}},\"settings\":{\"compilationTarget\":{\"contracts/strategies/ThreePoolStrategy.sol\":\"ThreePoolStrategy\"},\"evmVersion\":\"petersburg\",\"libraries\":{},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts/math/SafeMath.sol\":{\"keccak256\":\"0x640b6dee7a4b830bdfd52b5031a07fc2b12209f5b2e29e5d364a7d37f69d8076\",\"urls\":[\"bzz-raw://31113152e1ddb78fe7a4197f247591ca894e93f916867beb708d8e747b6cc74f\",\"dweb:/ipfs/QmbZaJyXdpsYGykVhHH9qpVGQg9DGCxE2QufbCUy3daTgq\"]},\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xe5bb0f57cff3e299f360052ba50f1ea0fff046df2be070b6943e0e3c3fdad8a9\",\"urls\":[\"bzz-raw://59fd025151435da35faa8093a5c7a17de02de9d08ad27275c5cdf05050820d91\",\"dweb:/ipfs/QmQMvwEcPhoRXzbXyrdoeRtvLoifUW9Qh7Luho7bmUPRkc\"]},\"@openzeppelin/contracts/token/ERC20/SafeERC20.sol\":{\"keccak256\":\"0x6f2c9955d65c522b80f4b8792f076512d2df947d2112cbc4d98a4781ed42ede2\",\"urls\":[\"bzz-raw://7d8ec81683520c06baeef3f7e06cd82bd6fd5fa611f26857f475f6c829540aff\",\"dweb:/ipfs/QmTDkFzKnrpiV1UKnSoiZAHPuguWzokrr4pFbSPvyaSo56\"]},\"@openzeppelin/contracts/utils/Address.sol\":{\"keccak256\":\"0x1a8e5072509c5ea7365eb1d48030b9be865140c8fb779968da0a459a0e174a11\",\"urls\":[\"bzz-raw://03335b7b07c7c8c8d613cfdd8ec39a0b5ec133ee510bf2fe6cc5a496767bef4b\",\"dweb:/ipfs/Qmebp4nzPja645c9yXSdJkGq96oU3am3LUnG2K3R7XxyKf\"]},\"@openzeppelin/upgrades/contracts/Initializable.sol\":{\"keccak256\":\"0x9bfec92e36234ecc99b5d37230acb6cd1f99560233753162204104a4897e8721\",\"urls\":[\"bzz-raw://5cf7c208583d4d046d75bd99f5507412ab01cce9dd9f802ce9768a416d93ea2f\",\"dweb:/ipfs/QmcQS1BBMPpVEkXP3qzwSjxHNrqDek8YeR7xbVWDC9ApC7\"]},\"contracts/governance/Governable.sol\":{\"keccak256\":\"0x342fa1b2e2cbe8d2d904c31e5a2b182446d3737db2f6704d3f247c6c733084ec\",\"urls\":[\"bzz-raw://93918076cf5ef49658f8dd78ac7aafe30a1e233cc49e70a642ee9559b78c3f28\",\"dweb:/ipfs/Qmcca1ncSQnxSRXs5FZHzH9dwfZ1KcuUfcAiBwgkbzfTeW\"]},\"contracts/interfaces/IBasicToken.sol\":{\"keccak256\":\"0x01eab42b6d54fa5389598e0663c24680ecc017e2da848e8ea1c40aeaa8225eef\",\"urls\":[\"bzz-raw://02670b5ea9f966c1f989a3a78ecca8d6c9898a8ff1f9886c287e0f669706afb1\",\"dweb:/ipfs/QmbdjDcqbP1fwe5AZG1o6HLwVbMxvUXJXrVtddNB5hCLMS\"]},\"contracts/strategies/ICRVMinter.sol\":{\"keccak256\":\"0xcb83e2b54eed241e5957ec27dcbdc1b6305c66d0c65600e1ba6b29c82f58930b\",\"urls\":[\"bzz-raw://58ee3bd5260006196b31628786920adc7c182444da3f17542a321391457b0994\",\"dweb:/ipfs/QmPsSDYNViVAbXYCByFQyrivq4gJ2kwEZzkTRoGE2kRbkt\"]},\"contracts/strategies/ICurveGauge.sol\":{\"keccak256\":\"0x3a007f258521d7321edce1c54e8e7a78cfbdd472d9180121b319420f525a79a4\",\"urls\":[\"bzz-raw://447e9f0106c584c75a2493f045d4cebb7bce30d9751af2e4597796ff7dbfc7d2\",\"dweb:/ipfs/QmZy8xaL4KSyJryYCWBq6bwFZM9xos5pTqK5akbT7ZLX6x\"]},\"contracts/strategies/ICurvePool.sol\":{\"keccak256\":\"0x6785637964012b55891b698e9f77ea5e80a5b749095afc8c648d0beec84ff228\",\"urls\":[\"bzz-raw://2b0d082a7cb8eba4f5719c77fea7b9017cc4ce368e7ae31cee2d0b49d8599efb\",\"dweb:/ipfs/QmRxvCgqtbR3mYnSzHztkzHKBDVjjUnfA7mJB5z4Ndj5Gh\"]},\"contracts/strategies/ThreePoolStrategy.sol\":{\"keccak256\":\"0xbf22021baae3b0be439b34acb6cadf7d84b66f5d953ab72a28bc57ba313d7c22\",\"urls\":[\"bzz-raw://6b3c933d6c15db1d68f151f3981ccd8a9caf954210431188062a60da5f6a96e8\",\"dweb:/ipfs/QmZefbzMomDKnfxrxaEMJ49v7KLDnhxjfwFJr8yX8s3Y9i\"]},\"contracts/utils/Helpers.sol\":{\"keccak256\":\"0xd2ca92e0af883dc1aec5b22caced274e59829e0e30a9e955dcc48b8d921f5cdc\",\"urls\":[\"bzz-raw://90a369ed17c35dfbb4bc6dd98d767b703f35687c28b18f34153744f494fb1ef2\",\"dweb:/ipfs/QmSe65R6r8RUgGvP1LinrH4ZycsgMQpKdcG83RG5NwmqRN\"]},\"contracts/utils/InitializableAbstractStrategy.sol\":{\"keccak256\":\"0xbd59f9f96b0fb3f3eaee7ee46d3e32f39c79a8d00c28361c1cb9423ff43d3cc5\",\"urls\":[\"bzz-raw://478903e83760f42e70c7430de5b6252954f80ef8d40c4cb47211033be5806507\",\"dweb:/ipfs/Qmc8vs5QLMyXBXDBD3cKRy5FWuFqX8KD1xKPrW3PvBXgcK\"]}},\"version\":1}", "bytecode": "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", "deployedBytecode": "0x608060405234801561001057600080fd5b50600436106101425760003560e01c80635d36b190116100b8578063ad1728cb1161007c578063ad1728cb14610723578063c7af33521461072d578063cd3b02121461074f578063d38bfff41461077d578063d9caed12146107c1578063dbe55e561461084357610142565b80635d36b190146104f35780635f515226146104fd578063790fcf9f146105555780639a6acf2014610683578063aa388af6146106c757610142565b8063125f9e331161010a578063125f9e33146102d157806328a070251461031b5780633587647614610325578063430bf08a1461042957806347e7ef24146104735780635653b414146104d557610142565b80630242241d146101475780630c340a24146101515780630ed57b3a1461019b5780630fc3b4c4146101ff5780631072cbea14610283575b600080fd5b61014f61088d565b005b610159610cfb565b604051808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060405180910390f35b6101fd600480360360408110156101b157600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803573ffffffffffffffffffffffffffffffffffffffff169060200190929190505050610d0a565b005b6102416004803603602081101561021557600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190505050610d92565b604051808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060405180910390f35b6102cf6004803603604081101561029957600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff16906020019092919080359060200190929190505050610dc5565b005b6102d9610f0d565b604051808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060405180910390f35b610323610f33565b005b610427600480360360e081101561033b57600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803573ffffffffffffffffffffffffffffffffffffffff1690602001909291905050506113c5565b005b6104316117a1565b604051808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060405180910390f35b6104bf6004803603604081101561048957600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803590602001909291905050506117c7565b6040518082815260200191505060405180910390f35b6104dd611c7b565b6040518082815260200191505060405180910390f35b6104fb611c81565b005b61053f6004803603602081101561051357600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190505050611d17565b6040518082815260200191505060405180910390f35b610681600480360360a081101561056b57600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803590602001906401000000008111156105e857600080fd5b8201836020820111156105fa57600080fd5b8035906020019184602083028401116401000000008311171561061c57600080fd5b90919293919293908035906020019064010000000081111561063d57600080fd5b82018360208201111561064f57600080fd5b8035906020019184602083028401116401000000008311171561067157600080fd5b9091929391929390505050611e09565b005b6106c56004803603602081101561069957600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190505050612018565b005b610709600480360360208110156106dd57600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff1690602001909291905050506120d6565b604051808215151515815260200191505060405180910390f35b61072b61216e565b005b610735612217565b604051808215151515815260200191505060405180910390f35b61077b6004803603602081101561076557600080fd5b8101908080359060200190929190505050612254565b005b6107bf6004803603602081101561079357600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff1690602001909291905050506122d8565b005b61082d600480360360608110156107d757600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803590602001909291905050506123bf565b6040518082815260200191505060405180910390f35b61084b612a9c565b604051808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060405180910390f35b603460009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff1614610950576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004018080602001828103825260178152602001807f43616c6c6572206973206e6f7420746865205661756c7400000000000000000081525060200191505060405180910390fd5b6000603a60009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1690508073ffffffffffffffffffffffffffffffffffffffff16636a627842603960009054906101000a900473ffffffffffffffffffffffffffffffffffffffff166040518263ffffffff1660e01b8152600401808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168152602001915050600060405180830381600087803b158015610a1857600080fd5b505af1158015610a2c573d6000803e3d6000fd5b505050506000603760009054906101000a900473ffffffffffffffffffffffffffffffffffffffff16905060008173ffffffffffffffffffffffffffffffffffffffff166370a08231306040518263ffffffff1660e01b8152600401808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060206040518083038186803b158015610ad657600080fd5b505afa158015610aea573d6000803e3d6000fd5b505050506040513d6020811015610b0057600080fd5b810190808051906020019092919050505090508173ffffffffffffffffffffffffffffffffffffffff1663a9059cbb603460009054906101000a900473ffffffffffffffffffffffffffffffffffffffff16836040518363ffffffff1660e01b8152600401808373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200182815260200192505050602060405180830381600087803b158015610bbc57600080fd5b505af1158015610bd0573d6000803e3d6000fd5b505050506040513d6020811015610be657600080fd5b8101908080519060200190929190505050610c69576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252601c8152602001807f52657761726420746f6b656e207472616e73666572206661696c65640000000081525060200191505060405180910390fd5b7f9b15fe06f6132479e0c4d9dfbbff1de507a47663a459b2cc4ba1aa5a55e52058603460009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1682604051808373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020018281526020019250505060405180910390a1505050565b6000610d05612ac2565b905090565b610d12612217565b610d84576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252601a8152602001807f43616c6c6572206973206e6f742074686520476f7665726e6f7200000000000081525060200191505060405180910390fd5b610d8e8282612af3565b5050565b60356020528060005260406000206000915054906101000a900473ffffffffffffffffffffffffffffffffffffffff1681565b610dcd612217565b610e3f576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252601a8152602001807f43616c6c6572206973206e6f742074686520476f7665726e6f7200000000000081525060200191505060405180910390fd5b8173ffffffffffffffffffffffffffffffffffffffff1663a9059cbb610e63610cfb565b836040518363ffffffff1660e01b8152600401808373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200182815260200192505050602060405180830381600087803b158015610ecd57600080fd5b505af1158015610ee1573d6000803e3d6000fd5b505050506040513d6020811015610ef757600080fd5b8101908080519060200190929190505050505050565b603760009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1681565b603460009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff161480610fc15750610f92610cfb565b73ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff16145b611016576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401808060200182810382526023815260200180613eb46023913960400191505060405180910390fd5b6000611020612e3c565b50915050603960009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16632e1a7d4d826040518263ffffffff1660e01b815260040180828152602001915050600060405180830381600087803b15801561109957600080fd5b505af11580156110ad573d6000803e3d6000fd5b50505050600060366000815481106110c157fe5b9060005260206000200160009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1690506000603560008373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff166370a08231306040518263ffffffff1660e01b8152600401808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060206040518083038186803b1580156111cc57600080fd5b505afa1580156111e0573d6000803e3d6000fd5b505050506040513d60208110156111f657600080fd5b81019080805190602001909291905050509050603360009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16631a4d01d282603b60009054906101000a9004600f0b60006040518463ffffffff1660e01b81526004018084815260200183600f0b600f0b81526020018281526020019350505050600060405180830381600087803b1580156112a457600080fd5b505af11580156112b8573d6000803e3d6000fd5b505050506113c0603460009054906101000a900473ffffffffffffffffffffffffffffffffffffffff168373ffffffffffffffffffffffffffffffffffffffff166370a08231306040518263ffffffff1660e01b8152600401808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060206040518083038186803b15801561135f57600080fd5b505afa158015611373573d6000803e3d6000fd5b505050506040513d602081101561138957600080fd5b81019080805190602001909291905050508473ffffffffffffffffffffffffffffffffffffffff1661308e9092919063ffffffff16565b505050565b6113cd612217565b61143f576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252601a8152602001807f43616c6c6572206973206e6f742074686520476f7665726e6f7200000000000081525060200191505060405180910390fd5b600060019054906101000a900460ff168061145e575061145d61315f565b5b8061147557506000809054906101000a900460ff16155b6114ca576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252602e815260200180613ef8602e913960400191505060405180910390fd5b60008060019054906101000a900460ff16159050801561151a576001600060016101000a81548160ff02191690831515021790555060016000806101000a81548160ff0219169083151502179055505b600088905060008090505b600381600f0b121561163d578673ffffffffffffffffffffffffffffffffffffffff168273ffffffffffffffffffffffffffffffffffffffff1663c661065783600f0b6040518263ffffffff1660e01b81526004018082815260200191505060206040518083038186803b15801561159c57600080fd5b505afa1580156115b0573d6000803e3d6000fd5b505050506040513d60208110156115c657600080fd5b810190808051906020019092919050505073ffffffffffffffffffffffffffffffffffffffff1614156116305780603b60006101000a8154816fffffffffffffffffffffffffffffffff0219169083600f0b6fffffffffffffffffffffffffffffffff1602179055505b8080600101915050611525565b507fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff603b60009054906101000a9004600f0b600f0b14156116e6576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004018080602001828103825260138152602001807f496e76616c69642033706f6f6c2061737365740000000000000000000000000081525060200191505060405180910390fd5b83603960006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff16021790555082603a60006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff1602179055506117758989898989613176565b5080156117975760008060016101000a81548160ff0219169083151502179055505b5050505050505050565b603460009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1681565b6000603460009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff161461188c576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004018080602001828103825260178152602001807f43616c6c6572206973206e6f7420746865205661756c7400000000000000000081525060200191505060405180910390fd5b60008211611902576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004018080602001828103825260168152602001807f4d757374206465706f73697420736f6d657468696e670000000000000000000081525060200191505060405180910390fd5b61190a613e91565b8281603b60009054906101000a9004600f0b600f0b6003811061192957fe5b602002018181525050603360009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16634515cef38260006040518363ffffffff1660e01b81526004018083600360200280838360005b838110156119ad578082015181840152602081019050611992565b5050505090500182815260200192505050600060405180830381600087803b1580156119d857600080fd5b505af11580156119ec573d6000803e3d6000fd5b505050506000603560008673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900473ffffffffffffffffffffffffffffffffffffffff169050603960009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16636e553f658273ffffffffffffffffffffffffffffffffffffffff166370a08231306040518263ffffffff1660e01b8152600401808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060206040518083038186803b158015611b0f57600080fd5b505afa158015611b23573d6000803e3d6000fd5b505050506040513d6020811015611b3957600080fd5b8101908080519060200190929190505050306040518363ffffffff1660e01b8152600401808381526020018273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200192505050600060405180830381600087803b158015611bb457600080fd5b505af1158015611bc8573d6000803e3d6000fd5b505050508392508473ffffffffffffffffffffffffffffffffffffffff167f5548c837ab068cf56a2c2479df0882a4922fd203edb7517321831d95078c5f62603360009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1685604051808373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020018281526020019250505060405180910390a2505092915050565b60385481565b611c8961324a565b73ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff1614611d0c576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401808060200182810382526030815260200180613f866030913960400191505060405180910390fd5b611d153361327b565b565b600080611d22612e3c565b92505050600091506000811115611e0357603360009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1663cc2b27d782603b60009054906101000a9004600f0b6040518363ffffffff1660e01b81526004018083815260200182600f0b600f0b81526020019250505060206040518083038186803b158015611dc357600080fd5b505afa158015611dd7573d6000803e3d6000fd5b505050506040513d6020811015611ded57600080fd5b8101908080519060200190929190505050820191505b50919050565b611e11612217565b611e83576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252601a8152602001807f43616c6c6572206973206e6f742074686520476f7665726e6f7200000000000081525060200191505060405180910390fd5b600060019054906101000a900460ff1680611ea25750611ea161315f565b5b80611eb957506000809054906101000a900460ff16155b611f0e576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252602e815260200180613ef8602e913960400191505060405180910390fd5b60008060019054906101000a900460ff161590508015611f5e576001600060016101000a81548160ff02191690831515021790555060016000806101000a81548160ff0219169083151502179055505b611fed888888888880806020026020016040519081016040528093929190818152602001838360200280828437600081840152601f19601f82011690508083019250505050505050878780806020026020016040519081016040528093929190818152602001838360200280828437600081840152601f19601f8201169050808301925050505050505061338b565b801561200e5760008060016101000a81548160ff0219169083151502179055505b5050505050505050565b612020612217565b612092576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252601a8152602001807f43616c6c6572206973206e6f742074686520476f7665726e6f7200000000000081525060200191505060405180910390fd5b80603760006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff16021790555050565b60008073ffffffffffffffffffffffffffffffffffffffff16603560008473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1614159050919050565b6000603660008154811061217e57fe5b9060005260206000200160009054906101000a900473ffffffffffffffffffffffffffffffffffffffff16905061221481603560008473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1661351e565b50565b6000612221612ac2565b73ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff1614905090565b61225c612217565b6122ce576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252601a8152602001807f43616c6c6572206973206e6f742074686520476f7665726e6f7200000000000081525060200191505060405180910390fd5b8060388190555050565b6122e0612217565b612352576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252601a8152602001807f43616c6c6572206973206e6f742074686520476f7665726e6f7200000000000081525060200191505060405180910390fd5b61235b8161375f565b8073ffffffffffffffffffffffffffffffffffffffff1661237a612ac2565b73ffffffffffffffffffffffffffffffffffffffff167fa39cc5eb22d0f34d8beaefee8a3f17cc229c1a1d1ef87a5ad47313487b1c4f0d60405160405180910390a350565b6000603460009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff1614612484576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004018080602001828103825260178152602001807f43616c6c6572206973206e6f7420746865205661756c7400000000000000000081525060200191505060405180910390fd5b600073ffffffffffffffffffffffffffffffffffffffff168473ffffffffffffffffffffffffffffffffffffffff161415612527576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004018080602001828103825260118152602001807f496e76616c696420726563697069656e7400000000000000000000000000000081525060200191505060405180910390fd5b6000821161259d576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252600e8152602001807f496e76616c696420616d6f756e7400000000000000000000000000000000000081525060200191505060405180910390fd5b60008060006125aa612e3c565b9250925092506000603360009054906101000a900473ffffffffffffffffffffffffffffffffffffffff16905060008173ffffffffffffffffffffffffffffffffffffffff1663cc2b27d784603b60009054906101000a9004600f0b6040518363ffffffff1660e01b81526004018083815260200182600f0b600f0b81526020019250505060206040518083038186803b15801561264757600080fd5b505afa15801561265b573d6000803e3d6000fd5b505050506040513d602081101561267157600080fd5b8101908080519060200190929190505050905060006126ab8261269d8a8761378e90919063ffffffff16565b61381490919063ffffffff16565b90508086101561274357603960009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16632e1a7d4d826040518263ffffffff1660e01b815260040180828152602001915050600060405180830381600087803b15801561272a57600080fd5b505af115801561273e573d6000803e3d6000fd5b505050505b8273ffffffffffffffffffffffffffffffffffffffff16631a4d01d282603b60009054906101000a9004600f0b60006040518463ffffffff1660e01b81526004018084815260200183600f0b600f0b81526020018281526020019350505050600060405180830381600087803b1580156127bc57600080fd5b505af11580156127d0573d6000803e3d6000fd5b505050508873ffffffffffffffffffffffffffffffffffffffff1663a9059cbb8b8a6040518363ffffffff1660e01b8152600401808373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200182815260200192505050602060405180830381600087803b15801561285b57600080fd5b505af115801561286f573d6000803e3d6000fd5b505050506040513d602081101561288557600080fd5b81019080805190602001909291905050505060008973ffffffffffffffffffffffffffffffffffffffff166370a08231306040518263ffffffff1660e01b8152600401808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060206040518083038186803b15801561291657600080fd5b505afa15801561292a573d6000803e3d6000fd5b505050506040513d602081101561294057600080fd5b8101908080519060200190929190505050905060008111156129aa576129a9603460009054906101000a900473ffffffffffffffffffffffffffffffffffffffff16828c73ffffffffffffffffffffffffffffffffffffffff1661308e9092919063ffffffff16565b5b8897508973ffffffffffffffffffffffffffffffffffffffff167f2717ead6b9200dd235aad468c9809ea400fe33ac69b5bfaa6d3e90fc922b6398603560008d73ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900473ffffffffffffffffffffffffffffffffffffffff168a604051808373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020018281526020019250505060405180910390a2505050505050509392505050565b603360009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1681565b6000807f7bea13895fa79d2831e0a9e28edede30099005a50d652d8957cf8a607ee6ca4a60001b9050805491505090565b600073ffffffffffffffffffffffffffffffffffffffff16603560008473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1614612bf4576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004018080602001828103825260128152602001807f70546f6b656e20616c726561647920736574000000000000000000000000000081525060200191505060405180910390fd5b600073ffffffffffffffffffffffffffffffffffffffff168273ffffffffffffffffffffffffffffffffffffffff1614158015612c5e5750600073ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff1614155b612cd0576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004018080602001828103825260118152602001807f496e76616c69642061646472657373657300000000000000000000000000000081525060200191505060405180910390fd5b80603560008473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff16021790555060368290806001815401808255809150509060018203906000526020600020016000909192909190916101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff160217905550508173ffffffffffffffffffffffffffffffffffffffff167fef6485b84315f9b1483beffa32aae9a0596890395e3d7521f1c5fbb51790e76582604051808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060405180910390a2612e38828261351e565b5050565b6000806000603560006036600081548110612e5357fe5b9060005260206000200160009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff166370a08231306040518263ffffffff1660e01b8152600401808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060206040518083038186803b158015612f5557600080fd5b505afa158015612f69573d6000803e3d6000fd5b505050506040513d6020811015612f7f57600080fd5b810190808051906020019092919050505092506000603960009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1690508073ffffffffffffffffffffffffffffffffffffffff166370a08231306040518263ffffffff1660e01b8152600401808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060206040518083038186803b15801561303657600080fd5b505afa15801561304a573d6000803e3d6000fd5b505050506040513d602081101561306057600080fd5b81019080805190602001909291905050509250613086838561385e90919063ffffffff16565b915050909192565b61315a838473ffffffffffffffffffffffffffffffffffffffff1663a9059cbb905060e01b8484604051602401808373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200182815260200192505050604051602081830303815290604052907bffffffffffffffffffffffffffffffffffffffffffffffffffffffff19166020820180517bffffffffffffffffffffffffffffffffffffffffffffffffffffffff83818316178352505050506138e6565b505050565b6000803090506000813b9050600081149250505090565b84603360006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff16021790555083603460006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff16021790555082603760006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff1602179055506132438282612af3565b5050505050565b6000807f44c4d30b2eaad5130ad70c3ba6972730566f3e6359ab83e800d905c61b1c51db60001b9050805491505090565b600073ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff16141561331e576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252601a8152602001807f4e657720476f7665726e6f72206973206164647265737328302900000000000081525060200191505060405180910390fd5b8073ffffffffffffffffffffffffffffffffffffffff1661333d612ac2565b73ffffffffffffffffffffffffffffffffffffffff167fc7c0c772add429241571afb3805861fb3cfa2af374534088b76cdb4325a87e9a60405160405180910390a361338881613b31565b50565b84603360006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff16021790555083603460006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff16021790555082603760006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff160217905550600082519050815181146134ca576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004018080602001828103825260148152602001807f496e76616c696420696e7075742061727261797300000000000000000000000081525060200191505060405180910390fd5b60008090505b81811015613515576135088482815181106134e757fe5b60200260200101518483815181106134fb57fe5b6020026020010151612af3565b80806001019150506134d0565b50505050505050565b60008290506000829050613576603360009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1660008473ffffffffffffffffffffffffffffffffffffffff16613b609092919063ffffffff16565b6135e3603360009054906101000a900473ffffffffffffffffffffffffffffffffffffffff167fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff8473ffffffffffffffffffffffffffffffffffffffff16613b609092919063ffffffff16565b613631603360009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1660008373ffffffffffffffffffffffffffffffffffffffff16613b609092919063ffffffff16565b61369e603360009054906101000a900473ffffffffffffffffffffffffffffffffffffffff167fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff8373ffffffffffffffffffffffffffffffffffffffff16613b609092919063ffffffff16565b6136ec603960009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1660008373ffffffffffffffffffffffffffffffffffffffff16613b609092919063ffffffff16565b613759603960009054906101000a900473ffffffffffffffffffffffffffffffffffffffff167fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff8373ffffffffffffffffffffffffffffffffffffffff16613b609092919063ffffffff16565b50505050565b60007f44c4d30b2eaad5130ad70c3ba6972730566f3e6359ab83e800d905c61b1c51db60001b90508181555050565b6000808314156137a1576000905061380e565b60008284029050828482816137b257fe5b0414613809576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401808060200182810382526021815260200180613ed76021913960400191505060405180910390fd5b809150505b92915050565b600061385683836040518060400160405280601a81526020017f536166654d6174683a206469766973696f6e206279207a65726f000000000000815250613d80565b905092915050565b6000808284019050838110156138dc576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252601b8152602001807f536166654d6174683a206164646974696f6e206f766572666c6f77000000000081525060200191505060405180910390fd5b8091505092915050565b6139058273ffffffffffffffffffffffffffffffffffffffff16613e46565b613977576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252601f8152602001807f5361666545524332303a2063616c6c20746f206e6f6e2d636f6e74726163740081525060200191505060405180910390fd5b600060608373ffffffffffffffffffffffffffffffffffffffff16836040518082805190602001908083835b602083106139c657805182526020820191506020810190506020830392506139a3565b6001836020036101000a0380198251168184511680821785525050505050509050019150506000604051808303816000865af19150503d8060008114613a28576040519150601f19603f3d011682016040523d82523d6000602084013e613a2d565b606091505b509150915081613aa5576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004018080602001828103825260208152602001807f5361666545524332303a206c6f772d6c6576656c2063616c6c206661696c656481525060200191505060405180910390fd5b600081511115613b2b57808060200190516020811015613ac457600080fd5b8101908080519060200190929190505050613b2a576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252602a815260200180613f26602a913960400191505060405180910390fd5b5b50505050565b60007f7bea13895fa79d2831e0a9e28edede30099005a50d652d8957cf8a607ee6ca4a60001b90508181555050565b6000811480613c5a575060008373ffffffffffffffffffffffffffffffffffffffff1663dd62ed3e30856040518363ffffffff1660e01b8152600401808373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020018273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019250505060206040518083038186803b158015613c1d57600080fd5b505afa158015613c31573d6000803e3d6000fd5b505050506040513d6020811015613c4757600080fd5b8101908080519060200190929190505050145b613caf576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401808060200182810382526036815260200180613f506036913960400191505060405180910390fd5b613d7b838473ffffffffffffffffffffffffffffffffffffffff1663095ea7b3905060e01b8484604051602401808373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200182815260200192505050604051602081830303815290604052907bffffffffffffffffffffffffffffffffffffffffffffffffffffffff19166020820180517bffffffffffffffffffffffffffffffffffffffffffffffffffffffff83818316178352505050506138e6565b505050565b60008083118290613e2c576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004018080602001828103825283818151815260200191508051906020019080838360005b83811015613df1578082015181840152602081019050613dd6565b50505050905090810190601f168015613e1e5780820380516001836020036101000a031916815260200191505b509250505060405180910390fd5b506000838581613e3857fe5b049050809150509392505050565b60008060007fc5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a47060001b9050833f9150808214158015613e8857506000801b8214155b92505050919050565b604051806060016040528060039060208202803883398082019150509050509056fe43616c6c6572206973206e6f7420746865205661756c74206f7220476f7665726e6f72536166654d6174683a206d756c7469706c69636174696f6e206f766572666c6f77436f6e747261637420696e7374616e63652068617320616c7265616479206265656e20696e697469616c697a65645361666545524332303a204552433230206f7065726174696f6e20646964206e6f7420737563636565645361666545524332303a20617070726f76652066726f6d206e6f6e2d7a65726f20746f206e6f6e2d7a65726f20616c6c6f77616e63654f6e6c79207468652070656e64696e6720476f7665726e6f722063616e20636f6d706c6574652074686520636c61696da265627a7a723158209bb4b18e45067f6bcec603fada25969ca5c2af822a1340539144e13d48e6c79264736f6c634300050b0032", "devdoc": {"methods": {"checkBalance(address)": {"details": "Get the total asset value held in the platform This includes any interest that was generated since depositing We calculate this by calculating a what we would get if we liquidated the allocated percentage of this asset.", "params": {"_asset": "Address of the asset"}, "return": "balance    Total value of the asset in the platform"}, "claimGovernance()": {"details": "Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor."}, "collectRewardToken()": {"details": "Collect accumulated CRV and send to Vault."}, "deposit(address,uint256)": {"details": "Deposit asset into the Curve 3Pool", "params": {"_amount": "Amount of asset to deposit", "_asset": "Address of asset to deposit"}, "return": "amountDeposited Amount of asset that was deposited"}, "governor()": {"details": "Returns the address of the current Governor."}, "initialize(address,address,address,address,address,address,address)": {"params": {"_asset": "Address of the supported asset", "_crvGaugeAddress": "Address of the Curve DAO gauge for this pool", "_crvMinterAddress": "Address of the CRV minter for rewards", "_pToken": "Correspond platform token addres (i.e. 3Crv)", "_platformAddress": "Address of the Curve 3pool", "_rewardTokenAddress": "Address of CRV", "_vaultAddress": "Address of the vault"}}, "initialize(address,address,address,address[],address[])": {"details": "Internal initialize function, to set up initial internal state", "params": {"_assets": "Addresses of initial supported assets", "_pTokens": "Platform Token corresponding addresses", "_platformAddress": "jGeneric platform address", "_rewardTokenAddress": "Address of reward token for platform", "_vaultAddress": "Address of the Vault"}}, "isGovernor()": {"details": "Returns true if the caller is the current Governor."}, "liquidate()": {"details": "Remove all assets from platform and send them to Vault contract."}, "safeApproveAllTokens()": {"details": "Approve the spending of all assets by their corresponding pool tokens,     if for some reason is it necessary."}, "setPTokenAddress(address,address)": {"details": "Provide support for asset by passing its pToken address.     This method can only be called by the system Governor", "params": {"_asset": "Address for the asset", "_pToken": "Address for the corresponding platform token"}}, "setRewardLiquidationThreshold(uint256)": {"details": "Set the reward token liquidation threshold.", "params": {"_threshold": "Threshold amount in decimals of reward token that will cause the Vault to claim and liquidate on allocate() calls."}}, "setRewardTokenAddress(address)": {"details": "Set the reward token address.", "params": {"_rewardTokenAddress": "Address of the reward token"}}, "supportsAsset(address)": {"details": "Retuns bool indicating whether asset is supported by strategy", "params": {"_asset": "Address of the asset"}}, "transferGovernance(address)": {"details": "Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete", "params": {"_newGovernor": "Address of the new Governor"}}, "transferToken(address,uint256)": {"details": "Transfer token to governor. Intended for recovering tokens stuck in     strategy contracts, i.e. mistaken sends.", "params": {"_amount": "Amount of the asset to transfer", "_asset": "Address for the asset"}}, "withdraw(address,address,uint256)": {"details": "Withdraw asset from Curve 3Pool", "params": {"_amount": "Amount of asset to withdraw", "_asset": "Address of asset to withdraw", "_recipient": "Address to receive withdrawn asset"}, "return": "amountWithdrawn Amount of asset that was withdrawn"}}}, "userdoc": {"methods": {"initialize(address,address,address,address,address,address,address)": {"notice": "Initializer for setting up strategy internal state. This overrides the InitializableAbstractStrategy initializer as Curve strategies don't fit well within that abstraction."}}}, "gasEstimates": {"creation": {"codeDepositCost": "3272400", "executionCost": "infinite", "totalCost": "infinite"}, "external": {"assetToPToken(address)": "696", "checkBalance(address)": "infinite", "claimGovernance()": "infinite", "collectRewardToken()": "infinite", "deposit(address,uint256)": "infinite", "governor()": "525", "initialize(address,address,address,address,address,address,address)": "infinite", "initialize(address,address,address,address[],address[])": "infinite", "isGovernor()": "540", "liquidate()": "infinite", "platformAddress()": "585", "rewardLiquidationThreshold()": "538", "rewardTokenAddress()": "476", "safeApproveAllTokens()": "infinite", "setPTokenAddress(address,address)": "infinite", "setRewardLiquidationThreshold(uint256)": "20631", "setRewardTokenAddress(address)": "20913", "supportsAsset(address)": "776", "transferGovernance(address)": "infinite", "transferToken(address,uint256)": "infinite", "vaultAddress()": "542", "withdraw(address,address,uint256)": "infinite"}, "internal": {"_abstractSetPToken(address,address)": "infinite", "_getTotalPTokens()": "infinite"}}}