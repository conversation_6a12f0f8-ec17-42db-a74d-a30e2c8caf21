{"address": "0xc71cc84eb9f51B3bE71ACd7479dbB46c3f3827cA", "abi": [{"constant": true, "inputs": [], "name": "governor", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}], "name": "upgradeTo", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "payable": true, "stateMutability": "payable", "type": "function"}, {"constant": true, "inputs": [], "name": "implementation", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "claimGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "isGovernor", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_logic", "type": "address"}, {"internalType": "address", "name": "_initGovernor", "type": "address"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "name": "initialize", "outputs": [], "payable": true, "stateMutability": "payable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_newGovernor", "type": "address"}], "name": "transferGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "admin", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"payable": true, "stateMutability": "payable", "type": "fallback"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "PendingGovernorshipTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "GovernorshipTransferred", "type": "event"}], "transactionHash": "0x393ba5c6d728ed239354f8ae634f5409dc8f2902d8edf2bb0569c5749d9bfc08", "receipt": {"to": null, "from": "0xD85A569F3C26f81070544451131c742283360400", "contractAddress": "0xc71cc84eb9f51B3bE71ACd7479dbB46c3f3827cA", "transactionIndex": 9, "gasUsed": "613537", "logsBloom": "0x00000000400000000000000000000000000000020000000000000000000000000000000000000000000000000000000000008000008000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000800000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000400000000000000000000000000004000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000020000000000000000000000000000000000000000400000000000000000000000000", "blockHash": "0x80a6d386995275b1f869c403d1c824491ffe162cd927978ab319f1ed82a33cc6", "transactionHash": "0x393ba5c6d728ed239354f8ae634f5409dc8f2902d8edf2bb0569c5749d9bfc08", "logs": [{"transactionIndex": 9, "blockNumber": 7733942, "transactionHash": "0x393ba5c6d728ed239354f8ae634f5409dc8f2902d8edf2bb0569c5749d9bfc08", "address": "0xc71cc84eb9f51B3bE71ACd7479dbB46c3f3827cA", "topics": ["0xc7c0c772add429241571afb3805861fb3cfa2af374534088b76cdb4325a87e9a", "0x0000000000000000000000000000000000000000000000000000000000000000", "0x000000000000000000000000d85a569f3c26f81070544451131c742283360400"], "data": "0x", "logIndex": 43, "blockHash": "0x80a6d386995275b1f869c403d1c824491ffe162cd927978ab319f1ed82a33cc6"}], "blockNumber": 7733942, "cumulativeGasUsed": "4528392", "status": 1, "byzantium": true}, "args": [], "solcInputHash": "19364863fdc82a67c752b1e7be162d9b", "metadata": "{\"compiler\":{\"version\":\"0.5.11+commit.22be8592.mod\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"constant\":true,\"inputs\":[],\"name\":\"governor\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"}],\"name\":\"upgradeTo\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"upgradeToAndCall\",\"outputs\":[],\"payable\":true,\"stateMutability\":\"payable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"implementation\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"claimGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"isGovernor\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_logic\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_initGovernor\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"_data\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"payable\":true,\"stateMutability\":\"payable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_newGovernor\",\"type\":\"address\"}],\"name\":\"transferGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"admin\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"payable\":true,\"stateMutability\":\"payable\",\"type\":\"fallback\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"Upgraded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"PendingGovernorshipTransfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"GovernorshipTransferred\",\"type\":\"event\"}],\"devdoc\":{\"author\":\"Origin Protocol Inc\",\"details\":\"This contract combines an upgradeability proxy with our governor system\",\"methods\":{\"admin()\":{\"return\":\"The address of the proxy admin/it's also the governor.\"},\"claimGovernance()\":{\"details\":\"Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor.\"},\"governor()\":{\"details\":\"Returns the address of the current Governor.\"},\"implementation()\":{\"return\":\"The address of the implementation.\"},\"initialize(address,address,bytes)\":{\"details\":\"Contract initializer with Governor enforcement\",\"params\":{\"_data\":\"Data to send as msg.data to the implementation to initialize the proxied contract. It should include the signature and the parameters of the function to be called, as described in https://solidity.readthedocs.io/en/v0.4.24/abi-spec.html#function-selector-and-argument-encoding. This parameter is optional, if no data is given the initialization call to proxied contract will be skipped.\",\"_initGovernor\":\"Address of the initial Governor.\",\"_logic\":\"Address of the initial implementation.\"}},\"isGovernor()\":{\"details\":\"Returns true if the caller is the current Governor.\"},\"transferGovernance(address)\":{\"details\":\"Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete\",\"params\":{\"_newGovernor\":\"Address of the new Governor\"}},\"upgradeTo(address)\":{\"details\":\"Upgrade the backing implementation of the proxy. Only the admin can call this function.\",\"params\":{\"newImplementation\":\"Address of the new implementation.\"}},\"upgradeToAndCall(address,bytes)\":{\"details\":\"Upgrade the backing implementation of the proxy and call a function on the new implementation. This is useful to initialize the proxied contract.\",\"params\":{\"data\":\"Data to send as msg.data in the low level call. It should include the signature and the parameters of the function to be called, as described in https://solidity.readthedocs.io/en/v0.4.24/abi-spec.html#function-selector-and-argument-encoding.\",\"newImplementation\":\"Address of the new implementation.\"}}},\"title\":\"BaseGovernedUpgradeabilityProxy\"},\"userdoc\":{\"methods\":{}}},\"settings\":{\"compilationTarget\":{\"contracts/proxies/InitializeGovernedUpgradeabilityProxy.sol\":\"InitializeGovernedUpgradeabilityProxy\"},\"evmVersion\":\"petersburg\",\"libraries\":{},\"metadata\":{\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/upgrades/contracts/upgradeability/BaseUpgradeabilityProxy.sol\":{\"content\":\"pragma solidity ^0.5.0;\\n\\nimport './Proxy.sol';\\nimport '../utils/Address.sol';\\n\\n/**\\n * @title BaseUpgradeabilityProxy\\n * @dev This contract implements a proxy that allows to change the\\n * implementation address to which it will delegate.\\n * Such a change is called an implementation upgrade.\\n */\\ncontract BaseUpgradeabilityProxy is Proxy {\\n  /**\\n   * @dev Emitted when the implementation is upgraded.\\n   * @param implementation Address of the new implementation.\\n   */\\n  event Upgraded(address indexed implementation);\\n\\n  /**\\n   * @dev Storage slot with the address of the current implementation.\\n   * This is the keccak-256 hash of \\\"eip1967.proxy.implementation\\\" subtracted by 1, and is\\n   * validated in the constructor.\\n   */\\n  bytes32 internal constant IMPLEMENTATION_SLOT = 0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc;\\n\\n  /**\\n   * @dev Returns the current implementation.\\n   * @return Address of the current implementation\\n   */\\n  function _implementation() internal view returns (address impl) {\\n    bytes32 slot = IMPLEMENTATION_SLOT;\\n    assembly {\\n      impl := sload(slot)\\n    }\\n  }\\n\\n  /**\\n   * @dev Upgrades the proxy to a new implementation.\\n   * @param newImplementation Address of the new implementation.\\n   */\\n  function _upgradeTo(address newImplementation) internal {\\n    _setImplementation(newImplementation);\\n    emit Upgraded(newImplementation);\\n  }\\n\\n  /**\\n   * @dev Sets the implementation address of the proxy.\\n   * @param newImplementation Address of the new implementation.\\n   */\\n  function _setImplementation(address newImplementation) internal {\\n    require(OpenZeppelinUpgradesAddress.isContract(newImplementation), \\\"Cannot set a proxy implementation to a non-contract address\\\");\\n\\n    bytes32 slot = IMPLEMENTATION_SLOT;\\n\\n    assembly {\\n      sstore(slot, newImplementation)\\n    }\\n  }\\n}\\n\",\"keccak256\":\"0x1b37f808dc0a45976ad23aa53908c485a74a4d9fbd03066a5ab399c46436e570\"},\"@openzeppelin/upgrades/contracts/upgradeability/Proxy.sol\":{\"content\":\"pragma solidity ^0.5.0;\\n\\n/**\\n * @title Proxy\\n * @dev Implements delegation of calls to other contracts, with proper\\n * forwarding of return values and bubbling of failures.\\n * It defines a fallback function that delegates all calls to the address\\n * returned by the abstract _implementation() internal function.\\n */\\ncontract Proxy {\\n  /**\\n   * @dev Fallback function.\\n   * Implemented entirely in `_fallback`.\\n   */\\n  function () payable external {\\n    _fallback();\\n  }\\n\\n  /**\\n   * @return The Address of the implementation.\\n   */\\n  function _implementation() internal view returns (address);\\n\\n  /**\\n   * @dev Delegates execution to an implementation contract.\\n   * This is a low level function that doesn't return to its internal call site.\\n   * It will return to the external caller whatever the implementation returns.\\n   * @param implementation Address to delegate.\\n   */\\n  function _delegate(address implementation) internal {\\n    assembly {\\n      // Copy msg.data. We take full control of memory in this inline assembly\\n      // block because it will not return to Solidity code. We overwrite the\\n      // Solidity scratch pad at memory position 0.\\n      calldatacopy(0, 0, calldatasize)\\n\\n      // Call the implementation.\\n      // out and outsize are 0 because we don't know the size yet.\\n      let result := delegatecall(gas, implementation, 0, calldatasize, 0, 0)\\n\\n      // Copy the returned data.\\n      returndatacopy(0, 0, returndatasize)\\n\\n      switch result\\n      // delegatecall returns 0 on error.\\n      case 0 { revert(0, returndatasize) }\\n      default { return(0, returndatasize) }\\n    }\\n  }\\n\\n  /**\\n   * @dev Function that is run as the first thing in the fallback function.\\n   * Can be redefined in derived contracts to add functionality.\\n   * Redefinitions must call super._willFallback().\\n   */\\n  function _willFallback() internal {\\n  }\\n\\n  /**\\n   * @dev fallback implementation.\\n   * Extracted to enable manual triggering.\\n   */\\n  function _fallback() internal {\\n    _willFallback();\\n    _delegate(_implementation());\\n  }\\n}\\n\",\"keccak256\":\"0xd8074ae5fa7ee6384ca8196a896612fb044bbf4ff4b7336d03cd97845000ac21\"},\"@openzeppelin/upgrades/contracts/utils/Address.sol\":{\"content\":\"pragma solidity ^0.5.0;\\n\\n/**\\n * Utility library of inline functions on addresses\\n *\\n * Source https://raw.githubusercontent.com/OpenZeppelin/openzeppelin-solidity/v2.1.3/contracts/utils/Address.sol\\n * This contract is copied here and renamed from the original to avoid clashes in the compiled artifacts\\n * when the user imports a zos-lib contract (that transitively causes this contract to be compiled and added to the\\n * build/artifacts folder) as well as the vanilla Address implementation from an openzeppelin version.\\n */\\nlibrary OpenZeppelinUpgradesAddress {\\n    /**\\n     * Returns whether the target address is a contract\\n     * @dev This function will return false if invoked during the constructor of a contract,\\n     * as the code is not actually created until after the constructor finishes.\\n     * @param account address of the account to check\\n     * @return whether the target address is a contract\\n     */\\n    function isContract(address account) internal view returns (bool) {\\n        uint256 size;\\n        // XXX Currently there is no better way to check if there is a contract in an address\\n        // than to check the size of the code at that address.\\n        // See https://ethereum.stackexchange.com/a/14016/36603\\n        // for more details about how this works.\\n        // TODO Check this again before the Serenity release, because all addresses will be\\n        // contracts then.\\n        // solhint-disable-next-line no-inline-assembly\\n        assembly { size := extcodesize(account) }\\n        return size > 0;\\n    }\\n}\\n\",\"keccak256\":\"0x7be7f8e4c08bc70ff9815e3f11c569f42aa3c447b5bc61fd75fb5cec97f63a9f\"},\"contracts/governance/Governable.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\n/**\\n * @title OUSD Governable Contract\\n * @dev Copy of the openzeppelin Ownable.sol contract with nomenclature change\\n *      from owner to governor and renounce methods removed. Does not use\\n *      Context.sol like Ownable.sol does for simplification.\\n * <AUTHOR> Protocol Inc\\n */\\ncontract Governable {\\n    // Storage position of the owner and pendingOwner of the contract\\n    // keccak256(\\\"OUSD.governor\\\");\\n    bytes32\\n        private constant governorPosition = 0x7bea13895fa79d2831e0a9e28edede30099005a50d652d8957cf8a607ee6ca4a;\\n\\n    // keccak256(\\\"OUSD.pending.governor\\\");\\n    bytes32\\n        private constant pendingGovernorPosition = 0x44c4d30b2eaad5130ad70c3ba6972730566f3e6359ab83e800d905c61b1c51db;\\n\\n    // keccak256(\\\"OUSD.reentry.status\\\");\\n    bytes32\\n        private constant reentryStatusPosition = 0x53bf423e48ed90e97d02ab0ebab13b2a235a6bfbe9c321847d5c175333ac4535;\\n\\n    // See OpenZeppelin ReentrancyGuard implementation\\n    uint256 constant _NOT_ENTERED = 1;\\n    uint256 constant _ENTERED = 2;\\n\\n    event PendingGovernorshipTransfer(\\n        address indexed previousGovernor,\\n        address indexed newGovernor\\n    );\\n\\n    event GovernorshipTransferred(\\n        address indexed previousGovernor,\\n        address indexed newGovernor\\n    );\\n\\n    /**\\n     * @dev Initializes the contract setting the deployer as the initial Governor.\\n     */\\n    constructor() internal {\\n        _setGovernor(msg.sender);\\n        emit GovernorshipTransferred(address(0), _governor());\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current Governor.\\n     */\\n    function governor() public view returns (address) {\\n        return _governor();\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current Governor.\\n     */\\n    function _governor() internal view returns (address governorOut) {\\n        bytes32 position = governorPosition;\\n        assembly {\\n            governorOut := sload(position)\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the address of the pending Governor.\\n     */\\n    function _pendingGovernor()\\n        internal\\n        view\\n        returns (address pendingGovernor)\\n    {\\n        bytes32 position = pendingGovernorPosition;\\n        assembly {\\n            pendingGovernor := sload(position)\\n        }\\n    }\\n\\n    /**\\n     * @dev Throws if called by any account other than the Governor.\\n     */\\n    modifier onlyGovernor() {\\n        require(isGovernor(), \\\"Caller is not the Governor\\\");\\n        _;\\n    }\\n\\n    /**\\n     * @dev Returns true if the caller is the current Governor.\\n     */\\n    function isGovernor() public view returns (bool) {\\n        return msg.sender == _governor();\\n    }\\n\\n    function _setGovernor(address newGovernor) internal {\\n        bytes32 position = governorPosition;\\n        assembly {\\n            sstore(position, newGovernor)\\n        }\\n    }\\n\\n    /**\\n     * @dev Prevents a contract from calling itself, directly or indirectly.\\n     * Calling a `nonReentrant` function from another `nonReentrant`\\n     * function is not supported. It is possible to prevent this from happening\\n     * by making the `nonReentrant` function external, and make it call a\\n     * `private` function that does the actual work.\\n     */\\n    modifier nonReentrant() {\\n        bytes32 position = reentryStatusPosition;\\n        uint256 _reentry_status;\\n        assembly {\\n            _reentry_status := sload(position)\\n        }\\n\\n        // On the first call to nonReentrant, _notEntered will be true\\n        require(_reentry_status != _ENTERED, \\\"Reentrant call\\\");\\n\\n        // Any calls to nonReentrant after this point will fail\\n        assembly {\\n            sstore(position, _ENTERED)\\n        }\\n\\n        _;\\n\\n        // By storing the original value once again, a refund is triggered (see\\n        // https://eips.ethereum.org/EIPS/eip-2200)\\n        assembly {\\n            sstore(position, _NOT_ENTERED)\\n        }\\n    }\\n\\n    function _setPendingGovernor(address newGovernor) internal {\\n        bytes32 position = pendingGovernorPosition;\\n        assembly {\\n            sstore(position, newGovernor)\\n        }\\n    }\\n\\n    /**\\n     * @dev Transfers Governance of the contract to a new account (`newGovernor`).\\n     * Can only be called by the current Governor. Must be claimed for this to complete\\n     * @param _newGovernor Address of the new Governor\\n     */\\n    function transferGovernance(address _newGovernor) external onlyGovernor {\\n        _setPendingGovernor(_newGovernor);\\n        emit PendingGovernorshipTransfer(_governor(), _newGovernor);\\n    }\\n\\n    /**\\n     * @dev Claim Governance of the contract to a new account (`newGovernor`).\\n     * Can only be called by the new Governor.\\n     */\\n    function claimGovernance() external {\\n        require(\\n            msg.sender == _pendingGovernor(),\\n            \\\"Only the pending Governor can complete the claim\\\"\\n        );\\n        _changeGovernor(msg.sender);\\n    }\\n\\n    /**\\n     * @dev Change Governance of the contract to a new account (`newGovernor`).\\n     * @param _newGovernor Address of the new Governor\\n     */\\n    function _changeGovernor(address _newGovernor) internal {\\n        require(_newGovernor != address(0), \\\"New Governor is address(0)\\\");\\n        emit GovernorshipTransferred(_governor(), _newGovernor);\\n        _setGovernor(_newGovernor);\\n    }\\n}\\n\",\"keccak256\":\"0x3e51ea48102945bf4b305bf9722a07514a585a29555d92f8c84352d1a4cfcee1\"},\"contracts/proxies/InitializeGovernedUpgradeabilityProxy.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\nimport { Governable } from \\\"../governance/Governable.sol\\\";\\nimport {\\n    BaseUpgradeabilityProxy\\n} from \\\"@openzeppelin/upgrades/contracts/upgradeability/BaseUpgradeabilityProxy.sol\\\";\\n\\n/**\\n * @title BaseGovernedUpgradeabilityProxy\\n * @dev This contract combines an upgradeability proxy with our governor system\\n * <AUTHOR> Protocol Inc\\n */\\ncontract InitializeGovernedUpgradeabilityProxy is\\n    Governable,\\n    BaseUpgradeabilityProxy\\n{\\n    /**\\n     * @dev Contract initializer with Governor enforcement\\n     * @param _logic Address of the initial implementation.\\n     * @param _initGovernor Address of the initial Governor.\\n     * @param _data Data to send as msg.data to the implementation to initialize the proxied contract.\\n     * It should include the signature and the parameters of the function to be called, as described in\\n     * https://solidity.readthedocs.io/en/v0.4.24/abi-spec.html#function-selector-and-argument-encoding.\\n     * This parameter is optional, if no data is given the initialization call to proxied contract will be skipped.\\n     */\\n    function initialize(\\n        address _logic,\\n        address _initGovernor,\\n        bytes memory _data\\n    ) public payable onlyGovernor {\\n        require(_implementation() == address(0));\\n        assert(\\n            IMPLEMENTATION_SLOT ==\\n                bytes32(uint256(keccak256(\\\"eip1967.proxy.implementation\\\")) - 1)\\n        );\\n        _changeGovernor(_initGovernor);\\n        _setImplementation(_logic);\\n        if (_data.length > 0) {\\n            (bool success, ) = _logic.delegatecall(_data);\\n            require(success);\\n        }\\n    }\\n\\n    /**\\n     * @return The address of the proxy admin/it's also the governor.\\n     */\\n    function admin() external view returns (address) {\\n        return _governor();\\n    }\\n\\n    /**\\n     * @return The address of the implementation.\\n     */\\n    function implementation() external view returns (address) {\\n        return _implementation();\\n    }\\n\\n    /**\\n     * @dev Upgrade the backing implementation of the proxy.\\n     * Only the admin can call this function.\\n     * @param newImplementation Address of the new implementation.\\n     */\\n    function upgradeTo(address newImplementation) external onlyGovernor {\\n        _upgradeTo(newImplementation);\\n    }\\n\\n    /**\\n     * @dev Upgrade the backing implementation of the proxy and call a function\\n     * on the new implementation.\\n     * This is useful to initialize the proxied contract.\\n     * @param newImplementation Address of the new implementation.\\n     * @param data Data to send as msg.data in the low level call.\\n     * It should include the signature and the parameters of the function to be called, as described in\\n     * https://solidity.readthedocs.io/en/v0.4.24/abi-spec.html#function-selector-and-argument-encoding.\\n     */\\n    function upgradeToAndCall(address newImplementation, bytes calldata data)\\n        external\\n        payable\\n        onlyGovernor\\n    {\\n        _upgradeTo(newImplementation);\\n        (bool success, ) = newImplementation.delegatecall(data);\\n        require(success);\\n    }\\n}\\n\",\"keccak256\":\"0xa86f13f5e8baa88830a6eabda549dd9ca654f2f54b8558b98599260ab4c3a274\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"author": "Origin Protocol Inc", "details": "This contract combines an upgradeability proxy with our governor system", "methods": {"admin()": {"return": "The address of the proxy admin/it's also the governor."}, "claimGovernance()": {"details": "Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor."}, "governor()": {"details": "Returns the address of the current Governor."}, "implementation()": {"return": "The address of the implementation."}, "initialize(address,address,bytes)": {"details": "Contract initializer with Governor enforcement", "params": {"_data": "Data to send as msg.data to the implementation to initialize the proxied contract. It should include the signature and the parameters of the function to be called, as described in https://solidity.readthedocs.io/en/v0.4.24/abi-spec.html#function-selector-and-argument-encoding. This parameter is optional, if no data is given the initialization call to proxied contract will be skipped.", "_initGovernor": "Address of the initial Governor.", "_logic": "Address of the initial implementation."}}, "isGovernor()": {"details": "Returns true if the caller is the current Governor."}, "transferGovernance(address)": {"details": "Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete", "params": {"_newGovernor": "Address of the new Governor"}}, "upgradeTo(address)": {"details": "Upgrade the backing implementation of the proxy. Only the admin can call this function.", "params": {"newImplementation": "Address of the new implementation."}}, "upgradeToAndCall(address,bytes)": {"details": "Upgrade the backing implementation of the proxy and call a function on the new implementation. This is useful to initialize the proxied contract.", "params": {"data": "Data to send as msg.data in the low level call. It should include the signature and the parameters of the function to be called, as described in https://solidity.readthedocs.io/en/v0.4.24/abi-spec.html#function-selector-and-argument-encoding.", "newImplementation": "Address of the new implementation."}}}, "title": "BaseGovernedUpgradeabilityProxy"}, "userdoc": {"methods": {}}}