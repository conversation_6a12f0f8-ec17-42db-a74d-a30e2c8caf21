{"name": "origin-dollar-contracts", "version": "0.1.0", "description": "Origin Dollar Contracts", "main": "index.js", "scripts": {"deploy": "(npx hardhat deploy --export '../dapp/network.json') && yarn run copy-interface-artifacts", "deploy:rinkeby": "(npx hardhat deploy --network rinkeby --verbose --export '../dapp/network.rinkeby.json') && yarn run copy-interface-artifacts", "deploy:mainnet": "(npx hardhat deploy --network mainnet --verbose --export '../dapp/network.mainnet.json') && yarn run copy-interface-artifacts", "node": "./node.sh", "node:fork": "./node.sh fork", "lint": "yarn run lint:js && yarn run lint:sol", "lint:js": "eslint \"test/**/*.js\"", "lint:sol": "solhint \"contracts/**/*.sol\"", "prettier": "yarn run prettier:js && yarn run prettier:sol", "prettier:js": "prettier --write \"*.js\" \"test/**/*.js\" \"deploy/**/*.js\" \"scripts/**/*.js\"", "prettier:sol": "prettier --write \"contracts/**/*.sol\"", "test": "IS_TEST=true npx hardhat test", "fund": "FORK=true npx hardhat fund --network localhost", "copy-interface-artifacts": "mkdir -p ../dapp/abis && cp artifacts/contracts/interfaces/IVault.sol/IVault.json ../dapp/abis/IVault.json && cp artifacts/contracts/liquidity/LiquidityReward.sol/LiquidityReward.json ../dapp/abis/LiquidityReward.json && cp artifacts/contracts/interfaces/uniswap/IUniswapV2Pair.sol/IUniswapV2Pair.json ../dapp/abis/IUniswapV2Pair.json && cp artifacts/contracts/staking/SingleAssetStaking.sol/SingleAssetStaking.json ../dapp/abis/SingleAssetStaking.json", "echidna": "yarn run clean && echidna-test . --contract PropertiesOUSDTransferable --config contracts/crytic/TestOUSDTransferable.yaml", "slither": "yarn run clean && slither . --filter-paths \"crytic|mocks|@openzeppelin\" --exclude-low --exclude-informational --exclude conformance-to-solidity-naming-conventions,different-pragma-directives-are-used,external-function,assembly", "clean": "rm -rf build crytic-export artifacts cache deployments/local*"}, "author": "Origin Protocol Inc <<EMAIL>>", "license": "MIT", "devDependencies": {"@nomiclabs/hardhat-ethers": "^2.0.0", "@nomiclabs/hardhat-solhint": "^2.0.0", "@nomiclabs/hardhat-waffle": "^2.0.0", "@openzeppelin/contracts": "2.5.0", "@openzeppelin/upgrades": "^2.8.0", "chai": "^4.2.0", "eslint": "^7.6.0", "ethereum-waffle": "^3.0.2", "ethers": "^5.0.0", "hardhat": "^2.0.2", "hardhat-contract-sizer": "^2.0.1", "hardhat-deploy": "^0.7.0-beta.18", "hardhat-deploy-ethers": "^0.3.0-beta.5", "prettier": "^2.0.5", "prettier-plugin-solidity": "^1.0.0-alpha.56", "solc": "0.5.16", "solhint": "^2.3.0", "solidifier": "^2.0.0", "solidity-coverage": "^0.7.10", "truffle": "^5.1.45", "truffle-flattener": "^1.5.0", "web3-utils": "^1.2.11"}}