// SPDX-License-Identifier: AGPL-3.0
pragma solidity ^0.8.10;

import {BridgeExecutorBase} from '../bridges/BridgeExecutorBase.sol';

contract SimpleBridgeExecutor is BridgeExecutorBase {
  constructor(
    uint256 delay,
    uint256 gracePeriod,
    uint256 minimumDelay,
    uint256 maximumDelay,
    address guardian
  ) BridgeExecutorBase(delay, gracePeriod, minimumDelay, maximumDelay, guardian) {}

  function queue(
    address[] memory targets,
    uint256[] memory values,
    string[] memory signatures,
    bytes[] memory calldatas,
    bool[] memory withDelegatecalls
  ) external {
    _queue(targets, values, signatures, calldatas, withDelegatecalls);
  }
}
