definition knownAsNonPrivileged(method f) returns bool  = false
  || f.selector == allocate().selector
  || f.selector == rebase().selector
  || f.selector == 0x0
  || f.selector == mint(address,uint256,uint256).selector
  || f.selector == redeemAll(uint256).selector
  || f.selector == redeem(uint256,uint256).selector
  || f.selector == mintMultiple(address[],uint256[],uint256).selector
; 

rule privilegedOperation(method f, address privileged)
description "$f can be called by more than one user without reverting"
{
	env e1;
	calldataarg arg;
	require !knownAsNonPrivileged(f);
	require e1.msg.sender == privileged;

	storage initialStorage = lastStorage;
	invoke f(e1, arg); // privileged succeeds executing candidate privileged operation.
	bool firstSucceeded = !lastReverted;

	env e2;
	calldataarg arg2;
	require e2.msg.sender != privileged;
	invoke f(e2, arg2) at initialStorage; // unprivileged
	bool secondSucceeded = !lastReverted;

	assert  !(firstSucceeded && secondSucceeded), "${f.selector} can be called by both ${e1.msg.sender} and ${e2.msg.sender}, so it is not privileged";
}
