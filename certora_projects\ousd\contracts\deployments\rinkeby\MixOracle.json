{"address": "******************************************", "abi": [{"constant": true, "inputs": [], "name": "governor", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "string", "name": "symbol", "type": "string"}], "name": "priceMin", "outputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "maxDrift", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "string", "name": "symbol", "type": "string"}], "name": "getTokenUSDOraclesLength", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "_minDrift", "type": "uint256"}, {"internalType": "uint256", "name": "_maxDrift", "type": "uint256"}], "name": "setMinMaxDrift", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "minDrift", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "claimGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "string", "name": "symbol", "type": "string"}], "name": "getTokenETHOraclesLength", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "string", "name": "symbol", "type": "string"}], "name": "priceMax", "outputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "address[]", "name": "ethOracles", "type": "address[]"}, {"internalType": "address[]", "name": "usdO<PERSON>les", "type": "address[]"}], "name": "registerTokenOracles", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "idx", "type": "uint256"}], "name": "getTokenETHOracle", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "isGovernor", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_newGovernor", "type": "address"}], "name": "transferGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "oracle", "type": "address"}], "name": "unregisterEthUsdOracle", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "oracle", "type": "address"}], "name": "registerEthUsdOracle", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "idx", "type": "uint256"}], "name": "getTokenUSDOracle", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "ethUsdOracles", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_maxDrift", "type": "uint256"}, {"internalType": "uint256", "name": "_minDrift", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "_minDrift", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_maxDrift", "type": "uint256"}], "name": "DriftsUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "_oracle", "type": "address"}], "name": "EthUsdOracleRegistered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "_oracle", "type": "address"}], "name": "EthUsdOracleDeregistered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "symbol", "type": "string"}, {"indexed": false, "internalType": "address[]", "name": "ethOracles", "type": "address[]"}, {"indexed": false, "internalType": "address[]", "name": "usdO<PERSON>les", "type": "address[]"}], "name": "TokenOracleRegistered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "PendingGovernorshipTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "GovernorshipTransferred", "type": "event"}], "transactionHash": "0x9d28fc59b386dd14a35211971b36583a08a542f88d0deaf2973fdc6b3d076a86", "receipt": {"to": null, "from": "******************************************", "contractAddress": "******************************************", "transactionIndex": 2, "gasUsed": "1452584", "logsBloom": "0x00000000400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000008000000000000000000000000000000000000000000000080000000000000000000020000000000000000000800000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000000080000000010000000000000000000000000000000000000000020000000800000000000000000000000000000000408000000000000000000800000", "blockHash": "0x8999160908812dc9323ab960de6a24cc0739f6fe46f8ba7b62fb9205421de2ed", "transactionHash": "0x9d28fc59b386dd14a35211971b36583a08a542f88d0deaf2973fdc6b3d076a86", "logs": [{"transactionIndex": 2, "blockNumber": 7803454, "transactionHash": "0x9d28fc59b386dd14a35211971b36583a08a542f88d0deaf2973fdc6b3d076a86", "address": "******************************************", "topics": ["0xc7c0c772add429241571afb3805861fb3cfa2af374534088b76cdb4325a87e9a", "0x0000000000000000000000000000000000000000000000000000000000000000", "0x000000000000000000000000d85a569f3c26f81070544451131c742283360400"], "data": "0x", "logIndex": 4, "blockHash": "0x8999160908812dc9323ab960de6a24cc0739f6fe46f8ba7b62fb9205421de2ed"}, {"transactionIndex": 2, "blockNumber": 7803454, "transactionHash": "0x9d28fc59b386dd14a35211971b36583a08a542f88d0deaf2973fdc6b3d076a86", "address": "******************************************", "topics": ["0x37cf419dd8a978e60861c3b0b3de9284d293490321c32d428e40c8f1f5a40b0e"], "data": "0x00000000000000000000000000000000000000000000000000000000042c1d800000000000000000000000000000000000000000000000000000000007bfa480", "logIndex": 5, "blockHash": "0x8999160908812dc9323ab960de6a24cc0739f6fe46f8ba7b62fb9205421de2ed"}], "blockNumber": 7803454, "cumulativeGasUsed": "1604902", "status": 1, "byzantium": true}, "args": [130000000, 70000000], "solcInputHash": "d641aaf0ed5d604d73e8f53c6d96978e", "metadata": "{\"compiler\":{\"version\":\"0.5.11+commit.22be8592.mod\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"constant\":true,\"inputs\":[],\"name\":\"governor\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"}],\"name\":\"priceMin\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"price\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"maxDrift\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"}],\"name\":\"getTokenUSDOraclesLength\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_minDrift\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_maxDrift\",\"type\":\"uint256\"}],\"name\":\"setMinMaxDrift\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"minDrift\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"claimGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"}],\"name\":\"getTokenETHOraclesLength\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"}],\"name\":\"priceMax\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"price\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"address[]\",\"name\":\"ethOracles\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"usdOracles\",\"type\":\"address[]\"}],\"name\":\"registerTokenOracles\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"idx\",\"type\":\"uint256\"}],\"name\":\"getTokenETHOracle\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"isGovernor\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_newGovernor\",\"type\":\"address\"}],\"name\":\"transferGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"oracle\",\"type\":\"address\"}],\"name\":\"unregisterEthUsdOracle\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"oracle\",\"type\":\"address\"}],\"name\":\"registerEthUsdOracle\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"idx\",\"type\":\"uint256\"}],\"name\":\"getTokenUSDOracle\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"ethUsdOracles\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_maxDrift\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_minDrift\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_minDrift\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_maxDrift\",\"type\":\"uint256\"}],\"name\":\"DriftsUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"_oracle\",\"type\":\"address\"}],\"name\":\"EthUsdOracleRegistered\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"_oracle\",\"type\":\"address\"}],\"name\":\"EthUsdOracleDeregistered\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"ethOracles\",\"type\":\"address[]\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"usdOracles\",\"type\":\"address[]\"}],\"name\":\"TokenOracleRegistered\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"PendingGovernorshipTransfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"GovernorshipTransferred\",\"type\":\"event\"}],\"devdoc\":{\"methods\":{\"claimGovernance()\":{\"details\":\"Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor.\"},\"getTokenETHOracle(string,uint256)\":{\"params\":{\"idx\":\"Index of the array value to return\",\"symbol\":\"Asset symbol. Example: \\\"DAI\\\"\"},\"return\":\"address of the oracle*\"},\"getTokenETHOraclesLength(string)\":{\"params\":{\"symbol\":\"Asset symbol. Example: \\\"DAI\\\"\"},\"return\":\"length of the ETH oracles array*\"},\"getTokenUSDOracle(string,uint256)\":{\"params\":{\"idx\":\"Index of the array value to return\",\"symbol\":\"Asset symbol. Example: \\\"DAI\\\"\"},\"return\":\"address of the oracle*\"},\"getTokenUSDOraclesLength(string)\":{\"params\":{\"symbol\":\"Asset symbol. Example: \\\"DAI\\\"\"},\"return\":\"length of the USD oracles array*\"},\"governor()\":{\"details\":\"Returns the address of the current Governor.\"},\"isGovernor()\":{\"details\":\"Returns true if the caller is the current Governor.\"},\"priceMax(string)\":{\"return\":\"symbol Asset symbol. Example: \\\"DAI\\\"price Max price from all the oracles, in USD with 8 decimal digits.*\"},\"priceMin(string)\":{\"return\":\"symbol Asset symbol. Example: \\\"DAI\\\"price Min price from all the oracles, in USD with 8 decimal digits.*\"},\"registerEthUsdOracle(address)\":{\"params\":{\"oracle\":\"Address of an oracle that implements the IEthUsdOracle interface.*\"}},\"registerTokenOracles(string,address[],address[])\":{\"params\":{\"ethOracles\":\"Addresses of oracles that implements the IEthUsdOracle interface and answers for this asset\",\"usdOracles\":\"Addresses of oracles that implements the IPriceOracle interface and answers for this asset*\"}},\"transferGovernance(address)\":{\"details\":\"Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete\",\"params\":{\"_newGovernor\":\"Address of the new Governor\"}},\"unregisterEthUsdOracle(address)\":{\"params\":{\"oracle\":\"Address of an oracle that implements the IEthUsdOracle interface.*\"}}}},\"userdoc\":{\"methods\":{\"getTokenETHOracle(string,uint256)\":{\"notice\":\"Returns the address of a specific ETH oracle\"},\"getTokenETHOraclesLength(string)\":{\"notice\":\"Returns the length of the ethOracles array for a given token\"},\"getTokenUSDOracle(string,uint256)\":{\"notice\":\"Returns the address of a specific USD oracle\"},\"getTokenUSDOraclesLength(string)\":{\"notice\":\"Returns the length of the usdOracles array for a given token\"},\"priceMax(string)\":{\"notice\":\"Returns max price of an asset in USD.\"},\"priceMin(string)\":{\"notice\":\"Returns the min price of an asset in USD.\"},\"registerEthUsdOracle(address)\":{\"notice\":\"Adds an oracle to the list of oracles to pull data from.\"},\"registerTokenOracles(string,address[],address[])\":{\"notice\":\"Adds an oracle to the list of oracles to pull data from.\"},\"unregisterEthUsdOracle(address)\":{\"notice\":\"Removes an oracle to the list of oracles to pull data from.\"}}}},\"settings\":{\"compilationTarget\":{\"contracts/oracle/MixOracle.sol\":\"MixOracle\"},\"evmVersion\":\"petersburg\",\"libraries\":{},\"metadata\":{\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"contracts/governance/Governable.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\n/**\\n * @title OUSD Governable Contract\\n * @dev Copy of the openzeppelin Ownable.sol contract with nomenclature change\\n *      from owner to governor and renounce methods removed. Does not use\\n *      Context.sol like Ownable.sol does for simplification.\\n * <AUTHOR> Protocol Inc\\n */\\ncontract Governable {\\n    // Storage position of the owner and pendingOwner of the contract\\n    // keccak256(\\\"OUSD.governor\\\");\\n    bytes32\\n        private constant governorPosition = 0x7bea13895fa79d2831e0a9e28edede30099005a50d652d8957cf8a607ee6ca4a;\\n\\n    // keccak256(\\\"OUSD.pending.governor\\\");\\n    bytes32\\n        private constant pendingGovernorPosition = 0x44c4d30b2eaad5130ad70c3ba6972730566f3e6359ab83e800d905c61b1c51db;\\n\\n    // keccak256(\\\"OUSD.reentry.status\\\");\\n    bytes32\\n        private constant reentryStatusPosition = 0x53bf423e48ed90e97d02ab0ebab13b2a235a6bfbe9c321847d5c175333ac4535;\\n\\n    // See OpenZeppelin ReentrancyGuard implementation\\n    uint256 constant _NOT_ENTERED = 1;\\n    uint256 constant _ENTERED = 2;\\n\\n    event PendingGovernorshipTransfer(\\n        address indexed previousGovernor,\\n        address indexed newGovernor\\n    );\\n\\n    event GovernorshipTransferred(\\n        address indexed previousGovernor,\\n        address indexed newGovernor\\n    );\\n\\n    /**\\n     * @dev Initializes the contract setting the deployer as the initial Governor.\\n     */\\n    constructor() internal {\\n        _setGovernor(msg.sender);\\n        emit GovernorshipTransferred(address(0), _governor());\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current Governor.\\n     */\\n    function governor() public view returns (address) {\\n        return _governor();\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current Governor.\\n     */\\n    function _governor() internal view returns (address governorOut) {\\n        bytes32 position = governorPosition;\\n        assembly {\\n            governorOut := sload(position)\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the address of the pending Governor.\\n     */\\n    function _pendingGovernor()\\n        internal\\n        view\\n        returns (address pendingGovernor)\\n    {\\n        bytes32 position = pendingGovernorPosition;\\n        assembly {\\n            pendingGovernor := sload(position)\\n        }\\n    }\\n\\n    /**\\n     * @dev Throws if called by any account other than the Governor.\\n     */\\n    modifier onlyGovernor() {\\n        require(isGovernor(), \\\"Caller is not the Governor\\\");\\n        _;\\n    }\\n\\n    /**\\n     * @dev Returns true if the caller is the current Governor.\\n     */\\n    function isGovernor() public view returns (bool) {\\n        return msg.sender == _governor();\\n    }\\n\\n    function _setGovernor(address newGovernor) internal {\\n        bytes32 position = governorPosition;\\n        assembly {\\n            sstore(position, newGovernor)\\n        }\\n    }\\n\\n    /**\\n     * @dev Prevents a contract from calling itself, directly or indirectly.\\n     * Calling a `nonReentrant` function from another `nonReentrant`\\n     * function is not supported. It is possible to prevent this from happening\\n     * by making the `nonReentrant` function external, and make it call a\\n     * `private` function that does the actual work.\\n     */\\n    modifier nonReentrant() {\\n        bytes32 position = reentryStatusPosition;\\n        uint256 _reentry_status;\\n        assembly {\\n            _reentry_status := sload(position)\\n        }\\n\\n        // On the first call to nonReentrant, _notEntered will be true\\n        require(_reentry_status != _ENTERED, \\\"Reentrant call\\\");\\n\\n        // Any calls to nonReentrant after this point will fail\\n        assembly {\\n            sstore(position, _ENTERED)\\n        }\\n\\n        _;\\n\\n        // By storing the original value once again, a refund is triggered (see\\n        // https://eips.ethereum.org/EIPS/eip-2200)\\n        assembly {\\n            sstore(position, _NOT_ENTERED)\\n        }\\n    }\\n\\n    function _setPendingGovernor(address newGovernor) internal {\\n        bytes32 position = pendingGovernorPosition;\\n        assembly {\\n            sstore(position, newGovernor)\\n        }\\n    }\\n\\n    /**\\n     * @dev Transfers Governance of the contract to a new account (`newGovernor`).\\n     * Can only be called by the current Governor. Must be claimed for this to complete\\n     * @param _newGovernor Address of the new Governor\\n     */\\n    function transferGovernance(address _newGovernor) external onlyGovernor {\\n        _setPendingGovernor(_newGovernor);\\n        emit PendingGovernorshipTransfer(_governor(), _newGovernor);\\n    }\\n\\n    /**\\n     * @dev Claim Governance of the contract to a new account (`newGovernor`).\\n     * Can only be called by the new Governor.\\n     */\\n    function claimGovernance() external {\\n        require(\\n            msg.sender == _pendingGovernor(),\\n            \\\"Only the pending Governor can complete the claim\\\"\\n        );\\n        _changeGovernor(msg.sender);\\n    }\\n\\n    /**\\n     * @dev Change Governance of the contract to a new account (`newGovernor`).\\n     * @param _newGovernor Address of the new Governor\\n     */\\n    function _changeGovernor(address _newGovernor) internal {\\n        require(_newGovernor != address(0), \\\"New Governor is address(0)\\\");\\n        emit GovernorshipTransferred(_governor(), _newGovernor);\\n        _setGovernor(_newGovernor);\\n    }\\n}\\n\",\"keccak256\":\"0x3e51ea48102945bf4b305bf9722a07514a585a29555d92f8c84352d1a4cfcee1\"},\"contracts/interfaces/IEthUsdOracle.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\ninterface IEthUsdOracle {\\n    /**\\n     * @notice Returns ETH price in USD.\\n     * @return Price in USD with 6 decimal digits.\\n     */\\n    function ethUsdPrice() external view returns (uint256);\\n\\n    /**\\n     * @notice Returns token price in USD.\\n     * @param symbol. Asset symbol. For ex. \\\"DAI\\\".\\n     * @return Price in USD with 6 decimal digits.\\n     */\\n    function tokUsdPrice(string calldata symbol)\\n        external\\n        view\\n        returns (uint256);\\n\\n    /**\\n     * @notice Returns the asset price in ETH.\\n     * @param symbol. Asset symbol. For ex. \\\"DAI\\\".\\n     * @return Price in ETH with 8 decimal digits.\\n     */\\n    function tokEthPrice(string calldata symbol)\\n        external\\n        view\\n        returns (uint256);\\n}\\n\\ninterface IViewEthUsdOracle {\\n    /**\\n     * @notice Returns ETH price in USD.\\n     * @return Price in USD with 6 decimal digits.\\n     */\\n    function ethUsdPrice() external view returns (uint256);\\n\\n    /**\\n     * @notice Returns token price in USD.\\n     * @param symbol. Asset symbol. For ex. \\\"DAI\\\".\\n     * @return Price in USD with 6 decimal digits.\\n     */\\n    function tokUsdPrice(string calldata symbol)\\n        external\\n        view\\n        returns (uint256);\\n\\n    /**\\n     * @notice Returns the asset price in ETH.\\n     * @param symbol. Asset symbol. For ex. \\\"DAI\\\".\\n     * @return Price in ETH with 8 decimal digits.\\n     */\\n    function tokEthPrice(string calldata symbol)\\n        external\\n        view\\n        returns (uint256);\\n}\\n\",\"keccak256\":\"0xa57ebbb67fc35436a173988dcefc8b35811ae2a5777ba2d7d734101e41e901a3\"},\"contracts/interfaces/IMinMaxOracle.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\ninterface IMinMaxOracle {\\n    //Assuming 8 decimals\\n    function priceMin(string calldata symbol) external view returns (uint256);\\n\\n    function priceMax(string calldata symbol) external view returns (uint256);\\n}\\n\",\"keccak256\":\"0x164c8759ca5a8e39bbe1de6b2504098c543b2f15663c9d452e083418f8313f48\"},\"contracts/interfaces/IPriceOracle.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\ninterface IPriceOracle {\\n    /**\\n     * @dev returns the asset price in USD, 6 decimal digits.\\n     * Compatible with the Open Price Feed.\\n     */\\n    function price(string calldata symbol) external view returns (uint256);\\n}\\n\",\"keccak256\":\"0x943aaa48eecafcbe186d21d3e4d7a2dbe356e45611b4d9794f0df63ba9abdec7\"},\"contracts/oracle/MixOracle.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\n/**\\n * @title OUSD MixOracle Contract\\n * @notice The MixOracle pulls exchange rate from multiple oracles and returns\\n *         min and max values.\\n * <AUTHOR> Protocol Inc\\n */\\nimport { IPriceOracle } from \\\"../interfaces/IPriceOracle.sol\\\";\\nimport { IEthUsdOracle } from \\\"../interfaces/IEthUsdOracle.sol\\\";\\nimport { IMinMaxOracle } from \\\"../interfaces/IMinMaxOracle.sol\\\";\\nimport { Governable } from \\\"../governance/Governable.sol\\\";\\n\\ncontract MixOracle is IMinMaxOracle, Governable {\\n    event DriftsUpdated(uint256 _minDrift, uint256 _maxDrift);\\n    event EthUsdOracleRegistered(address _oracle);\\n    event EthUsdOracleDeregistered(address _oracle);\\n    event TokenOracleRegistered(\\n        string symbol,\\n        address[] ethOracles,\\n        address[] usdOracles\\n    );\\n\\n    address[] public ethUsdOracles;\\n\\n    struct MixConfig {\\n        address[] usdOracles;\\n        address[] ethOracles;\\n    }\\n\\n    mapping(bytes32 => MixConfig) configs;\\n\\n    uint256 constant MAX_INT = 2**256 - 1;\\n    uint256 public maxDrift;\\n    uint256 public minDrift;\\n\\n    constructor(uint256 _maxDrift, uint256 _minDrift) public {\\n        maxDrift = _maxDrift;\\n        minDrift = _minDrift;\\n        emit DriftsUpdated(_minDrift, _maxDrift);\\n    }\\n\\n    function setMinMaxDrift(uint256 _minDrift, uint256 _maxDrift)\\n        public\\n        onlyGovernor\\n    {\\n        minDrift = _minDrift;\\n        maxDrift = _maxDrift;\\n        emit DriftsUpdated(_minDrift, _maxDrift);\\n    }\\n\\n    /**\\n     * @notice Adds an oracle to the list of oracles to pull data from.\\n     * @param oracle Address of an oracle that implements the IEthUsdOracle interface.\\n     **/\\n    function registerEthUsdOracle(address oracle) public onlyGovernor {\\n        for (uint256 i = 0; i < ethUsdOracles.length; i++) {\\n            require(ethUsdOracles[i] != oracle, \\\"Oracle already registered.\\\");\\n        }\\n        ethUsdOracles.push(oracle);\\n        emit EthUsdOracleRegistered(oracle);\\n    }\\n\\n    /**\\n     * @notice Removes an oracle to the list of oracles to pull data from.\\n     * @param oracle Address of an oracle that implements the IEthUsdOracle interface.\\n     **/\\n    function unregisterEthUsdOracle(address oracle) public onlyGovernor {\\n        for (uint256 i = 0; i < ethUsdOracles.length; i++) {\\n            if (ethUsdOracles[i] == oracle) {\\n                // swap with the last element of the array, and then delete last element (could be itself)\\n                ethUsdOracles[i] = ethUsdOracles[ethUsdOracles.length - 1];\\n                delete ethUsdOracles[ethUsdOracles.length - 1];\\n                emit EthUsdOracleDeregistered(oracle);\\n                ethUsdOracles.pop();\\n                return;\\n            }\\n        }\\n        revert(\\\"Oracle not found\\\");\\n    }\\n\\n    /**\\n     * @notice Adds an oracle to the list of oracles to pull data from.\\n     * @param ethOracles Addresses of oracles that implements the IEthUsdOracle interface and answers for this asset\\n     * @param usdOracles Addresses of oracles that implements the IPriceOracle interface and answers for this asset\\n     **/\\n    function registerTokenOracles(\\n        string calldata symbol,\\n        address[] calldata ethOracles,\\n        address[] calldata usdOracles\\n    ) external onlyGovernor {\\n        MixConfig storage config = configs[keccak256(abi.encodePacked(symbol))];\\n        config.ethOracles = ethOracles;\\n        config.usdOracles = usdOracles;\\n        emit TokenOracleRegistered(symbol, ethOracles, usdOracles);\\n    }\\n\\n    /**\\n     * @notice Returns the min price of an asset in USD.\\n     * @return symbol Asset symbol. Example: \\\"DAI\\\"\\n     * @return price Min price from all the oracles, in USD with 8 decimal digits.\\n     **/\\n    function priceMin(string calldata symbol)\\n        external\\n        view\\n        returns (uint256 price)\\n    {\\n        MixConfig storage config = configs[keccak256(abi.encodePacked(symbol))];\\n        uint256 ep;\\n        uint256 p; //holder variables\\n        price = MAX_INT;\\n        if (config.ethOracles.length > 0) {\\n            ep = MAX_INT;\\n            for (uint256 i = 0; i < config.ethOracles.length; i++) {\\n                p = IEthUsdOracle(config.ethOracles[i]).tokEthPrice(symbol);\\n                if (ep > p) {\\n                    ep = p;\\n                }\\n            }\\n            price = ep;\\n            ep = MAX_INT;\\n            for (uint256 i = 0; i < ethUsdOracles.length; i++) {\\n                p = IEthUsdOracle(ethUsdOracles[i]).ethUsdPrice();\\n                if (ep > p) {\\n                    ep = p;\\n                }\\n            }\\n            if (price != MAX_INT && ep != MAX_INT) {\\n                // tokEthPrice has precision of 8 which ethUsdPrice has precision of 6\\n                // we want precision of 8\\n                price = (price * ep) / 1e6;\\n            }\\n        }\\n\\n        if (config.usdOracles.length > 0) {\\n            for (uint256 i = 0; i < config.usdOracles.length; i++) {\\n                // upscale by 2 since price oracles are precision 6\\n                p = IPriceOracle(config.usdOracles[i]).price(symbol) * 1e2;\\n                if (price > p) {\\n                    price = p;\\n                }\\n            }\\n        }\\n        require(price <= maxDrift, \\\"Price exceeds maxDrift\\\");\\n        require(price >= minDrift, \\\"Price below minDrift\\\");\\n        require(\\n            price != MAX_INT,\\n            \\\"None of our oracles returned a valid min price!\\\"\\n        );\\n    }\\n\\n    /**\\n     * @notice Returns max price of an asset in USD.\\n     * @return symbol Asset symbol. Example: \\\"DAI\\\"\\n     * @return price Max price from all the oracles, in USD with 8 decimal digits.\\n     **/\\n    function priceMax(string calldata symbol)\\n        external\\n        view\\n        returns (uint256 price)\\n    {\\n        MixConfig storage config = configs[keccak256(abi.encodePacked(symbol))];\\n        uint256 ep;\\n        uint256 p; //holder variables\\n        price = 0;\\n        if (config.ethOracles.length > 0) {\\n            ep = 0;\\n            for (uint256 i = 0; i < config.ethOracles.length; i++) {\\n                p = IEthUsdOracle(config.ethOracles[i]).tokEthPrice(symbol);\\n                if (ep < p) {\\n                    ep = p;\\n                }\\n            }\\n            price = ep;\\n            ep = 0;\\n            for (uint256 i = 0; i < ethUsdOracles.length; i++) {\\n                p = IEthUsdOracle(ethUsdOracles[i]).ethUsdPrice();\\n                if (ep < p) {\\n                    ep = p;\\n                }\\n            }\\n            if (price != 0 && ep != 0) {\\n                // tokEthPrice has precision of 8 which ethUsdPrice has precision of 6\\n                // we want precision of 8\\n                price = (price * ep) / 1e6;\\n            }\\n        }\\n\\n        if (config.usdOracles.length > 0) {\\n            for (uint256 i = 0; i < config.usdOracles.length; i++) {\\n                // upscale by 2 since price oracles are precision 6\\n                p = IPriceOracle(config.usdOracles[i]).price(symbol) * 1e2;\\n                if (price < p) {\\n                    price = p;\\n                }\\n            }\\n        }\\n\\n        require(price <= maxDrift, \\\"Price exceeds maxDrift\\\");\\n        require(price >= minDrift, \\\"Price below minDrift\\\");\\n        require(price != 0, \\\"None of our oracles returned a valid max price!\\\");\\n    }\\n\\n    /**\\n     * @notice Returns the length of the usdOracles array for a given token\\n     * @param symbol Asset symbol. Example: \\\"DAI\\\"\\n     * @return length of the USD oracles array\\n     **/\\n    function getTokenUSDOraclesLength(string calldata symbol)\\n        external\\n        view\\n        returns (uint256)\\n    {\\n        MixConfig storage config = configs[keccak256(abi.encodePacked(symbol))];\\n        return config.usdOracles.length;\\n    }\\n\\n    /**\\n     * @notice Returns the address of a specific USD oracle\\n     * @param symbol Asset symbol. Example: \\\"DAI\\\"\\n     * @param idx Index of the array value to return\\n     * @return address of the oracle\\n     **/\\n    function getTokenUSDOracle(string calldata symbol, uint256 idx)\\n        external\\n        view\\n        returns (address)\\n    {\\n        MixConfig storage config = configs[keccak256(abi.encodePacked(symbol))];\\n        return config.usdOracles[idx];\\n    }\\n\\n    /**\\n     * @notice Returns the length of the ethOracles array for a given token\\n     * @param symbol Asset symbol. Example: \\\"DAI\\\"\\n     * @return length of the ETH oracles array\\n     **/\\n    function getTokenETHOraclesLength(string calldata symbol)\\n        external\\n        view\\n        returns (uint256)\\n    {\\n        MixConfig storage config = configs[keccak256(abi.encodePacked(symbol))];\\n        return config.ethOracles.length;\\n    }\\n\\n    /**\\n     * @notice Returns the address of a specific ETH oracle\\n     * @param symbol Asset symbol. Example: \\\"DAI\\\"\\n     * @param idx Index of the array value to return\\n     * @return address of the oracle\\n     **/\\n    function getTokenETHOracle(string calldata symbol, uint256 idx)\\n        external\\n        view\\n        returns (address)\\n    {\\n        MixConfig storage config = configs[keccak256(abi.encodePacked(symbol))];\\n        return config.ethOracles[idx];\\n    }\\n}\\n\",\"keccak256\":\"0x1fd0b0f067a63c100276d01329607233a492571b37d78f03bda8fa6a7baa8a27\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"methods": {"claimGovernance()": {"details": "Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor."}, "getTokenETHOracle(string,uint256)": {"params": {"idx": "Index of the array value to return", "symbol": "Asset symbol. Example: \"DAI\""}, "return": "address of the oracle*"}, "getTokenETHOraclesLength(string)": {"params": {"symbol": "Asset symbol. Example: \"DAI\""}, "return": "length of the ETH oracles array*"}, "getTokenUSDOracle(string,uint256)": {"params": {"idx": "Index of the array value to return", "symbol": "Asset symbol. Example: \"DAI\""}, "return": "address of the oracle*"}, "getTokenUSDOraclesLength(string)": {"params": {"symbol": "Asset symbol. Example: \"DAI\""}, "return": "length of the USD oracles array*"}, "governor()": {"details": "Returns the address of the current Governor."}, "isGovernor()": {"details": "Returns true if the caller is the current Governor."}, "priceMax(string)": {"return": "symbol Asset symbol. Example: \"DAI\"price Max price from all the oracles, in USD with 8 decimal digits.*"}, "priceMin(string)": {"return": "symbol Asset symbol. Example: \"DAI\"price Min price from all the oracles, in USD with 8 decimal digits.*"}, "registerEthUsdOracle(address)": {"params": {"oracle": "Address of an oracle that implements the IEthUsdOracle interface.*"}}, "registerTokenOracles(string,address[],address[])": {"params": {"ethOracles": "Addresses of oracles that implements the IEthUsdOracle interface and answers for this asset", "usdOracles": "Addresses of oracles that implements the IPriceOracle interface and answers for this asset*"}}, "transferGovernance(address)": {"details": "Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete", "params": {"_newGovernor": "Address of the new Governor"}}, "unregisterEthUsdOracle(address)": {"params": {"oracle": "Address of an oracle that implements the IEthUsdOracle interface.*"}}}}, "userdoc": {"methods": {"getTokenETHOracle(string,uint256)": {"notice": "Returns the address of a specific ETH oracle"}, "getTokenETHOraclesLength(string)": {"notice": "Returns the length of the ethOracles array for a given token"}, "getTokenUSDOracle(string,uint256)": {"notice": "Returns the address of a specific USD oracle"}, "getTokenUSDOraclesLength(string)": {"notice": "Returns the length of the usdOracles array for a given token"}, "priceMax(string)": {"notice": "Returns max price of an asset in USD."}, "priceMin(string)": {"notice": "Returns the min price of an asset in USD."}, "registerEthUsdOracle(address)": {"notice": "Adds an oracle to the list of oracles to pull data from."}, "registerTokenOracles(string,address[],address[])": {"notice": "Adds an oracle to the list of oracles to pull data from."}, "unregisterEthUsdOracle(address)": {"notice": "Removes an oracle to the list of oracles to pull data from."}}}}