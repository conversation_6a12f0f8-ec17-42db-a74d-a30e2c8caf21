// Copyright 2021-2022, Offchain Labs, Inc.
// For license information, see https://github.com/nitro/blob/master/LICENSE
// SPDX-License-Identifier: BUSL-1.1

// solhint-disable-next-line compiler-version
pragma solidity >=0.6.9 <0.9.0;

import './IBridge.sol';
import './IDelayedMessageProvider.sol';
import './ISequencerInbox.sol';

interface IInbox is IDelayedMessageProvider {
  function bridge() external view returns (IBridge);

  function sequencerInbox() external view returns (ISequencerInbox);

  /**
   * @notice Send a generic L2 message to the chain
   * @dev This method is an optimization to avoid having to emit the entirety of the messageData in a log. Instead validators are expected to be able to parse the data from the transaction's input
   * @param messageData Data of the message being sent
   */
  function sendL2MessageFromOrigin(bytes calldata messageData) external returns (uint256);

  /**
   * @notice Send a generic L2 message to the chain
   * @dev This method can be used to send any type of message that doesn't require L1 validation
   * @param messageData Data of the message being sent
   */
  function sendL2Message(bytes calldata messageData) external returns (uint256);

  function sendL1FundedUnsignedTransaction(
    uint256 gasLimit,
    uint256 maxFeePerGas,
    uint256 nonce,
    address to,
    bytes calldata data
  ) external payable returns (uint256);

  function sendL1FundedContractTransaction(
    uint256 gasLimit,
    uint256 maxFeePerGas,
    address to,
    bytes calldata data
  ) external payable returns (uint256);

  function sendUnsignedTransaction(
    uint256 gasLimit,
    uint256 maxFeePerGas,
    uint256 nonce,
    address to,
    uint256 value,
    bytes calldata data
  ) external returns (uint256);

  function sendContractTransaction(
    uint256 gasLimit,
    uint256 maxFeePerGas,
    address to,
    uint256 value,
    bytes calldata data
  ) external returns (uint256);

  /**
   * @notice Get the L1 fee for submitting a retryable
   * @dev This fee can be paid by funds already in the L2 aliased address or by the current message value
   * @dev This formula may change in the future, to future proof your code query this method instead of inlining!!
   * @param dataLength The length of the retryable's calldata, in bytes
   * @param baseFee The block basefee when the retryable is included in the chain, if 0 current block.basefee will be used
   */
  function calculateRetryableSubmissionFee(uint256 dataLength, uint256 baseFee)
    external
    view
    returns (uint256);

  /**
   * @notice Deposit eth from L1 to L2
   * @dev This does not trigger the fallback function when receiving in the L2 side.
   *      Look into retryable tickets if you are interested in this functionality.
   * @dev This function should not be called inside contract constructors
   */
  function depositEth() external payable returns (uint256);

  /**
   * @notice Put a message in the L2 inbox that can be reexecuted for some fixed amount of time if it reverts
   * @dev all msg.value will deposited to callValueRefundAddress on L2
   * @dev Gas limit and maxFeePerGas should not be set to 1 as that is used to trigger the RetryableData error
   * @param to destination L2 contract address
   * @param l2CallValue call value for retryable L2 message
   * @param maxSubmissionCost Max gas deducted from user's L2 balance to cover base submission fee
   * @param excessFeeRefundAddress gasLimit x maxFeePerGas - execution cost gets credited here on L2 balance
   * @param callValueRefundAddress l2Callvalue gets credited here on L2 if retryable txn times out or gets cancelled
   * @param gasLimit Max gas deducted from user's L2 balance to cover L2 execution. Should not be set to 1 (magic value used to trigger the RetryableData error)
   * @param maxFeePerGas price bid for L2 execution. Should not be set to 1 (magic value used to trigger the RetryableData error)
   * @param data ABI encoded data of L2 message
   * @return unique message number of the retryable transaction
   */
  function createRetryableTicket(
    address to,
    uint256 l2CallValue,
    uint256 maxSubmissionCost,
    address excessFeeRefundAddress,
    address callValueRefundAddress,
    uint256 gasLimit,
    uint256 maxFeePerGas,
    bytes calldata data
  ) external payable returns (uint256);

  /**
   * @notice Put a message in the L2 inbox that can be reexecuted for some fixed amount of time if it reverts
   * @dev Same as createRetryableTicket, but does not guarantee that submission will succeed by requiring the needed funds
   * come from the deposit alone, rather than falling back on the user's L2 balance
   * @dev Advanced usage only (does not rewrite aliases for excessFeeRefundAddress and callValueRefundAddress).
   * createRetryableTicket method is the recommended standard.
   * @dev Gas limit and maxFeePerGas should not be set to 1 as that is used to trigger the RetryableData error
   * @param to destination L2 contract address
   * @param l2CallValue call value for retryable L2 message
   * @param maxSubmissionCost Max gas deducted from user's L2 balance to cover base submission fee
   * @param excessFeeRefundAddress gasLimit x maxFeePerGas - execution cost gets credited here on L2 balance
   * @param callValueRefundAddress l2Callvalue gets credited here on L2 if retryable txn times out or gets cancelled
   * @param gasLimit Max gas deducted from user's L2 balance to cover L2 execution. Should not be set to 1 (magic value used to trigger the RetryableData error)
   * @param maxFeePerGas price bid for L2 execution. Should not be set to 1 (magic value used to trigger the RetryableData error)
   * @param data ABI encoded data of L2 message
   * @return unique message number of the retryable transaction
   */
  function unsafeCreateRetryableTicket(
    address to,
    uint256 l2CallValue,
    uint256 maxSubmissionCost,
    address excessFeeRefundAddress,
    address callValueRefundAddress,
    uint256 gasLimit,
    uint256 maxFeePerGas,
    bytes calldata data
  ) external payable returns (uint256);

  // ---------- onlyRollupOrOwner functions ----------

  /// @notice pauses all inbox functionality
  function pause() external;

  /// @notice unpauses all inbox functionality
  function unpause() external;

  // ---------- initializer ----------

  /**
   * @dev function to be called one time during the inbox upgrade process
   *      this is used to fix the storage slots
   */
  function postUpgradeInit(IBridge _bridge) external;

  function initialize(IBridge _bridge, ISequencerInbox _sequencerInbox) external;
}
