{"address": "0x0Ed143d1D6d98f10db7151b66D76aA3956072b5a", "abi": [{"constant": true, "inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "getAllStakes", "outputs": [{"components": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "end", "type": "uint256"}, {"internalType": "uint256", "name": "duration", "type": "uint256"}, {"internalType": "uint240", "name": "rate", "type": "uint240"}, {"internalType": "bool", "name": "paid", "type": "bool"}, {"internalType": "uint8", "name": "stakeType", "type": "uint8"}], "internalType": "struct SingleAssetStaking.Stake[]", "name": "", "type": "tuple[]"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "governor", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "totalOutstanding", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "bool", "name": "_paused", "type": "bool"}], "name": "setPaused", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256[]", "name": "_durations", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "_rates", "type": "uint256[]"}], "name": "setDurationRates", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint8", "name": "_stakeType", "type": "uint8"}, {"internalType": "bytes32", "name": "_rootHash", "type": "bytes32"}, {"internalType": "uint256", "name": "_proofDepth", "type": "uint256"}], "name": "setAirDropRoot", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}, {"internalType": "uint8", "name": "stakeType", "type": "uint8"}, {"internalType": "uint256", "name": "duration", "type": "uint256"}, {"internalType": "uint256", "name": "rate", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes32[]", "name": "merkleProof", "type": "bytes32[]"}], "name": "airDroppedStake", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "claimGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "staker", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "duration", "type": "uint256"}], "name": "stakeWithSender", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "stakingToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "duration", "type": "uint256"}], "name": "stake", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "uint256", "name": "_duration", "type": "uint256"}], "name": "durationRewardRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "totalCurrentHoldings", "outputs": [{"internalType": "uint256", "name": "total", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "totalStaked", "outputs": [{"internalType": "uint256", "name": "total", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint8", "name": "stakeType", "type": "uint8"}], "name": "airDroppedStakeClaimed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "userStakes", "outputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "end", "type": "uint256"}, {"internalType": "uint256", "name": "duration", "type": "uint256"}, {"internalType": "uint240", "name": "rate", "type": "uint240"}, {"internalType": "bool", "name": "paid", "type": "bool"}, {"internalType": "uint8", "name": "stakeType", "type": "uint8"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "durations", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "getAllDurations", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "isGovernor", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_newGovernor", "type": "address"}], "name": "transferGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "rates", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "name": "dropRoots", "outputs": [{"internalType": "bytes32", "name": "hash", "type": "bytes32"}, {"internalType": "uint256", "name": "depth", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_stakingToken", "type": "address"}, {"internalType": "uint256[]", "name": "_durations", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "_rates", "type": "uint256[]"}], "name": "initialize", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [], "name": "exit", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "totalExpectedRewards", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "getAllRates", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "payable": false, "stateMutability": "view", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "duration", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "rate", "type": "uint256"}], "name": "Staked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "stakedAmount", "type": "uint256"}], "name": "Withdrawn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "yes", "type": "bool"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256[]", "name": "durations", "type": "uint256[]"}], "name": "NewDurations", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256[]", "name": "rates", "type": "uint256[]"}], "name": "NewRates", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "stakeType", "type": "uint8"}, {"indexed": false, "internalType": "bytes32", "name": "rootHash", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "proof<PERSON><PERSON>h", "type": "uint256"}], "name": "NewAirDropRootHash", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "PendingGovernorshipTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "GovernorshipTransferred", "type": "event"}], "transactionHash": "0x9a998931bf39359cbfc6c186f28b3741e10102738241891a53fb005f3e109a5e", "receipt": {"to": null, "from": "0x71F78361537A6f7B6818e7A760c8bC0146D93f50", "contractAddress": "0x0Ed143d1D6d98f10db7151b66D76aA3956072b5a", "transactionIndex": 86, "gasUsed": "2518562", "logsBloom": "0x00000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000100000020000000000000000000800000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000080000000000000000000100010000000000000000000000200000000000000000020000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x95e8e58e6bb9324431a63bc6fcfb7e1d40e7d882b995cb3b0d99ded0806f6a5a", "transactionHash": "0x9a998931bf39359cbfc6c186f28b3741e10102738241891a53fb005f3e109a5e", "logs": [{"transactionIndex": 86, "blockNumber": ********, "transactionHash": "0x9a998931bf39359cbfc6c186f28b3741e10102738241891a53fb005f3e109a5e", "address": "0x0Ed143d1D6d98f10db7151b66D76aA3956072b5a", "topics": ["0xc7c0c772add429241571afb3805861fb3cfa2af374534088b76cdb4325a87e9a", "0x0000000000000000000000000000000000000000000000000000000000000000", "0x00000000000000000000000071f78361537a6f7b6818e7a760c8bc0146d93f50"], "data": "0x", "logIndex": 127, "blockHash": "0x95e8e58e6bb9324431a63bc6fcfb7e1d40e7d882b995cb3b0d99ded0806f6a5a"}], "blockNumber": ********, "cumulativeGasUsed": "8156154", "status": 1, "byzantium": true}, "args": [], "solcInputHash": "c7459e40fa663addef2a13f82ab72194", "metadata": "{\"compiler\":{\"version\":\"0.5.11+commit.22be8592.mod\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"getAllStakes\",\"outputs\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"end\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"duration\",\"type\":\"uint256\"},{\"internalType\":\"uint240\",\"name\":\"rate\",\"type\":\"uint240\"},{\"internalType\":\"bool\",\"name\":\"paid\",\"type\":\"bool\"},{\"internalType\":\"uint8\",\"name\":\"stakeType\",\"type\":\"uint8\"}],\"internalType\":\"struct SingleAssetStaking.Stake[]\",\"name\":\"\",\"type\":\"tuple[]\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"governor\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"totalOutstanding\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"bool\",\"name\":\"_paused\",\"type\":\"bool\"}],\"name\":\"setPaused\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256[]\",\"name\":\"_durations\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"_rates\",\"type\":\"uint256[]\"}],\"name\":\"setDurationRates\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint8\",\"name\":\"_stakeType\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"_rootHash\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"_proofDepth\",\"type\":\"uint256\"}],\"name\":\"setAirDropRoot\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"},{\"internalType\":\"uint8\",\"name\":\"stakeType\",\"type\":\"uint8\"},{\"internalType\":\"uint256\",\"name\":\"duration\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"rate\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"bytes32[]\",\"name\":\"merkleProof\",\"type\":\"bytes32[]\"}],\"name\":\"airDroppedStake\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"paused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"claimGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"duration\",\"type\":\"uint256\"}],\"name\":\"stakeWithSender\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"stakingToken\",\"outputs\":[{\"internalType\":\"contract IERC20\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"duration\",\"type\":\"uint256\"}],\"name\":\"stake\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_duration\",\"type\":\"uint256\"}],\"name\":\"durationRewardRate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"totalCurrentHoldings\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"total\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"totalStaked\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"total\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint8\",\"name\":\"stakeType\",\"type\":\"uint8\"}],\"name\":\"airDroppedStakeClaimed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"userStakes\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"end\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"duration\",\"type\":\"uint256\"},{\"internalType\":\"uint240\",\"name\":\"rate\",\"type\":\"uint240\"},{\"internalType\":\"bool\",\"name\":\"paid\",\"type\":\"bool\"},{\"internalType\":\"uint8\",\"name\":\"stakeType\",\"type\":\"uint8\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"durations\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"getAllDurations\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"\",\"type\":\"uint256[]\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"isGovernor\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_newGovernor\",\"type\":\"address\"}],\"name\":\"transferGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"rates\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"name\":\"dropRoots\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"hash\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"depth\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_stakingToken\",\"type\":\"address\"},{\"internalType\":\"uint256[]\",\"name\":\"_durations\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"_rates\",\"type\":\"uint256[]\"}],\"name\":\"initialize\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"exit\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"totalExpectedRewards\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"getAllRates\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"\",\"type\":\"uint256[]\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"duration\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"rate\",\"type\":\"uint256\"}],\"name\":\"Staked\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"stakedAmount\",\"type\":\"uint256\"}],\"name\":\"Withdrawn\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"yes\",\"type\":\"bool\"}],\"name\":\"Paused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"durations\",\"type\":\"uint256[]\"}],\"name\":\"NewDurations\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"rates\",\"type\":\"uint256[]\"}],\"name\":\"NewRates\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint8\",\"name\":\"stakeType\",\"type\":\"uint8\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"rootHash\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"proofDepth\",\"type\":\"uint256\"}],\"name\":\"NewAirDropRootHash\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"PendingGovernorshipTransfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"GovernorshipTransferred\",\"type\":\"event\"}],\"devdoc\":{\"methods\":{\"airDroppedStake(uint256,uint8,uint256,uint256,uint256,bytes32[])\":{\"details\":\"Make a preapproved stake for the user, this is a presigned voucher that the user can redeem either from     an airdrop or a compensation program.     Only 1 of each type is allowed per user. The proof must match the root hash\",\"params\":{\"amount\":\"Number of tokens to stake in 1e18\",\"duration\":\"Number of seconds this stake will be held for\",\"index\":\"Number that is zero base index of the stake in the payout entry\",\"merkleProof\":\"Array of proofs for that amount\",\"rate\":\"Rate(0.3 is 30%) of reward for this stake in 1e18, uint240 to fit the bool and type in struct Stake\",\"stakeType\":\"Number that represent the type of the stake, must not be 0 which is user stake\"}},\"airDroppedStakeClaimed(address,uint8)\":{\"details\":\"Has the airdropped stake already been claimed\"},\"claimGovernance()\":{\"details\":\"Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor.\"},\"durationRewardRate(uint256)\":{\"details\":\"Find the rate that corresponds to a given duration\",\"params\":{\"_duration\":\"Number of seconds\"}},\"exit()\":{\"details\":\"Exit out of all possible stakes\"},\"getAllStakes(address)\":{\"details\":\"Return all the stakes paid and unpaid for a given user\",\"params\":{\"account\":\"Address of the account that we want to look up\"}},\"governor()\":{\"details\":\"Returns the address of the current Governor.\"},\"initialize(address,uint256[],uint256[])\":{\"details\":\"Initialize the contracts, sets up durations, rates, and preApprover     for preApproved contracts can only be called once\",\"params\":{\"_durations\":\"Array of allowed durations in seconds\",\"_rates\":\"Array of rates(0.3 is 30%) that correspond to the allowed              durations in 1e18 precision\",\"_stakingToken\":\"Address of the token that we are staking\"}},\"isGovernor()\":{\"details\":\"Returns true if the caller is the current Governor.\"},\"setAirDropRoot(uint8,bytes32,uint256)\":{\"details\":\"Set air drop root for a specific stake type\",\"params\":{\"_proofDepth\":\"Depth of the Merklke Tree\",\"_rootHash\":\"Root hash of the Merkle Tree\",\"_stakeType\":\"Type of staking must be greater than 0\"}},\"setDurationRates(uint256[],uint256[])\":{\"details\":\"Set new durations and rates will not effect existing stakes\",\"params\":{\"_durations\":\"Array of durations in seconds\",\"_rates\":\"Array of rates that corresponds to the durations (0.01 is 1%) in 1e18\"}},\"stake(uint256,uint256)\":{\"details\":\"Stake an approved amount of staking token into the contract.     User must have already approved the contract for specified amount.\",\"params\":{\"amount\":\"Number of tokens to stake in 1e18\",\"duration\":\"Number of seconds this stake will be held for\"}},\"stakeWithSender(address,uint256,uint256)\":{\"details\":\"Stake an approved amount of staking token into the contract. This function     can only be called by OGN token contract.\",\"params\":{\"amount\":\"Number of tokens to stake in 1e18\",\"duration\":\"Number of seconds this stake will be held for\",\"staker\":\"Address of the account that is creating the stake\"}},\"totalCurrentHoldings(address)\":{\"details\":\"Calculate all current holdings of a user: staked value + prorated rewards\",\"params\":{\"account\":\"Address of the account that we want to look up\"}},\"totalExpectedRewards(address)\":{\"details\":\"Calculate all the rewards a user can expect to receive.\",\"params\":{\"account\":\"Address of the account that we want to look up\"}},\"totalStaked(address)\":{\"details\":\"Calculate all the staked value a user has put into the contract,     rewards not included\",\"params\":{\"account\":\"Address of the account that we want to look up\"}},\"transferGovernance(address)\":{\"details\":\"Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete\",\"params\":{\"_newGovernor\":\"Address of the new Governor\"}}}},\"userdoc\":{\"methods\":{}}},\"settings\":{\"compilationTarget\":{\"contracts/staking/SingleAssetStaking.sol\":\"SingleAssetStaking\"},\"evmVersion\":\"petersburg\",\"libraries\":{},\"metadata\":{\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts/math/SafeMath.sol\":{\"content\":\"pragma solidity ^0.5.0;\\n\\n/**\\n * @dev Wrappers over Solidity's arithmetic operations with added overflow\\n * checks.\\n *\\n * Arithmetic operations in Solidity wrap on overflow. This can easily result\\n * in bugs, because programmers usually assume that an overflow raises an\\n * error, which is the standard behavior in high level programming languages.\\n * `SafeMath` restores this intuition by reverting the transaction when an\\n * operation overflows.\\n *\\n * Using this library instead of the unchecked operations eliminates an entire\\n * class of bugs, so it's recommended to use it always.\\n */\\nlibrary SafeMath {\\n    /**\\n     * @dev Returns the addition of two unsigned integers, reverting on\\n     * overflow.\\n     *\\n     * Counterpart to Solidity's `+` operator.\\n     *\\n     * Requirements:\\n     * - Addition cannot overflow.\\n     */\\n    function add(uint256 a, uint256 b) internal pure returns (uint256) {\\n        uint256 c = a + b;\\n        require(c >= a, \\\"SafeMath: addition overflow\\\");\\n\\n        return c;\\n    }\\n\\n    /**\\n     * @dev Returns the subtraction of two unsigned integers, reverting on\\n     * overflow (when the result is negative).\\n     *\\n     * Counterpart to Solidity's `-` operator.\\n     *\\n     * Requirements:\\n     * - Subtraction cannot overflow.\\n     */\\n    function sub(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return sub(a, b, \\\"SafeMath: subtraction overflow\\\");\\n    }\\n\\n    /**\\n     * @dev Returns the subtraction of two unsigned integers, reverting with custom message on\\n     * overflow (when the result is negative).\\n     *\\n     * Counterpart to Solidity's `-` operator.\\n     *\\n     * Requirements:\\n     * - Subtraction cannot overflow.\\n     *\\n     * _Available since v2.4.0._\\n     */\\n    function sub(uint256 a, uint256 b, string memory errorMessage) internal pure returns (uint256) {\\n        require(b <= a, errorMessage);\\n        uint256 c = a - b;\\n\\n        return c;\\n    }\\n\\n    /**\\n     * @dev Returns the multiplication of two unsigned integers, reverting on\\n     * overflow.\\n     *\\n     * Counterpart to Solidity's `*` operator.\\n     *\\n     * Requirements:\\n     * - Multiplication cannot overflow.\\n     */\\n    function mul(uint256 a, uint256 b) internal pure returns (uint256) {\\n        // Gas optimization: this is cheaper than requiring 'a' not being zero, but the\\n        // benefit is lost if 'b' is also tested.\\n        // See: https://github.com/OpenZeppelin/openzeppelin-contracts/pull/522\\n        if (a == 0) {\\n            return 0;\\n        }\\n\\n        uint256 c = a * b;\\n        require(c / a == b, \\\"SafeMath: multiplication overflow\\\");\\n\\n        return c;\\n    }\\n\\n    /**\\n     * @dev Returns the integer division of two unsigned integers. Reverts on\\n     * division by zero. The result is rounded towards zero.\\n     *\\n     * Counterpart to Solidity's `/` operator. Note: this function uses a\\n     * `revert` opcode (which leaves remaining gas untouched) while Solidity\\n     * uses an invalid opcode to revert (consuming all remaining gas).\\n     *\\n     * Requirements:\\n     * - The divisor cannot be zero.\\n     */\\n    function div(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return div(a, b, \\\"SafeMath: division by zero\\\");\\n    }\\n\\n    /**\\n     * @dev Returns the integer division of two unsigned integers. Reverts with custom message on\\n     * division by zero. The result is rounded towards zero.\\n     *\\n     * Counterpart to Solidity's `/` operator. Note: this function uses a\\n     * `revert` opcode (which leaves remaining gas untouched) while Solidity\\n     * uses an invalid opcode to revert (consuming all remaining gas).\\n     *\\n     * Requirements:\\n     * - The divisor cannot be zero.\\n     *\\n     * _Available since v2.4.0._\\n     */\\n    function div(uint256 a, uint256 b, string memory errorMessage) internal pure returns (uint256) {\\n        // Solidity only automatically asserts when dividing by 0\\n        require(b > 0, errorMessage);\\n        uint256 c = a / b;\\n        // assert(a == b * c + a % b); // There is no case in which this doesn't hold\\n\\n        return c;\\n    }\\n\\n    /**\\n     * @dev Returns the remainder of dividing two unsigned integers. (unsigned integer modulo),\\n     * Reverts when dividing by zero.\\n     *\\n     * Counterpart to Solidity's `%` operator. This function uses a `revert`\\n     * opcode (which leaves remaining gas untouched) while Solidity uses an\\n     * invalid opcode to revert (consuming all remaining gas).\\n     *\\n     * Requirements:\\n     * - The divisor cannot be zero.\\n     */\\n    function mod(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return mod(a, b, \\\"SafeMath: modulo by zero\\\");\\n    }\\n\\n    /**\\n     * @dev Returns the remainder of dividing two unsigned integers. (unsigned integer modulo),\\n     * Reverts with custom message when dividing by zero.\\n     *\\n     * Counterpart to Solidity's `%` operator. This function uses a `revert`\\n     * opcode (which leaves remaining gas untouched) while Solidity uses an\\n     * invalid opcode to revert (consuming all remaining gas).\\n     *\\n     * Requirements:\\n     * - The divisor cannot be zero.\\n     *\\n     * _Available since v2.4.0._\\n     */\\n    function mod(uint256 a, uint256 b, string memory errorMessage) internal pure returns (uint256) {\\n        require(b != 0, errorMessage);\\n        return a % b;\\n    }\\n}\\n\",\"keccak256\":\"0x640b6dee7a4b830bdfd52b5031a07fc2b12209f5b2e29e5d364a7d37f69d8076\"},\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"content\":\"pragma solidity ^0.5.0;\\n\\n/**\\n * @dev Interface of the ERC20 standard as defined in the EIP. Does not include\\n * the optional functions; to access them see {ERC20Detailed}.\\n */\\ninterface IERC20 {\\n    /**\\n     * @dev Returns the amount of tokens in existence.\\n     */\\n    function totalSupply() external view returns (uint256);\\n\\n    /**\\n     * @dev Returns the amount of tokens owned by `account`.\\n     */\\n    function balanceOf(address account) external view returns (uint256);\\n\\n    /**\\n     * @dev Moves `amount` tokens from the caller's account to `recipient`.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transfer(address recipient, uint256 amount) external returns (bool);\\n\\n    /**\\n     * @dev Returns the remaining number of tokens that `spender` will be\\n     * allowed to spend on behalf of `owner` through {transferFrom}. This is\\n     * zero by default.\\n     *\\n     * This value changes when {approve} or {transferFrom} are called.\\n     */\\n    function allowance(address owner, address spender) external view returns (uint256);\\n\\n    /**\\n     * @dev Sets `amount` as the allowance of `spender` over the caller's tokens.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * IMPORTANT: Beware that changing an allowance with this method brings the risk\\n     * that someone may use both the old and the new allowance by unfortunate\\n     * transaction ordering. One possible solution to mitigate this race\\n     * condition is to first reduce the spender's allowance to 0 and set the\\n     * desired value afterwards:\\n     * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\\n     *\\n     * Emits an {Approval} event.\\n     */\\n    function approve(address spender, uint256 amount) external returns (bool);\\n\\n    /**\\n     * @dev Moves `amount` tokens from `sender` to `recipient` using the\\n     * allowance mechanism. `amount` is then deducted from the caller's\\n     * allowance.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transferFrom(address sender, address recipient, uint256 amount) external returns (bool);\\n\\n    /**\\n     * @dev Emitted when `value` tokens are moved from one account (`from`) to\\n     * another (`to`).\\n     *\\n     * Note that `value` may be zero.\\n     */\\n    event Transfer(address indexed from, address indexed to, uint256 value);\\n\\n    /**\\n     * @dev Emitted when the allowance of a `spender` for an `owner` is set by\\n     * a call to {approve}. `value` is the new allowance.\\n     */\\n    event Approval(address indexed owner, address indexed spender, uint256 value);\\n}\\n\",\"keccak256\":\"0xe5bb0f57cff3e299f360052ba50f1ea0fff046df2be070b6943e0e3c3fdad8a9\"},\"@openzeppelin/contracts/token/ERC20/SafeERC20.sol\":{\"content\":\"pragma solidity ^0.5.0;\\n\\nimport \\\"./IERC20.sol\\\";\\nimport \\\"../../math/SafeMath.sol\\\";\\nimport \\\"../../utils/Address.sol\\\";\\n\\n/**\\n * @title SafeERC20\\n * @dev Wrappers around ERC20 operations that throw on failure (when the token\\n * contract returns false). Tokens that return no value (and instead revert or\\n * throw on failure) are also supported, non-reverting calls are assumed to be\\n * successful.\\n * To use this library you can add a `using SafeERC20 for ERC20;` statement to your contract,\\n * which allows you to call the safe operations as `token.safeTransfer(...)`, etc.\\n */\\nlibrary SafeERC20 {\\n    using SafeMath for uint256;\\n    using Address for address;\\n\\n    function safeTransfer(IERC20 token, address to, uint256 value) internal {\\n        callOptionalReturn(token, abi.encodeWithSelector(token.transfer.selector, to, value));\\n    }\\n\\n    function safeTransferFrom(IERC20 token, address from, address to, uint256 value) internal {\\n        callOptionalReturn(token, abi.encodeWithSelector(token.transferFrom.selector, from, to, value));\\n    }\\n\\n    function safeApprove(IERC20 token, address spender, uint256 value) internal {\\n        // safeApprove should only be called when setting an initial allowance,\\n        // or when resetting it to zero. To increase and decrease it, use\\n        // 'safeIncreaseAllowance' and 'safeDecreaseAllowance'\\n        // solhint-disable-next-line max-line-length\\n        require((value == 0) || (token.allowance(address(this), spender) == 0),\\n            \\\"SafeERC20: approve from non-zero to non-zero allowance\\\"\\n        );\\n        callOptionalReturn(token, abi.encodeWithSelector(token.approve.selector, spender, value));\\n    }\\n\\n    function safeIncreaseAllowance(IERC20 token, address spender, uint256 value) internal {\\n        uint256 newAllowance = token.allowance(address(this), spender).add(value);\\n        callOptionalReturn(token, abi.encodeWithSelector(token.approve.selector, spender, newAllowance));\\n    }\\n\\n    function safeDecreaseAllowance(IERC20 token, address spender, uint256 value) internal {\\n        uint256 newAllowance = token.allowance(address(this), spender).sub(value, \\\"SafeERC20: decreased allowance below zero\\\");\\n        callOptionalReturn(token, abi.encodeWithSelector(token.approve.selector, spender, newAllowance));\\n    }\\n\\n    /**\\n     * @dev Imitates a Solidity high-level call (i.e. a regular function call to a contract), relaxing the requirement\\n     * on the return value: the return value is optional (but if data is returned, it must not be false).\\n     * @param token The token targeted by the call.\\n     * @param data The call data (encoded using abi.encode or one of its variants).\\n     */\\n    function callOptionalReturn(IERC20 token, bytes memory data) private {\\n        // We need to perform a low level call here, to bypass Solidity's return data size checking mechanism, since\\n        // we're implementing it ourselves.\\n\\n        // A Solidity high level call has three parts:\\n        //  1. The target address is checked to verify it contains contract code\\n        //  2. The call itself is made, and success asserted\\n        //  3. The return value is decoded, which in turn checks the size of the returned data.\\n        // solhint-disable-next-line max-line-length\\n        require(address(token).isContract(), \\\"SafeERC20: call to non-contract\\\");\\n\\n        // solhint-disable-next-line avoid-low-level-calls\\n        (bool success, bytes memory returndata) = address(token).call(data);\\n        require(success, \\\"SafeERC20: low-level call failed\\\");\\n\\n        if (returndata.length > 0) { // Return data is optional\\n            // solhint-disable-next-line max-line-length\\n            require(abi.decode(returndata, (bool)), \\\"SafeERC20: ERC20 operation did not succeed\\\");\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0x6f2c9955d65c522b80f4b8792f076512d2df947d2112cbc4d98a4781ed42ede2\"},\"@openzeppelin/contracts/utils/Address.sol\":{\"content\":\"pragma solidity ^0.5.5;\\n\\n/**\\n * @dev Collection of functions related to the address type\\n */\\nlibrary Address {\\n    /**\\n     * @dev Returns true if `account` is a contract.\\n     *\\n     * [IMPORTANT]\\n     * ====\\n     * It is unsafe to assume that an address for which this function returns\\n     * false is an externally-owned account (EOA) and not a contract.\\n     *\\n     * Among others, `isContract` will return false for the following \\n     * types of addresses:\\n     *\\n     *  - an externally-owned account\\n     *  - a contract in construction\\n     *  - an address where a contract will be created\\n     *  - an address where a contract lived, but was destroyed\\n     * ====\\n     */\\n    function isContract(address account) internal view returns (bool) {\\n        // According to EIP-1052, 0x0 is the value returned for not-yet created accounts\\n        // and 0xc5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470 is returned\\n        // for accounts without code, i.e. `keccak256('')`\\n        bytes32 codehash;\\n        bytes32 accountHash = 0xc5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470;\\n        // solhint-disable-next-line no-inline-assembly\\n        assembly { codehash := extcodehash(account) }\\n        return (codehash != accountHash && codehash != 0x0);\\n    }\\n\\n    /**\\n     * @dev Converts an `address` into `address payable`. Note that this is\\n     * simply a type cast: the actual underlying value is not changed.\\n     *\\n     * _Available since v2.4.0._\\n     */\\n    function toPayable(address account) internal pure returns (address payable) {\\n        return address(uint160(account));\\n    }\\n\\n    /**\\n     * @dev Replacement for Solidity's `transfer`: sends `amount` wei to\\n     * `recipient`, forwarding all available gas and reverting on errors.\\n     *\\n     * https://eips.ethereum.org/EIPS/eip-1884[EIP1884] increases the gas cost\\n     * of certain opcodes, possibly making contracts go over the 2300 gas limit\\n     * imposed by `transfer`, making them unable to receive funds via\\n     * `transfer`. {sendValue} removes this limitation.\\n     *\\n     * https://diligence.consensys.net/posts/2019/09/stop-using-soliditys-transfer-now/[Learn more].\\n     *\\n     * IMPORTANT: because control is transferred to `recipient`, care must be\\n     * taken to not create reentrancy vulnerabilities. Consider using\\n     * {ReentrancyGuard} or the\\n     * https://solidity.readthedocs.io/en/v0.5.11/security-considerations.html#use-the-checks-effects-interactions-pattern[checks-effects-interactions pattern].\\n     *\\n     * _Available since v2.4.0._\\n     */\\n    function sendValue(address payable recipient, uint256 amount) internal {\\n        require(address(this).balance >= amount, \\\"Address: insufficient balance\\\");\\n\\n        // solhint-disable-next-line avoid-call-value\\n        (bool success, ) = recipient.call.value(amount)(\\\"\\\");\\n        require(success, \\\"Address: unable to send value, recipient may have reverted\\\");\\n    }\\n}\\n\",\"keccak256\":\"0x1a8e5072509c5ea7365eb1d48030b9be865140c8fb779968da0a459a0e174a11\"},\"@openzeppelin/upgrades/contracts/Initializable.sol\":{\"content\":\"pragma solidity >=0.4.24 <0.7.0;\\n\\n\\n/**\\n * @title Initializable\\n *\\n * @dev Helper contract to support initializer functions. To use it, replace\\n * the constructor with a function that has the `initializer` modifier.\\n * WARNING: Unlike constructors, initializer functions must be manually\\n * invoked. This applies both to deploying an Initializable contract, as well\\n * as extending an Initializable contract via inheritance.\\n * WARNING: When used with inheritance, manual care must be taken to not invoke\\n * a parent initializer twice, or ensure that all initializers are idempotent,\\n * because this is not dealt with automatically as with constructors.\\n */\\ncontract Initializable {\\n\\n  /**\\n   * @dev Indicates that the contract has been initialized.\\n   */\\n  bool private initialized;\\n\\n  /**\\n   * @dev Indicates that the contract is in the process of being initialized.\\n   */\\n  bool private initializing;\\n\\n  /**\\n   * @dev Modifier to use in the initializer function of a contract.\\n   */\\n  modifier initializer() {\\n    require(initializing || isConstructor() || !initialized, \\\"Contract instance has already been initialized\\\");\\n\\n    bool isTopLevelCall = !initializing;\\n    if (isTopLevelCall) {\\n      initializing = true;\\n      initialized = true;\\n    }\\n\\n    _;\\n\\n    if (isTopLevelCall) {\\n      initializing = false;\\n    }\\n  }\\n\\n  /// @dev Returns true if and only if the function is running in the constructor\\n  function isConstructor() private view returns (bool) {\\n    // extcodesize checks the size of the code stored in an address, and\\n    // address returns the current address. Since the code is still not\\n    // deployed when running a constructor, any checks on its code size will\\n    // yield zero, making it an effective way to detect if a contract is\\n    // under construction or not.\\n    address self = address(this);\\n    uint256 cs;\\n    assembly { cs := extcodesize(self) }\\n    return cs == 0;\\n  }\\n\\n  // Reserved storage space to allow for layout changes in the future.\\n  uint256[50] private ______gap;\\n}\\n\",\"keccak256\":\"0x9bfec92e36234ecc99b5d37230acb6cd1f99560233753162204104a4897e8721\"},\"contracts/governance/Governable.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\n/**\\n * @title OUSD Governable Contract\\n * @dev Copy of the openzeppelin Ownable.sol contract with nomenclature change\\n *      from owner to governor and renounce methods removed. Does not use\\n *      Context.sol like Ownable.sol does for simplification.\\n * <AUTHOR> Protocol Inc\\n */\\ncontract Governable {\\n    // Storage position of the owner and pendingOwner of the contract\\n    // keccak256(\\\"OUSD.governor\\\");\\n    bytes32\\n        private constant governorPosition = 0x7bea13895fa79d2831e0a9e28edede30099005a50d652d8957cf8a607ee6ca4a;\\n\\n    // keccak256(\\\"OUSD.pending.governor\\\");\\n    bytes32\\n        private constant pendingGovernorPosition = 0x44c4d30b2eaad5130ad70c3ba6972730566f3e6359ab83e800d905c61b1c51db;\\n\\n    // keccak256(\\\"OUSD.reentry.status\\\");\\n    bytes32\\n        private constant reentryStatusPosition = 0x53bf423e48ed90e97d02ab0ebab13b2a235a6bfbe9c321847d5c175333ac4535;\\n\\n    // See OpenZeppelin ReentrancyGuard implementation\\n    uint256 constant _NOT_ENTERED = 1;\\n    uint256 constant _ENTERED = 2;\\n\\n    event PendingGovernorshipTransfer(\\n        address indexed previousGovernor,\\n        address indexed newGovernor\\n    );\\n\\n    event GovernorshipTransferred(\\n        address indexed previousGovernor,\\n        address indexed newGovernor\\n    );\\n\\n    /**\\n     * @dev Initializes the contract setting the deployer as the initial Governor.\\n     */\\n    constructor() internal {\\n        _setGovernor(msg.sender);\\n        emit GovernorshipTransferred(address(0), _governor());\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current Governor.\\n     */\\n    function governor() public view returns (address) {\\n        return _governor();\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current Governor.\\n     */\\n    function _governor() internal view returns (address governorOut) {\\n        bytes32 position = governorPosition;\\n        assembly {\\n            governorOut := sload(position)\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the address of the pending Governor.\\n     */\\n    function _pendingGovernor()\\n        internal\\n        view\\n        returns (address pendingGovernor)\\n    {\\n        bytes32 position = pendingGovernorPosition;\\n        assembly {\\n            pendingGovernor := sload(position)\\n        }\\n    }\\n\\n    /**\\n     * @dev Throws if called by any account other than the Governor.\\n     */\\n    modifier onlyGovernor() {\\n        require(isGovernor(), \\\"Caller is not the Governor\\\");\\n        _;\\n    }\\n\\n    /**\\n     * @dev Returns true if the caller is the current Governor.\\n     */\\n    function isGovernor() public view returns (bool) {\\n        return msg.sender == _governor();\\n    }\\n\\n    function _setGovernor(address newGovernor) internal {\\n        bytes32 position = governorPosition;\\n        assembly {\\n            sstore(position, newGovernor)\\n        }\\n    }\\n\\n    /**\\n     * @dev Prevents a contract from calling itself, directly or indirectly.\\n     * Calling a `nonReentrant` function from another `nonReentrant`\\n     * function is not supported. It is possible to prevent this from happening\\n     * by making the `nonReentrant` function external, and make it call a\\n     * `private` function that does the actual work.\\n     */\\n    modifier nonReentrant() {\\n        bytes32 position = reentryStatusPosition;\\n        uint256 _reentry_status;\\n        assembly {\\n            _reentry_status := sload(position)\\n        }\\n\\n        // On the first call to nonReentrant, _notEntered will be true\\n        require(_reentry_status != _ENTERED, \\\"Reentrant call\\\");\\n\\n        // Any calls to nonReentrant after this point will fail\\n        assembly {\\n            sstore(position, _ENTERED)\\n        }\\n\\n        _;\\n\\n        // By storing the original value once again, a refund is triggered (see\\n        // https://eips.ethereum.org/EIPS/eip-2200)\\n        assembly {\\n            sstore(position, _NOT_ENTERED)\\n        }\\n    }\\n\\n    function _setPendingGovernor(address newGovernor) internal {\\n        bytes32 position = pendingGovernorPosition;\\n        assembly {\\n            sstore(position, newGovernor)\\n        }\\n    }\\n\\n    /**\\n     * @dev Transfers Governance of the contract to a new account (`newGovernor`).\\n     * Can only be called by the current Governor. Must be claimed for this to complete\\n     * @param _newGovernor Address of the new Governor\\n     */\\n    function transferGovernance(address _newGovernor) external onlyGovernor {\\n        _setPendingGovernor(_newGovernor);\\n        emit PendingGovernorshipTransfer(_governor(), _newGovernor);\\n    }\\n\\n    /**\\n     * @dev Claim Governance of the contract to a new account (`newGovernor`).\\n     * Can only be called by the new Governor.\\n     */\\n    function claimGovernance() external {\\n        require(\\n            msg.sender == _pendingGovernor(),\\n            \\\"Only the pending Governor can complete the claim\\\"\\n        );\\n        _changeGovernor(msg.sender);\\n    }\\n\\n    /**\\n     * @dev Change Governance of the contract to a new account (`newGovernor`).\\n     * @param _newGovernor Address of the new Governor\\n     */\\n    function _changeGovernor(address _newGovernor) internal {\\n        require(_newGovernor != address(0), \\\"New Governor is address(0)\\\");\\n        emit GovernorshipTransferred(_governor(), _newGovernor);\\n        _setGovernor(_newGovernor);\\n    }\\n}\\n\",\"keccak256\":\"0x3e51ea48102945bf4b305bf9722a07514a585a29555d92f8c84352d1a4cfcee1\"},\"contracts/staking/SingleAssetStaking.sol\":{\"content\":\"pragma solidity 0.5.11;\\npragma experimental ABIEncoderV2;\\n\\nimport { IERC20 } from \\\"@openzeppelin/contracts/token/ERC20/IERC20.sol\\\";\\nimport { SafeMath } from \\\"@openzeppelin/contracts/math/SafeMath.sol\\\";\\nimport {\\n    Initializable\\n} from \\\"@openzeppelin/upgrades/contracts/Initializable.sol\\\";\\nimport { SafeERC20 } from \\\"@openzeppelin/contracts/token/ERC20/SafeERC20.sol\\\";\\nimport { Governable } from \\\"../governance/Governable.sol\\\";\\nimport { StableMath } from \\\"../utils/StableMath.sol\\\";\\n\\ncontract SingleAssetStaking is Initializable, Governable {\\n    using SafeMath for uint256;\\n    using StableMath for uint256;\\n    using SafeERC20 for IERC20;\\n\\n    /* ========== STATE VARIABLES ========== */\\n\\n    IERC20 public stakingToken; // this is both the staking and rewards\\n\\n    struct Stake {\\n        uint256 amount; // amount to stake\\n        uint256 end; // when does the staking period end\\n        uint256 duration; // the duration of the stake\\n        uint240 rate; // rate to charge use 248 to reserve 8 bits for the bool\\n        bool paid;\\n        uint8 stakeType;\\n    }\\n\\n    struct DropRoot {\\n        bytes32 hash;\\n        uint256 depth;\\n    }\\n\\n    uint256[] public durations; // allowed durations\\n    uint256[] public rates; // rates that correspond with the allowed durations\\n\\n    uint256 public totalOutstanding;\\n    bool public paused;\\n\\n    mapping(address => Stake[]) public userStakes;\\n\\n    mapping(uint8 => DropRoot) public dropRoots;\\n\\n    // type 0 is reserved for stakes done by the user, all other types will be drop/preApproved stakes\\n    uint8 constant USER_STAKE_TYPE = 0;\\n    uint256 constant MAX_STAKES = 256;\\n\\n    /* ========== Initialize ========== */\\n\\n    /**\\n     * @dev Initialize the contracts, sets up durations, rates, and preApprover\\n     *      for preApproved contracts can only be called once\\n     * @param _stakingToken Address of the token that we are staking\\n     * @param _durations Array of allowed durations in seconds\\n     * @param _rates Array of rates(0.3 is 30%) that correspond to the allowed\\n     *               durations in 1e18 precision\\n     */\\n    function initialize(\\n        address _stakingToken,\\n        uint256[] calldata _durations,\\n        uint256[] calldata _rates\\n    ) external onlyGovernor initializer {\\n        stakingToken = IERC20(_stakingToken);\\n        _setDurationRates(_durations, _rates);\\n    }\\n\\n    /* ========= Internal helper functions ======== */\\n\\n    /**\\n     * @dev Validate and set the duration and corresponding rates, will emit\\n     *      events NewRate and NewDurations\\n     */\\n    function _setDurationRates(\\n        uint256[] memory _durations,\\n        uint256[] memory _rates\\n    ) internal {\\n        require(\\n            _rates.length == _durations.length,\\n            \\\"Mismatch durations and rates\\\"\\n        );\\n\\n        for (uint256 i = 0; i < _rates.length; i++) {\\n            require(_rates[i] < uint240(-1), \\\"Max rate exceeded\\\");\\n        }\\n\\n        rates = _rates;\\n        durations = _durations;\\n\\n        emit NewRates(msg.sender, rates);\\n        emit NewDurations(msg.sender, durations);\\n    }\\n\\n    function _totalExpectedRewards(Stake[] storage stakes)\\n        internal\\n        view\\n        returns (uint256 total)\\n    {\\n        for (uint256 i = 0; i < stakes.length; i++) {\\n            Stake storage stake = stakes[i];\\n            if (!stake.paid) {\\n                total = total.add(stake.amount.mulTruncate(stake.rate));\\n            }\\n        }\\n    }\\n\\n    function _totalExpected(Stake storage _stake)\\n        internal\\n        view\\n        returns (uint256)\\n    {\\n        return _stake.amount.add(_stake.amount.mulTruncate(_stake.rate));\\n    }\\n\\n    function _airDroppedStakeClaimed(address account, uint8 stakeType)\\n        internal\\n        view\\n        returns (bool)\\n    {\\n        Stake[] storage stakes = userStakes[account];\\n        for (uint256 i = 0; i < stakes.length; i++) {\\n            if (stakes[i].stakeType == stakeType) {\\n                return true;\\n            }\\n        }\\n        return false;\\n    }\\n\\n    function _findDurationRate(uint256 duration)\\n        internal\\n        view\\n        returns (uint240)\\n    {\\n        for (uint256 i = 0; i < durations.length; i++) {\\n            if (duration == durations[i]) {\\n                return uint240(rates[i]);\\n            }\\n        }\\n        return 0;\\n    }\\n\\n    /**\\n     * @dev Internal staking function\\n     *      will insert the stake into the stakes array and verify we have\\n     *      enough to pay off stake + reward\\n     * @param staker Address of the staker\\n     * @param stakeType Number that represent the type of the stake, 0 is user\\n     *                  initiated all else is currently preApproved\\n     * @param duration Number of seconds this stake will be held for\\n     * @param rate Rate(0.3 is 30%) of reward for this stake in 1e18, uint240 =\\n     *             to fit the bool and type in struct Stake\\n     * @param amount Number of tokens to stake in 1e18\\n     */\\n    function _stake(\\n        address staker,\\n        uint8 stakeType,\\n        uint256 duration,\\n        uint240 rate,\\n        uint256 amount\\n    ) internal {\\n        require(!paused, \\\"Staking paused\\\");\\n\\n        Stake[] storage stakes = userStakes[staker];\\n\\n        uint256 end = block.timestamp.add(duration);\\n\\n        uint256 i = stakes.length; // start at the end of the current array\\n\\n        require(i < MAX_STAKES, \\\"Max stakes\\\");\\n\\n        stakes.length += 1; // grow the array\\n        // find the spot where we can insert the current stake\\n        // this should make an increasing list sorted by end\\n        while (i != 0 && stakes[i - 1].end > end) {\\n            // shift it back one\\n            stakes[i] = stakes[i - 1];\\n            i -= 1;\\n        }\\n\\n        // insert the stake\\n        Stake storage newStake = stakes[i];\\n        newStake.rate = rate;\\n        newStake.stakeType = stakeType;\\n        newStake.end = end;\\n        newStake.duration = duration;\\n        newStake.amount = amount;\\n\\n        totalOutstanding = totalOutstanding.add(_totalExpected(newStake));\\n\\n        emit Staked(staker, amount, duration, rate);\\n    }\\n\\n    function _stakeWithChecks(\\n        address staker,\\n        uint256 amount,\\n        uint256 duration\\n    ) internal {\\n        require(amount > 0, \\\"Cannot stake 0\\\");\\n\\n        uint240 rewardRate = _findDurationRate(duration);\\n        require(rewardRate > 0, \\\"Invalid duration\\\"); // we couldn't find the rate that correspond to the passed duration\\n\\n        _stake(staker, USER_STAKE_TYPE, duration, rewardRate, amount);\\n        // transfer in the token so that we can stake the correct amount\\n        stakingToken.safeTransferFrom(staker, address(this), amount);\\n    }\\n\\n    modifier requireLiquidity() {\\n        // we need to have enough balance to cover the rewards after the operation is complete\\n        _;\\n        require(\\n            stakingToken.balanceOf(address(this)) >= totalOutstanding,\\n            \\\"Insufficient rewards\\\"\\n        );\\n    }\\n\\n    /* ========== VIEWS ========== */\\n\\n    function getAllDurations() external view returns (uint256[] memory) {\\n        return durations;\\n    }\\n\\n    function getAllRates() external view returns (uint256[] memory) {\\n        return rates;\\n    }\\n\\n    /**\\n     * @dev Return all the stakes paid and unpaid for a given user\\n     * @param account Address of the account that we want to look up\\n     */\\n    function getAllStakes(address account)\\n        external\\n        view\\n        returns (Stake[] memory)\\n    {\\n        return userStakes[account];\\n    }\\n\\n    /**\\n     * @dev Find the rate that corresponds to a given duration\\n     * @param _duration Number of seconds\\n     */\\n    function durationRewardRate(uint256 _duration)\\n        external\\n        view\\n        returns (uint256)\\n    {\\n        return _findDurationRate(_duration);\\n    }\\n\\n    /**\\n     * @dev Has the airdropped stake already been claimed\\n     */\\n    function airDroppedStakeClaimed(address account, uint8 stakeType)\\n        external\\n        view\\n        returns (bool)\\n    {\\n        return _airDroppedStakeClaimed(account, stakeType);\\n    }\\n\\n    /**\\n     * @dev Calculate all the staked value a user has put into the contract,\\n     *      rewards not included\\n     * @param account Address of the account that we want to look up\\n     */\\n    function totalStaked(address account)\\n        external\\n        view\\n        returns (uint256 total)\\n    {\\n        Stake[] storage stakes = userStakes[account];\\n\\n        for (uint256 i = 0; i < stakes.length; i++) {\\n            if (!stakes[i].paid) {\\n                total = total.add(stakes[i].amount);\\n            }\\n        }\\n    }\\n\\n    /**\\n     * @dev Calculate all the rewards a user can expect to receive.\\n     * @param account Address of the account that we want to look up\\n     */\\n    function totalExpectedRewards(address account)\\n        external\\n        view\\n        returns (uint256)\\n    {\\n        return _totalExpectedRewards(userStakes[account]);\\n    }\\n\\n    /**\\n     * @dev Calculate all current holdings of a user: staked value + prorated rewards\\n     * @param account Address of the account that we want to look up\\n     */\\n    function totalCurrentHoldings(address account)\\n        external\\n        view\\n        returns (uint256 total)\\n    {\\n        Stake[] storage stakes = userStakes[account];\\n\\n        for (uint256 i = 0; i < stakes.length; i++) {\\n            Stake storage stake = stakes[i];\\n            if (stake.paid) {\\n                continue;\\n            } else if (stake.end < block.timestamp) {\\n                total = total.add(_totalExpected(stake));\\n            } else {\\n                //calcualte the precentage accrued in term of rewards\\n                total = total.add(\\n                    stake.amount.add(\\n                        stake.amount.mulTruncate(stake.rate).mulTruncate(\\n                            stake\\n                                .duration\\n                                .sub(stake.end.sub(block.timestamp))\\n                                .divPrecisely(stake.duration)\\n                        )\\n                    )\\n                );\\n            }\\n        }\\n    }\\n\\n    /* ========== MUTATIVE FUNCTIONS ========== */\\n\\n    /**\\n     * @dev Make a preapproved stake for the user, this is a presigned voucher that the user can redeem either from\\n     *      an airdrop or a compensation program.\\n     *      Only 1 of each type is allowed per user. The proof must match the root hash\\n     * @param index Number that is zero base index of the stake in the payout entry\\n     * @param stakeType Number that represent the type of the stake, must not be 0 which is user stake\\n     * @param duration Number of seconds this stake will be held for\\n     * @param rate Rate(0.3 is 30%) of reward for this stake in 1e18, uint240 to fit the bool and type in struct Stake\\n     * @param amount Number of tokens to stake in 1e18\\n     * @param merkleProof Array of proofs for that amount\\n     */\\n    function airDroppedStake(\\n        uint256 index,\\n        uint8 stakeType,\\n        uint256 duration,\\n        uint256 rate,\\n        uint256 amount,\\n        bytes32[] calldata merkleProof\\n    ) external requireLiquidity {\\n        require(stakeType != USER_STAKE_TYPE, \\\"Cannot be normal staking\\\");\\n        require(rate < uint240(-1), \\\"Max rate exceeded\\\");\\n        require(index < 2**merkleProof.length, \\\"Invalid index\\\");\\n        DropRoot storage dropRoot = dropRoots[stakeType];\\n        require(merkleProof.length == dropRoot.depth, \\\"Invalid proof\\\");\\n\\n        // Compute the merkle root\\n        bytes32 node = keccak256(\\n            abi.encodePacked(\\n                index,\\n                stakeType,\\n                address(this),\\n                msg.sender,\\n                duration,\\n                rate,\\n                amount\\n            )\\n        );\\n        uint256 path = index;\\n        for (uint16 i = 0; i < merkleProof.length; i++) {\\n            if ((path & 0x01) == 1) {\\n                node = keccak256(abi.encodePacked(merkleProof[i], node));\\n            } else {\\n                node = keccak256(abi.encodePacked(node, merkleProof[i]));\\n            }\\n            path /= 2;\\n        }\\n\\n        // Check the merkle proof\\n        require(node == dropRoot.hash, \\\"Stake not approved\\\");\\n\\n        // verify that we haven't already staked\\n        require(\\n            !_airDroppedStakeClaimed(msg.sender, stakeType),\\n            \\\"Already staked\\\"\\n        );\\n\\n        _stake(msg.sender, stakeType, duration, uint240(rate), amount);\\n    }\\n\\n    /**\\n     * @dev Stake an approved amount of staking token into the contract.\\n     *      User must have already approved the contract for specified amount.\\n     * @param amount Number of tokens to stake in 1e18\\n     * @param duration Number of seconds this stake will be held for\\n     */\\n    function stake(uint256 amount, uint256 duration) external requireLiquidity {\\n        // no checks are performed in this function since those are already present in _stakeWithChecks\\n        _stakeWithChecks(msg.sender, amount, duration);\\n    }\\n\\n    /**\\n     * @dev Stake an approved amount of staking token into the contract. This function\\n     *      can only be called by OGN token contract.\\n     * @param staker Address of the account that is creating the stake\\n     * @param amount Number of tokens to stake in 1e18\\n     * @param duration Number of seconds this stake will be held for\\n     */\\n    function stakeWithSender(\\n        address staker,\\n        uint256 amount,\\n        uint256 duration\\n    ) external returns (bool) {\\n        require(\\n            msg.sender == address(stakingToken),\\n            \\\"Only token contract can make this call\\\"\\n        );\\n\\n        _stakeWithChecks(staker, amount, duration);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev Exit out of all possible stakes\\n     */\\n    function exit() external requireLiquidity {\\n        Stake[] storage stakes = userStakes[msg.sender];\\n        require(stakes.length > 0, \\\"Nothing staked\\\");\\n\\n        uint256 totalWithdraw = 0;\\n        uint256 stakedAmount = 0;\\n        uint256 l = stakes.length;\\n        do {\\n            Stake storage exitStake = stakes[l - 1];\\n            // stop on the first ended stake that's already been paid\\n            if (exitStake.end < block.timestamp && exitStake.paid) {\\n                break;\\n            }\\n            //might not be ended\\n            if (exitStake.end < block.timestamp) {\\n                //we are paying out the stake\\n                exitStake.paid = true;\\n                totalWithdraw = totalWithdraw.add(_totalExpected(exitStake));\\n                stakedAmount = stakedAmount.add(exitStake.amount);\\n            }\\n            l--;\\n        } while (l > 0);\\n        require(totalWithdraw > 0, \\\"All stakes in lock-up\\\");\\n\\n        totalOutstanding = totalOutstanding.sub(totalWithdraw);\\n        emit Withdrawn(msg.sender, totalWithdraw, stakedAmount);\\n        stakingToken.safeTransfer(msg.sender, totalWithdraw);\\n    }\\n\\n    /* ========== MODIFIERS ========== */\\n\\n    function setPaused(bool _paused) external onlyGovernor {\\n        paused = _paused;\\n        emit Paused(msg.sender, paused);\\n    }\\n\\n    /**\\n     * @dev Set new durations and rates will not effect existing stakes\\n     * @param _durations Array of durations in seconds\\n     * @param _rates Array of rates that corresponds to the durations (0.01 is 1%) in 1e18\\n     */\\n    function setDurationRates(\\n        uint256[] calldata _durations,\\n        uint256[] calldata _rates\\n    ) external onlyGovernor {\\n        _setDurationRates(_durations, _rates);\\n    }\\n\\n    /**\\n     * @dev Set air drop root for a specific stake type\\n     * @param _stakeType Type of staking must be greater than 0\\n     * @param _rootHash Root hash of the Merkle Tree\\n     * @param _proofDepth Depth of the Merklke Tree\\n     */\\n    function setAirDropRoot(\\n        uint8 _stakeType,\\n        bytes32 _rootHash,\\n        uint256 _proofDepth\\n    ) external onlyGovernor {\\n        require(_stakeType != USER_STAKE_TYPE, \\\"Cannot be normal staking\\\");\\n        dropRoots[_stakeType].hash = _rootHash;\\n        dropRoots[_stakeType].depth = _proofDepth;\\n        emit NewAirDropRootHash(_stakeType, _rootHash, _proofDepth);\\n    }\\n\\n    /* ========== EVENTS ========== */\\n\\n    event Staked(\\n        address indexed user,\\n        uint256 amount,\\n        uint256 duration,\\n        uint256 rate\\n    );\\n    event Withdrawn(address indexed user, uint256 amount, uint256 stakedAmount);\\n    event Paused(address indexed user, bool yes);\\n    event NewDurations(address indexed user, uint256[] durations);\\n    event NewRates(address indexed user, uint256[] rates);\\n    event NewAirDropRootHash(\\n        uint8 stakeType,\\n        bytes32 rootHash,\\n        uint256 proofDepth\\n    );\\n}\\n\",\"keccak256\":\"0x397ead290428160d0e34a9d74a54d68498d8ad12d8329c3b576b0fcacae17b2e\"},\"contracts/utils/StableMath.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\nimport { SafeMath } from \\\"@openzeppelin/contracts/math/SafeMath.sol\\\";\\n\\n// Based on StableMath from Stability Labs Pty. Ltd.\\n// https://github.com/mstable/mStable-contracts/blob/master/contracts/shared/StableMath.sol\\n\\nlibrary StableMath {\\n    using SafeMath for uint256;\\n\\n    /**\\n     * @dev Scaling unit for use in specific calculations,\\n     * where 1 * 10**18, or 1e18 represents a unit '1'\\n     */\\n    uint256 private constant FULL_SCALE = 1e18;\\n\\n    /***************************************\\n                    Helpers\\n    ****************************************/\\n\\n    /**\\n     * @dev Adjust the scale of an integer\\n     * @param adjustment Amount to adjust by e.g. scaleBy(1e18, -1) == 1e17\\n     */\\n    function scaleBy(uint256 x, int8 adjustment)\\n        internal\\n        pure\\n        returns (uint256)\\n    {\\n        if (adjustment > 0) {\\n            x = x.mul(10**uint256(adjustment));\\n        } else if (adjustment < 0) {\\n            x = x.div(10**uint256(adjustment * -1));\\n        }\\n        return x;\\n    }\\n\\n    /***************************************\\n               Precise Arithmetic\\n    ****************************************/\\n\\n    /**\\n     * @dev Multiplies two precise units, and then truncates by the full scale\\n     * @param x Left hand input to multiplication\\n     * @param y Right hand input to multiplication\\n     * @return Result after multiplying the two inputs and then dividing by the shared\\n     *         scale unit\\n     */\\n    function mulTruncate(uint256 x, uint256 y) internal pure returns (uint256) {\\n        return mulTruncateScale(x, y, FULL_SCALE);\\n    }\\n\\n    /**\\n     * @dev Multiplies two precise units, and then truncates by the given scale. For example,\\n     * when calculating 90% of 10e18, (10e18 * 9e17) / 1e18 = (9e36) / 1e18 = 9e18\\n     * @param x Left hand input to multiplication\\n     * @param y Right hand input to multiplication\\n     * @param scale Scale unit\\n     * @return Result after multiplying the two inputs and then dividing by the shared\\n     *         scale unit\\n     */\\n    function mulTruncateScale(\\n        uint256 x,\\n        uint256 y,\\n        uint256 scale\\n    ) internal pure returns (uint256) {\\n        // e.g. assume scale = fullScale\\n        // z = 10e18 * 9e17 = 9e36\\n        uint256 z = x.mul(y);\\n        // return 9e38 / 1e18 = 9e18\\n        return z.div(scale);\\n    }\\n\\n    /**\\n     * @dev Multiplies two precise units, and then truncates by the full scale, rounding up the result\\n     * @param x Left hand input to multiplication\\n     * @param y Right hand input to multiplication\\n     * @return Result after multiplying the two inputs and then dividing by the shared\\n     *          scale unit, rounded up to the closest base unit.\\n     */\\n    function mulTruncateCeil(uint256 x, uint256 y)\\n        internal\\n        pure\\n        returns (uint256)\\n    {\\n        // e.g. 8e17 * 17268172638 = 138145381104e17\\n        uint256 scaled = x.mul(y);\\n        // e.g. 138145381104e17 + 9.99...e17 = 138145381113.99...e17\\n        uint256 ceil = scaled.add(FULL_SCALE.sub(1));\\n        // e.g. 13814538111.399...e18 / 1e18 = 13814538111\\n        return ceil.div(FULL_SCALE);\\n    }\\n\\n    /**\\n     * @dev Precisely divides two units, by first scaling the left hand operand. Useful\\n     *      for finding percentage weightings, i.e. 8e18/10e18 = 80% (or 8e17)\\n     * @param x Left hand input to division\\n     * @param y Right hand input to division\\n     * @return Result after multiplying the left operand by the scale, and\\n     *         executing the division on the right hand input.\\n     */\\n    function divPrecisely(uint256 x, uint256 y)\\n        internal\\n        pure\\n        returns (uint256)\\n    {\\n        // e.g. 8e18 * 1e18 = 8e36\\n        uint256 z = x.mul(FULL_SCALE);\\n        // e.g. 8e36 / 10e18 = 8e17\\n        return z.div(y);\\n    }\\n}\\n\",\"keccak256\":\"0xa77fccf850feb6d54ba3a6530f92554caef8a67a1ceb573d4f8a5d1bf64ff9d2\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"methods": {"airDroppedStake(uint256,uint8,uint256,uint256,uint256,bytes32[])": {"details": "Make a preapproved stake for the user, this is a presigned voucher that the user can redeem either from     an airdrop or a compensation program.     Only 1 of each type is allowed per user. The proof must match the root hash", "params": {"amount": "Number of tokens to stake in 1e18", "duration": "Number of seconds this stake will be held for", "index": "Number that is zero base index of the stake in the payout entry", "merkleProof": "Array of proofs for that amount", "rate": "Rate(0.3 is 30%) of reward for this stake in 1e18, uint240 to fit the bool and type in struct Stake", "stakeType": "Number that represent the type of the stake, must not be 0 which is user stake"}}, "airDroppedStakeClaimed(address,uint8)": {"details": "Has the airdropped stake already been claimed"}, "claimGovernance()": {"details": "Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor."}, "durationRewardRate(uint256)": {"details": "Find the rate that corresponds to a given duration", "params": {"_duration": "Number of seconds"}}, "exit()": {"details": "Exit out of all possible stakes"}, "getAllStakes(address)": {"details": "Return all the stakes paid and unpaid for a given user", "params": {"account": "Address of the account that we want to look up"}}, "governor()": {"details": "Returns the address of the current Governor."}, "initialize(address,uint256[],uint256[])": {"details": "Initialize the contracts, sets up durations, rates, and preApprover     for preApproved contracts can only be called once", "params": {"_durations": "Array of allowed durations in seconds", "_rates": "Array of rates(0.3 is 30%) that correspond to the allowed              durations in 1e18 precision", "_stakingToken": "Address of the token that we are staking"}}, "isGovernor()": {"details": "Returns true if the caller is the current Governor."}, "setAirDropRoot(uint8,bytes32,uint256)": {"details": "Set air drop root for a specific stake type", "params": {"_proofDepth": "Depth of the Merklke Tree", "_rootHash": "Root hash of the Merkle Tree", "_stakeType": "Type of staking must be greater than 0"}}, "setDurationRates(uint256[],uint256[])": {"details": "Set new durations and rates will not effect existing stakes", "params": {"_durations": "Array of durations in seconds", "_rates": "Array of rates that corresponds to the durations (0.01 is 1%) in 1e18"}}, "stake(uint256,uint256)": {"details": "Stake an approved amount of staking token into the contract.     User must have already approved the contract for specified amount.", "params": {"amount": "Number of tokens to stake in 1e18", "duration": "Number of seconds this stake will be held for"}}, "stakeWithSender(address,uint256,uint256)": {"details": "Stake an approved amount of staking token into the contract. This function     can only be called by OGN token contract.", "params": {"amount": "Number of tokens to stake in 1e18", "duration": "Number of seconds this stake will be held for", "staker": "Address of the account that is creating the stake"}}, "totalCurrentHoldings(address)": {"details": "Calculate all current holdings of a user: staked value + prorated rewards", "params": {"account": "Address of the account that we want to look up"}}, "totalExpectedRewards(address)": {"details": "Calculate all the rewards a user can expect to receive.", "params": {"account": "Address of the account that we want to look up"}}, "totalStaked(address)": {"details": "Calculate all the staked value a user has put into the contract,     rewards not included", "params": {"account": "Address of the account that we want to look up"}}, "transferGovernance(address)": {"details": "Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete", "params": {"_newGovernor": "Address of the new Governor"}}}}, "userdoc": {"methods": {}}}