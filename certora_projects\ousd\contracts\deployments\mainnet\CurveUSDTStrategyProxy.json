{"abi": [{"constant": true, "inputs": [], "name": "governor", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}], "name": "upgradeTo", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "payable": true, "stateMutability": "payable", "type": "function"}, {"constant": true, "inputs": [], "name": "implementation", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "claimGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "isGovernor", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_logic", "type": "address"}, {"internalType": "address", "name": "_initGovernor", "type": "address"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "name": "initialize", "outputs": [], "payable": true, "stateMutability": "payable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_newGovernor", "type": "address"}], "name": "transferGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "admin", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"payable": true, "stateMutability": "payable", "type": "fallback"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "PendingGovernorshipTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "GovernorshipTransferred", "type": "event"}], "receipt": {"to": null, "from": "0xAed9fDc9681D61edB5F8B8E421f5cEe8D7F4B04f", "contractAddress": "0xe40e09cD6725E542001FcB900d9dfeA447B529C0", "transactionIndex": 114, "gasUsed": "815075", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000004000001000000048000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000800000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000000000010000800000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000001", "blockHash": "0xf1019c7c54a01b87008ec8bad67923062e2b377a513ba0e23ee89e20bbb5daa5", "transactionHash": "0x4644524dc13c6ca864908ba560d0f552097f70430af70f31e5875a11bbce2f5f", "logs": [{"transactionIndex": 114, "blockNumber": 11056480, "transactionHash": "0x4644524dc13c6ca864908ba560d0f552097f70430af70f31e5875a11bbce2f5f", "address": "0xe40e09cD6725E542001FcB900d9dfeA447B529C0", "topics": ["0xc7c0c772add429241571afb3805861fb3cfa2af374534088b76cdb4325a87e9a", "0x0000000000000000000000000000000000000000000000000000000000000000", "0x000000000000000000000000aed9fdc9681d61edb5f8b8e421f5cee8d7f4b04f"], "data": "0x", "logIndex": 219, "blockHash": "0xf1019c7c54a01b87008ec8bad67923062e2b377a513ba0e23ee89e20bbb5daa5"}], "blockNumber": 11056480, "cumulativeGasUsed": "9445691", "status": 1, "byzantium": true}, "address": "0xe40e09cD6725E542001FcB900d9dfeA447B529C0", "args": [], "solcInputHash": "0xfed873d87c2289f48fdb814c684c6291df98b6ba6fb3b9a0fc638f52843fda9e", "metadata": "{\"compiler\":{\"version\":\"0.5.11+commit.c082d0b4\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"constant\":true,\"inputs\":[],\"name\":\"governor\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"}],\"name\":\"upgradeTo\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"upgradeToAndCall\",\"outputs\":[],\"payable\":true,\"stateMutability\":\"payable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"implementation\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"claimGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"isGovernor\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_logic\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_initGovernor\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"_data\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"payable\":true,\"stateMutability\":\"payable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_newGovernor\",\"type\":\"address\"}],\"name\":\"transferGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"admin\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"payable\":true,\"stateMutability\":\"payable\",\"type\":\"fallback\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"Upgraded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"PendingGovernorshipTransfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"GovernorshipTransferred\",\"type\":\"event\"}],\"devdoc\":{\"methods\":{\"admin()\":{\"return\":\"The address of the proxy admin/it's also the governor.\"},\"claimGovernance()\":{\"details\":\"Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor.\"},\"governor()\":{\"details\":\"Returns the address of the current Governor.\"},\"implementation()\":{\"return\":\"The address of the implementation.\"},\"initialize(address,address,bytes)\":{\"details\":\"Contract initializer with Governor enforcement\",\"params\":{\"_data\":\"Data to send as msg.data to the implementation to initialize the proxied contract. It should include the signature and the parameters of the function to be called, as described in https://solidity.readthedocs.io/en/v0.4.24/abi-spec.html#function-selector-and-argument-encoding. This parameter is optional, if no data is given the initialization call to proxied contract will be skipped.\",\"_initGovernor\":\"Address of the initial Governor.\",\"_logic\":\"Address of the initial implementation.\"}},\"isGovernor()\":{\"details\":\"Returns true if the caller is the current Governor.\"},\"transferGovernance(address)\":{\"details\":\"Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete\",\"params\":{\"_newGovernor\":\"Address of the new Governor\"}},\"upgradeTo(address)\":{\"details\":\"Upgrade the backing implementation of the proxy. Only the admin can call this function.\",\"params\":{\"newImplementation\":\"Address of the new implementation.\"}},\"upgradeToAndCall(address,bytes)\":{\"details\":\"Upgrade the backing implementation of the proxy and call a function on the new implementation. This is useful to initialize the proxied contract.\",\"params\":{\"data\":\"Data to send as msg.data in the low level call. It should include the signature and the parameters of the function to be called, as described in https://solidity.readthedocs.io/en/v0.4.24/abi-spec.html#function-selector-and-argument-encoding.\",\"newImplementation\":\"Address of the new implementation.\"}}}},\"userdoc\":{\"methods\":{},\"notice\":\"ThreePoolStrategyProxy delegates calls to a ThreePoolStrategy implementation\"}},\"settings\":{\"compilationTarget\":{\"contracts/proxies/Proxies.sol\":\"ThreePoolStrategyProxy\"},\"evmVersion\":\"petersburg\",\"libraries\":{},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/upgrades/contracts/upgradeability/BaseUpgradeabilityProxy.sol\":{\"keccak256\":\"0x1b37f808dc0a45976ad23aa53908c485a74a4d9fbd03066a5ab399c46436e570\",\"urls\":[\"bzz-raw://de04959e1261bbc2ee53a88b98469a888a2f2fa49c07ea87a5d646a8fe438558\",\"dweb:/ipfs/QmUmYQaNnAMfvbfKLniFLHyBzsH77bCMf9NpNSfndfuqWh\"]},\"@openzeppelin/upgrades/contracts/upgradeability/Proxy.sol\":{\"keccak256\":\"0xd8074ae5fa7ee6384ca8196a896612fb044bbf4ff4b7336d03cd97845000ac21\",\"urls\":[\"bzz-raw://3a111e1dc777d80e6c2ad41c6ff2bfb3ae8f61658fd6580ddf9ba9ab0d25a354\",\"dweb:/ipfs/QmSfwvUqqiownzHeMmUiYkejWs6FXR2KLtHQnSzP6FivTj\"]},\"@openzeppelin/upgrades/contracts/utils/Address.sol\":{\"keccak256\":\"0x7be7f8e4c08bc70ff9815e3f11c569f42aa3c447b5bc61fd75fb5cec97f63a9f\",\"urls\":[\"bzz-raw://f301a9226d637e1593916ba2de7b0b28c35f2adc3ca8526289fe1eedafa83bc2\",\"dweb:/ipfs/QmPb991LE8nyvhB4BkX46P1Pan2ijH9tfqWwLAz65VUUKs\"]},\"contracts/governance/Governable.sol\":{\"keccak256\":\"0x342fa1b2e2cbe8d2d904c31e5a2b182446d3737db2f6704d3f247c6c733084ec\",\"urls\":[\"bzz-raw://93918076cf5ef49658f8dd78ac7aafe30a1e233cc49e70a642ee9559b78c3f28\",\"dweb:/ipfs/Qmcca1ncSQnxSRXs5FZHzH9dwfZ1KcuUfcAiBwgkbzfTeW\"]},\"contracts/proxies/InitializeGovernedUpgradeabilityProxy.sol\":{\"keccak256\":\"0x7f538dc61626d9a3e02305e095d428c3da5c146feff48fa021359ae2e142df22\",\"urls\":[\"bzz-raw://6db4cc183aadaeb1628b9091323f3b3c03ff7b6bd3bfc67d0d9ea22bb9f98e1e\",\"dweb:/ipfs/QmZw6b24txWTDk381uCZSo6fS2LuYJUBrR9T193HGrFGGr\"]},\"contracts/proxies/Proxies.sol\":{\"keccak256\":\"0x0d5ad1879fe66484753a8bdb80b1ca0c4c4cbbec3500d26560869268a7453833\",\"urls\":[\"bzz-raw://9452e9bd27056c4047617bdb93bcf1bcfa6de723c1662dc21ec5ca60c8ff7ab2\",\"dweb:/ipfs/QmYFHjbaRcWxTmTTNF2uZqmo7WedUv9j9m5L5EEoiuPNbZ\"]}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"methods": {"admin()": {"return": "The address of the proxy admin/it's also the governor."}, "claimGovernance()": {"details": "Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor."}, "governor()": {"details": "Returns the address of the current Governor."}, "implementation()": {"return": "The address of the implementation."}, "initialize(address,address,bytes)": {"details": "Contract initializer with Governor enforcement", "params": {"_data": "Data to send as msg.data to the implementation to initialize the proxied contract. It should include the signature and the parameters of the function to be called, as described in https://solidity.readthedocs.io/en/v0.4.24/abi-spec.html#function-selector-and-argument-encoding. This parameter is optional, if no data is given the initialization call to proxied contract will be skipped.", "_initGovernor": "Address of the initial Governor.", "_logic": "Address of the initial implementation."}}, "isGovernor()": {"details": "Returns true if the caller is the current Governor."}, "transferGovernance(address)": {"details": "Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete", "params": {"_newGovernor": "Address of the new Governor"}}, "upgradeTo(address)": {"details": "Upgrade the backing implementation of the proxy. Only the admin can call this function.", "params": {"newImplementation": "Address of the new implementation."}}, "upgradeToAndCall(address,bytes)": {"details": "Upgrade the backing implementation of the proxy and call a function on the new implementation. This is useful to initialize the proxied contract.", "params": {"data": "Data to send as msg.data in the low level call. It should include the signature and the parameters of the function to be called, as described in https://solidity.readthedocs.io/en/v0.4.24/abi-spec.html#function-selector-and-argument-encoding.", "newImplementation": "Address of the new implementation."}}}}, "userdoc": {"methods": {}, "notice": "ThreePoolStrategyProxy delegates calls to a ThreePoolStrategy implementation"}, "gasEstimates": {"creation": {"codeDepositCost": "684400", "executionCost": "infinite", "totalCost": "infinite"}, "external": {"": "infinite", "admin()": "567", "claimGovernance()": "infinite", "governor()": "480", "implementation()": "546", "initialize(address,address,bytes)": "infinite", "isGovernor()": "518", "transferGovernance(address)": "infinite", "upgradeTo(address)": "infinite", "upgradeToAndCall(address,bytes)": "infinite"}}}