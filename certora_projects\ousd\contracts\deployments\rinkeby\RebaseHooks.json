{"abi": [{"constant": true, "inputs": [], "name": "governor", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "bool", "name": "sync", "type": "bool"}], "name": "postRebase", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "franck<PERSON>ack", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "claimGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "uniswapPairs", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address[]", "name": "_uniswapPairs", "type": "address[]"}], "name": "setUniswapPairs", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "isGovernor", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_newGovernor", "type": "address"}], "name": "transferGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "PendingGovernorshipTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "GovernorshipTransferred", "type": "event"}], "receipt": {"to": null, "from": "0xD85A569F3C26f81070544451131c742283360400", "contractAddress": "0x49c29feb858eae45FeE9866Ec0Dd5f0326E998C5", "transactionIndex": 5, "gasUsed": "571661", "logsBloom": "0x00000000400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000020000000000000000000800000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000020000000000000000000000000000010000000000000000000000000000000000000000020000000000000000000000000000000000000000400002000000000000000000000", "blockHash": "0xbd6a12512ee68e99c927f255530e959ee178aef93ccdaa5c80188707c8631ff3", "transactionHash": "0xd77cb46fa5d2705b6a4917afc5c65a7ce0a289595ff3e2b21842b424be9dd9b5", "logs": [{"transactionIndex": 5, "blockNumber": 7285601, "transactionHash": "0xd77cb46fa5d2705b6a4917afc5c65a7ce0a289595ff3e2b21842b424be9dd9b5", "address": "0x49c29feb858eae45FeE9866Ec0Dd5f0326E998C5", "topics": ["0xc7c0c772add429241571afb3805861fb3cfa2af374534088b76cdb4325a87e9a", "0x0000000000000000000000000000000000000000000000000000000000000000", "0x000000000000000000000000d85a569f3c26f81070544451131c742283360400"], "data": "0x", "logIndex": 154, "blockHash": "0xbd6a12512ee68e99c927f255530e959ee178aef93ccdaa5c80188707c8631ff3"}], "blockNumber": 7285601, "cumulativeGasUsed": "6244423", "status": 1, "byzantium": true}, "address": "0x49c29feb858eae45FeE9866Ec0Dd5f0326E998C5", "args": [], "solcInputHash": "0xd005e0c5eace540df3e1c72c7e488e1737e10feda5e3ea122bf409e2adc14e8f", "metadata": "{\"compiler\":{\"version\":\"0.5.11+commit.c082d0b4\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"constant\":true,\"inputs\":[],\"name\":\"governor\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"bool\",\"name\":\"sync\",\"type\":\"bool\"}],\"name\":\"postRebase\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"franckHack\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"claimGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"uniswapPairs\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"_uniswapPairs\",\"type\":\"address[]\"}],\"name\":\"setUniswapPairs\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"isGovernor\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_newGovernor\",\"type\":\"address\"}],\"name\":\"transferGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"PendingGovernorshipTransfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"GovernorshipTransferred\",\"type\":\"event\"}],\"devdoc\":{\"methods\":{\"claimGovernance()\":{\"details\":\"Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor.\"},\"governor()\":{\"details\":\"Returns the address of the current Governor.\"},\"isGovernor()\":{\"details\":\"Returns true if the caller is the current Governor.\"},\"transferGovernance(address)\":{\"details\":\"Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete\",\"params\":{\"_newGovernor\":\"Address of the new Governor\"}}}},\"userdoc\":{\"methods\":{}}},\"settings\":{\"compilationTarget\":{\"contracts/utils/RebaseHooks.sol\":\"RebaseHooks\"},\"evmVersion\":\"petersburg\",\"libraries\":{},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"contracts/governance/Governable.sol\":{\"keccak256\":\"0x342fa1b2e2cbe8d2d904c31e5a2b182446d3737db2f6704d3f247c6c733084ec\",\"urls\":[\"bzz-raw://93918076cf5ef49658f8dd78ac7aafe30a1e233cc49e70a642ee9559b78c3f28\",\"dweb:/ipfs/Qmcca1ncSQnxSRXs5FZHzH9dwfZ1KcuUfcAiBwgkbzfTeW\"]},\"contracts/interfaces/uniswap/IUniswapV2Pair.sol\":{\"keccak256\":\"0xf722b9b3a04647d5a596b92dbc9aa6208aa999a51a4380197d2762be9591a436\",\"urls\":[\"bzz-raw://127a6bfbefbbc07ce0e3c72c02280fb097c062f4ca33a2ec97edb8cc916e36c7\",\"dweb:/ipfs/QmZ9TffGaf9uXgsbxK5ns9UfNhpkngeqWS2LNdhCbN133X\"]},\"contracts/utils/RebaseHooks.sol\":{\"keccak256\":\"0xcddbcd5179a23a561db18566c3d67bcce37dacb72c0e289b2bada35bf2db98bc\",\"urls\":[\"bzz-raw://daff9fe817c1f0ecaaef8036fab214f7585b8e3e59c3346c2295fd1c45a7085b\",\"dweb:/ipfs/QmbXPSX597WfNmiVXabM9BMdhuYF1sZmtkxiZCoB8AmzZj\"]}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"methods": {"claimGovernance()": {"details": "Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor."}, "governor()": {"details": "Returns the address of the current Governor."}, "isGovernor()": {"details": "Returns true if the caller is the current Governor."}, "transferGovernance(address)": {"details": "Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete", "params": {"_newGovernor": "Address of the new Governor"}}}}, "userdoc": {"methods": {}}, "gasEstimates": {"creation": {"codeDepositCost": "457800", "executionCost": "infinite", "totalCost": "infinite"}, "external": {"claimGovernance()": "infinite", "franckHack()": "450", "governor()": "480", "isGovernor()": "540", "postRebase(bool)": "infinite", "setUniswapPairs(address[])": "infinite", "transferGovernance(address)": "infinite", "uniswapPairs(uint256)": "817"}}}