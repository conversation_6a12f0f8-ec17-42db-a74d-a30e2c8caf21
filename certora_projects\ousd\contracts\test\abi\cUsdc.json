{"abi": [{"constant": true, "inputs": [], "name": "name", "outputs": [{"name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "spender", "type": "address"}, {"name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "repayAmount", "type": "uint256"}], "name": "repayBorrow", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "reserveFactorMantissa", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "account", "type": "address"}], "name": "borrowBalanceCurrent", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "totalSupply", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "exchangeRateStored", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "src", "type": "address"}, {"name": "dst", "type": "address"}, {"name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "borrower", "type": "address"}, {"name": "repayAmount", "type": "uint256"}], "name": "repayBorrowBehalf", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "pendingAdmin", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "decimals", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "owner", "type": "address"}], "name": "balanceOfUnderlying", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "getCash", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "newComptroller", "type": "address"}], "name": "_setComptroller", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "totalBorrows", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "comptroller", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "reduceAmount", "type": "uint256"}], "name": "_reduceReserves", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "initialExchangeRateMantissa", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "accrualBlockNumber", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "underlying", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "owner", "type": "address"}], "name": "balanceOf", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "totalBorrowsCurrent", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "redeemAmount", "type": "uint256"}], "name": "redeemUnderlying", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "totalReserves", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "symbol", "outputs": [{"name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "account", "type": "address"}], "name": "borrowBalanceStored", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "mintAmount", "type": "uint256"}], "name": "mint", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [], "name": "accrueInterest", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "dst", "type": "address"}, {"name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "borrowIndex", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "supplyRatePerBlock", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "liquidator", "type": "address"}, {"name": "borrower", "type": "address"}, {"name": "seizeTokens", "type": "uint256"}], "name": "seize", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "newPendingAdmin", "type": "address"}], "name": "_setPendingAdmin", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [], "name": "exchangeRateCurrent", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "account", "type": "address"}], "name": "getAccountSnapshot", "outputs": [{"name": "", "type": "uint256"}, {"name": "", "type": "uint256"}, {"name": "", "type": "uint256"}, {"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "borrowAmount", "type": "uint256"}], "name": "borrow", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "redeemTokens", "type": "uint256"}], "name": "redeem", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "owner", "type": "address"}, {"name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "_acceptAdmin", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "newInterestRateModel", "type": "address"}], "name": "_setInterestRateModel", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "interestRateModel", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "borrower", "type": "address"}, {"name": "repayAmount", "type": "uint256"}, {"name": "cTokenCollateral", "type": "address"}], "name": "liquidateBorrow", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "admin", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "borrowRatePerBlock", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "newReserveFactorMantissa", "type": "uint256"}], "name": "_setReserveFactor", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "isCToken", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"inputs": [{"name": "underlying_", "type": "address"}, {"name": "comptroller_", "type": "address"}, {"name": "interestRateModel_", "type": "address"}, {"name": "initialExchangeRateMantissa_", "type": "uint256"}, {"name": "name_", "type": "string"}, {"name": "symbol_", "type": "string"}, {"name": "decimals_", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "interestAccumulated", "type": "uint256"}, {"indexed": false, "name": "borrowIndex", "type": "uint256"}, {"indexed": false, "name": "totalBorrows", "type": "uint256"}], "name": "AccrueInterest", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "minter", "type": "address"}, {"indexed": false, "name": "mintAmount", "type": "uint256"}, {"indexed": false, "name": "mintTokens", "type": "uint256"}], "name": "Mint", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "redeemer", "type": "address"}, {"indexed": false, "name": "redeemAmount", "type": "uint256"}, {"indexed": false, "name": "redeemTokens", "type": "uint256"}], "name": "Redeem", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "borrower", "type": "address"}, {"indexed": false, "name": "borrowAmount", "type": "uint256"}, {"indexed": false, "name": "accountBorrows", "type": "uint256"}, {"indexed": false, "name": "totalBorrows", "type": "uint256"}], "name": "Borrow", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "payer", "type": "address"}, {"indexed": false, "name": "borrower", "type": "address"}, {"indexed": false, "name": "repayAmount", "type": "uint256"}, {"indexed": false, "name": "accountBorrows", "type": "uint256"}, {"indexed": false, "name": "totalBorrows", "type": "uint256"}], "name": "RepayBorrow", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "liquidator", "type": "address"}, {"indexed": false, "name": "borrower", "type": "address"}, {"indexed": false, "name": "repayAmount", "type": "uint256"}, {"indexed": false, "name": "cTokenCollateral", "type": "address"}, {"indexed": false, "name": "seizeTokens", "type": "uint256"}], "name": "LiquidateBorrow", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "oldPendingAdmin", "type": "address"}, {"indexed": false, "name": "newPendingAdmin", "type": "address"}], "name": "NewPendingAdmin", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "oldAd<PERSON>", "type": "address"}, {"indexed": false, "name": "newAdmin", "type": "address"}], "name": "NewAdmin", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "oldComptroller", "type": "address"}, {"indexed": false, "name": "newComptroller", "type": "address"}], "name": "NewComptroller", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "oldInterestRateModel", "type": "address"}, {"indexed": false, "name": "newInterestRateModel", "type": "address"}], "name": "NewMarketInterestRateModel", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "oldReserveFactorMantissa", "type": "uint256"}, {"indexed": false, "name": "newReserveFactorMantissa", "type": "uint256"}], "name": "NewReserveFactor", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "admin", "type": "address"}, {"indexed": false, "name": "reduceAmount", "type": "uint256"}, {"indexed": false, "name": "newTotalReserves", "type": "uint256"}], "name": "ReservesReduced", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "error", "type": "uint256"}, {"indexed": false, "name": "info", "type": "uint256"}, {"indexed": false, "name": "detail", "type": "uint256"}], "name": "Failure", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "from", "type": "address"}, {"indexed": true, "name": "to", "type": "address"}, {"indexed": false, "name": "amount", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "owner", "type": "address"}, {"indexed": true, "name": "spender", "type": "address"}, {"indexed": false, "name": "amount", "type": "uint256"}], "name": "Approval", "type": "event"}]}