// SPDX-License-Identifier: MIT
// OpenZeppelin Contracts for Cairo v0.2.0 (utils/constants.cairo)

%lang starknet

//
// Numbers
//

const UINT8_MAX = 256;

//
// Interface Ids
//

// ERC165
const IERC165_ID = 0x01ffc9a7;
const INVALID_ID = 0xffffffff;

// Account
const IACCOUNT_ID = 0xf10dbd44;

// ERC721
const IERC721_ID = 0x80ac58cd;
const IERC721_RECEIVER_ID = 0x150b7a02;
const IERC721_METADATA_ID = 0x5b5e139f;
const IERC721_ENUMERABLE_ID = 0x780e9d63;

// AccessControl
const IACCESSCONTROL_ID = 0x7965db0b;

//
// Roles
//

const DEFAULT_ADMIN_ROLE = 0;
