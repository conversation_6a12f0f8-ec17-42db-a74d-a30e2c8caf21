// Copyright 2021-2022, Offchain Labs, Inc.
// For license information, see https://github.com/nitro/blob/master/LICENSE
// SPDX-License-Identifier: BUSL-1.1

// solhint-disable-next-line compiler-version
pragma solidity >=0.6.9 <0.9.0;

interface IGasRefunder {
  function onGasSpent(
    address payable spender,
    uint256 gasUsed,
    uint256 calldataSize
  ) external returns (bool success);
}

abstract contract GasRefundEnabled {
  /// @dev this refunds the sender for execution costs of the tx
  /// calldata costs are only refunded if `msg.sender == tx.origin` to guarantee the value refunded relates to charging
  /// for the `tx.input`. this avoids a possible attack where you generate large calldata from a contract and get over-refunded
  modifier refundsGas(IGasRefunder gasRefunder) {
    uint256 startGasLeft = gasleft();
    _;
    if (address(gasRefunder) != address(0)) {
      uint256 calldataSize = 0;
      // if triggered in a contract call, the spender may be overrefunded by appending dummy data to the call
      // so we check if it is a top level call, which would mean the sender paid calldata as part of tx.input
      // solhint-disable-next-line avoid-tx-origin
      if (msg.sender == tx.origin) {
        assembly {
          calldataSize := calldatasize()
        }
      }
      gasRefunder.onGasSpent(payable(msg.sender), startGasLeft - gasleft(), calldataSize);
    }
  }
}
