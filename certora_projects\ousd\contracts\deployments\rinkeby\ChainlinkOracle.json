{"address": "******************************************", "abi": [{"constant": true, "inputs": [], "name": "governor", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "string", "name": "symbol", "type": "string"}], "name": "tokEthPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "string", "name": "symbol", "type": "string"}], "name": "tokUsdPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "claimGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "ethUsdPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "isGovernor", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_newGovernor", "type": "address"}], "name": "transferGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "feed", "type": "address"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "bool", "name": "directToUsd", "type": "bool"}], "name": "registerFeed", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "string", "name": "symbol", "type": "string"}], "name": "price", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "ethFeed_", "type": "address"}], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "_feed", "type": "address"}, {"indexed": false, "internalType": "string", "name": "_symbol", "type": "string"}, {"indexed": false, "internalType": "bool", "name": "_directToUsd", "type": "bool"}], "name": "FeedRegistered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "PendingGovernorshipTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "GovernorshipTransferred", "type": "event"}], "transactionHash": "0xd54f513a2e36e55ab83f273dcb2d4d361f14c258f2d89ee61148a411208d4672", "receipt": {"to": null, "from": "0xD85A569F3C26f81070544451131c742283360400", "contractAddress": "******************************************", "transactionIndex": 3, "gasUsed": "906662", "logsBloom": "0x00000000400000000000000000000000000000000000000000000000000000000000000000000000000000002000000000008000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000800000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000020000000400000000000000000000000000000000400000000000000000000000010", "blockHash": "0x0a95d30bbff5e54cae6a1d9d2024b1e95814d8d164d9cd91a27ec38aae45eb71", "transactionHash": "0xd54f513a2e36e55ab83f273dcb2d4d361f14c258f2d89ee61148a411208d4672", "logs": [{"transactionIndex": 3, "blockNumber": 7828275, "transactionHash": "0xd54f513a2e36e55ab83f273dcb2d4d361f14c258f2d89ee61148a411208d4672", "address": "******************************************", "topics": ["0xc7c0c772add429241571afb3805861fb3cfa2af374534088b76cdb4325a87e9a", "0x0000000000000000000000000000000000000000000000000000000000000000", "0x000000000000000000000000d85a569f3c26f81070544451131c742283360400"], "data": "0x", "logIndex": 1, "blockHash": "0x0a95d30bbff5e54cae6a1d9d2024b1e95814d8d164d9cd91a27ec38aae45eb71"}], "blockNumber": 7828275, "cumulativeGasUsed": "1176932", "status": 1, "byzantium": true}, "args": ["0xCCc725dEc3Bd914ba4DE8c7A3F32677B397d23a3"], "solcInputHash": "6c5ec0829f89df496fcae564eaa88d66", "metadata": "{\"compiler\":{\"version\":\"0.5.11+commit.22be8592.mod\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"constant\":true,\"inputs\":[],\"name\":\"governor\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"}],\"name\":\"tokEthPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"}],\"name\":\"tokUsdPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"claimGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"ethUsdPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"isGovernor\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_newGovernor\",\"type\":\"address\"}],\"name\":\"transferGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"feed\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"bool\",\"name\":\"directToUsd\",\"type\":\"bool\"}],\"name\":\"registerFeed\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"}],\"name\":\"price\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"ethFeed_\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"_feed\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"_symbol\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"_directToUsd\",\"type\":\"bool\"}],\"name\":\"FeedRegistered\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"PendingGovernorshipTransfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"GovernorshipTransferred\",\"type\":\"event\"}],\"devdoc\":{\"methods\":{\"claimGovernance()\":{\"details\":\"Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor.\"},\"governor()\":{\"details\":\"Returns the address of the current Governor.\"},\"isGovernor()\":{\"details\":\"Returns true if the caller is the current Governor.\"},\"transferGovernance(address)\":{\"details\":\"Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete\",\"params\":{\"_newGovernor\":\"Address of the new Governor\"}}}},\"userdoc\":{\"methods\":{}}},\"settings\":{\"compilationTarget\":{\"contracts/oracle/ChainlinkOracle.sol\":\"ChainlinkOracle\"},\"evmVersion\":\"petersburg\",\"libraries\":{},\"metadata\":{\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"contracts/governance/Governable.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\n/**\\n * @title OUSD Governable Contract\\n * @dev Copy of the openzeppelin Ownable.sol contract with nomenclature change\\n *      from owner to governor and renounce methods removed. Does not use\\n *      Context.sol like Ownable.sol does for simplification.\\n * <AUTHOR> Protocol Inc\\n */\\ncontract Governable {\\n    // Storage position of the owner and pendingOwner of the contract\\n    // keccak256(\\\"OUSD.governor\\\");\\n    bytes32\\n        private constant governorPosition = 0x7bea13895fa79d2831e0a9e28edede30099005a50d652d8957cf8a607ee6ca4a;\\n\\n    // keccak256(\\\"OUSD.pending.governor\\\");\\n    bytes32\\n        private constant pendingGovernorPosition = 0x44c4d30b2eaad5130ad70c3ba6972730566f3e6359ab83e800d905c61b1c51db;\\n\\n    // keccak256(\\\"OUSD.reentry.status\\\");\\n    bytes32\\n        private constant reentryStatusPosition = 0x53bf423e48ed90e97d02ab0ebab13b2a235a6bfbe9c321847d5c175333ac4535;\\n\\n    // See OpenZeppelin ReentrancyGuard implementation\\n    uint256 constant _NOT_ENTERED = 1;\\n    uint256 constant _ENTERED = 2;\\n\\n    event PendingGovernorshipTransfer(\\n        address indexed previousGovernor,\\n        address indexed newGovernor\\n    );\\n\\n    event GovernorshipTransferred(\\n        address indexed previousGovernor,\\n        address indexed newGovernor\\n    );\\n\\n    /**\\n     * @dev Initializes the contract setting the deployer as the initial Governor.\\n     */\\n    constructor() internal {\\n        _setGovernor(msg.sender);\\n        emit GovernorshipTransferred(address(0), _governor());\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current Governor.\\n     */\\n    function governor() public view returns (address) {\\n        return _governor();\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current Governor.\\n     */\\n    function _governor() internal view returns (address governorOut) {\\n        bytes32 position = governorPosition;\\n        assembly {\\n            governorOut := sload(position)\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the address of the pending Governor.\\n     */\\n    function _pendingGovernor()\\n        internal\\n        view\\n        returns (address pendingGovernor)\\n    {\\n        bytes32 position = pendingGovernorPosition;\\n        assembly {\\n            pendingGovernor := sload(position)\\n        }\\n    }\\n\\n    /**\\n     * @dev Throws if called by any account other than the Governor.\\n     */\\n    modifier onlyGovernor() {\\n        require(isGovernor(), \\\"Caller is not the Governor\\\");\\n        _;\\n    }\\n\\n    /**\\n     * @dev Returns true if the caller is the current Governor.\\n     */\\n    function isGovernor() public view returns (bool) {\\n        return msg.sender == _governor();\\n    }\\n\\n    function _setGovernor(address newGovernor) internal {\\n        bytes32 position = governorPosition;\\n        assembly {\\n            sstore(position, newGovernor)\\n        }\\n    }\\n\\n    /**\\n     * @dev Prevents a contract from calling itself, directly or indirectly.\\n     * Calling a `nonReentrant` function from another `nonReentrant`\\n     * function is not supported. It is possible to prevent this from happening\\n     * by making the `nonReentrant` function external, and make it call a\\n     * `private` function that does the actual work.\\n     */\\n    modifier nonReentrant() {\\n        bytes32 position = reentryStatusPosition;\\n        uint256 _reentry_status;\\n        assembly {\\n            _reentry_status := sload(position)\\n        }\\n\\n        // On the first call to nonReentrant, _notEntered will be true\\n        require(_reentry_status != _ENTERED, \\\"Reentrant call\\\");\\n\\n        // Any calls to nonReentrant after this point will fail\\n        assembly {\\n            sstore(position, _ENTERED)\\n        }\\n\\n        _;\\n\\n        // By storing the original value once again, a refund is triggered (see\\n        // https://eips.ethereum.org/EIPS/eip-2200)\\n        assembly {\\n            sstore(position, _NOT_ENTERED)\\n        }\\n    }\\n\\n    function _setPendingGovernor(address newGovernor) internal {\\n        bytes32 position = pendingGovernorPosition;\\n        assembly {\\n            sstore(position, newGovernor)\\n        }\\n    }\\n\\n    /**\\n     * @dev Transfers Governance of the contract to a new account (`newGovernor`).\\n     * Can only be called by the current Governor. Must be claimed for this to complete\\n     * @param _newGovernor Address of the new Governor\\n     */\\n    function transferGovernance(address _newGovernor) external onlyGovernor {\\n        _setPendingGovernor(_newGovernor);\\n        emit PendingGovernorshipTransfer(_governor(), _newGovernor);\\n    }\\n\\n    /**\\n     * @dev Claim Governance of the contract to a new account (`newGovernor`).\\n     * Can only be called by the new Governor.\\n     */\\n    function claimGovernance() external {\\n        require(\\n            msg.sender == _pendingGovernor(),\\n            \\\"Only the pending Governor can complete the claim\\\"\\n        );\\n        _changeGovernor(msg.sender);\\n    }\\n\\n    /**\\n     * @dev Change Governance of the contract to a new account (`newGovernor`).\\n     * @param _newGovernor Address of the new Governor\\n     */\\n    function _changeGovernor(address _newGovernor) internal {\\n        require(_newGovernor != address(0), \\\"New Governor is address(0)\\\");\\n        emit GovernorshipTransferred(_governor(), _newGovernor);\\n        _setGovernor(_newGovernor);\\n    }\\n}\\n\",\"keccak256\":\"0x3e51ea48102945bf4b305bf9722a07514a585a29555d92f8c84352d1a4cfcee1\"},\"contracts/interfaces/IEthUsdOracle.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\ninterface IEthUsdOracle {\\n    /**\\n     * @notice Returns ETH price in USD.\\n     * @return Price in USD with 6 decimal digits.\\n     */\\n    function ethUsdPrice() external view returns (uint256);\\n\\n    /**\\n     * @notice Returns token price in USD.\\n     * @param symbol. Asset symbol. For ex. \\\"DAI\\\".\\n     * @return Price in USD with 6 decimal digits.\\n     */\\n    function tokUsdPrice(string calldata symbol)\\n        external\\n        view\\n        returns (uint256);\\n\\n    /**\\n     * @notice Returns the asset price in ETH.\\n     * @param symbol. Asset symbol. For ex. \\\"DAI\\\".\\n     * @return Price in ETH with 8 decimal digits.\\n     */\\n    function tokEthPrice(string calldata symbol)\\n        external\\n        view\\n        returns (uint256);\\n}\\n\\ninterface IViewEthUsdOracle {\\n    /**\\n     * @notice Returns ETH price in USD.\\n     * @return Price in USD with 6 decimal digits.\\n     */\\n    function ethUsdPrice() external view returns (uint256);\\n\\n    /**\\n     * @notice Returns token price in USD.\\n     * @param symbol. Asset symbol. For ex. \\\"DAI\\\".\\n     * @return Price in USD with 6 decimal digits.\\n     */\\n    function tokUsdPrice(string calldata symbol)\\n        external\\n        view\\n        returns (uint256);\\n\\n    /**\\n     * @notice Returns the asset price in ETH.\\n     * @param symbol. Asset symbol. For ex. \\\"DAI\\\".\\n     * @return Price in ETH with 8 decimal digits.\\n     */\\n    function tokEthPrice(string calldata symbol)\\n        external\\n        view\\n        returns (uint256);\\n}\\n\",\"keccak256\":\"0xa57ebbb67fc35436a173988dcefc8b35811ae2a5777ba2d7d734101e41e901a3\"},\"contracts/interfaces/IPriceOracle.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\ninterface IPriceOracle {\\n    /**\\n     * @dev returns the asset price in USD, 6 decimal digits.\\n     * Compatible with the Open Price Feed.\\n     */\\n    function price(string calldata symbol) external view returns (uint256);\\n}\\n\",\"keccak256\":\"0x943aaa48eecafcbe186d21d3e4d7a2dbe356e45611b4d9794f0df63ba9abdec7\"},\"contracts/oracle/AggregatorV3Interface.sol\":{\"content\":\"pragma solidity ^0.5.11;\\n\\ninterface AggregatorV3Interface {\\n    function decimals() external view returns (uint8);\\n\\n    function description() external view returns (string memory);\\n\\n    function version() external view returns (uint256);\\n\\n    // getRoundData and latestRoundData should both raise \\\"No data present\\\"\\n    // if they do not have data to report, instead of returning unset values\\n    // which could be misinterpreted as actual reported values.\\n    function getRoundData(uint80 _roundId)\\n        external\\n        view\\n        returns (\\n            uint80 roundId,\\n            int256 answer,\\n            uint256 startedAt,\\n            uint256 updatedAt,\\n            uint80 answeredInRound\\n        );\\n\\n    function latestRoundData()\\n        external\\n        view\\n        returns (\\n            uint80 roundId,\\n            int256 answer,\\n            uint256 startedAt,\\n            uint256 updatedAt,\\n            uint80 answeredInRound\\n        );\\n}\\n\",\"keccak256\":\"0xef73b8afeb1cf9a35e7f5d9cbc878eff44841f7a78a56a0aaafe9943c32848f7\"},\"contracts/oracle/ChainlinkOracle.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\n/**\\n * @title OUSD ChainlinkOracle Contract\\n * <AUTHOR> Protocol Inc\\n */\\nimport \\\"./AggregatorV3Interface.sol\\\";\\nimport { IPriceOracle } from \\\"../interfaces/IPriceOracle.sol\\\";\\nimport { IEthUsdOracle } from \\\"../interfaces/IEthUsdOracle.sol\\\";\\nimport { Governable } from \\\"../governance/Governable.sol\\\";\\n\\ncontract ChainlinkOracle is IEthUsdOracle, IPriceOracle, Governable {\\n    event FeedRegistered(address _feed, string _symbol, bool _directToUsd);\\n\\n    address ethFeed;\\n\\n    struct FeedConfig {\\n        address feed;\\n        uint8 decimals;\\n        bool directToUsd;\\n    }\\n\\n    mapping(bytes32 => FeedConfig) feeds;\\n\\n    uint8 ethDecimals;\\n\\n    string constant ethSymbol = \\\"ETH\\\";\\n    bytes32 constant ethHash = keccak256(abi.encodePacked(ethSymbol));\\n\\n    constructor(address ethFeed_) public {\\n        ethFeed = ethFeed_;\\n        ethDecimals = AggregatorV3Interface(ethFeed_).decimals();\\n    }\\n\\n    function registerFeed(\\n        address feed,\\n        string memory symbol,\\n        bool directToUsd\\n    ) public onlyGovernor {\\n        FeedConfig storage config = feeds[keccak256(abi.encodePacked(symbol))];\\n\\n        config.feed = feed;\\n        config.decimals = AggregatorV3Interface(feed).decimals();\\n        config.directToUsd = directToUsd;\\n\\n        emit FeedRegistered(feed, symbol, directToUsd);\\n    }\\n\\n    function getLatestPrice(address feed) internal view returns (int256) {\\n        (\\n            uint80 roundID,\\n            int256 price,\\n            uint256 startedAt,\\n            uint256 timeStamp,\\n            uint80 answeredInRound\\n        ) = AggregatorV3Interface(feed).latestRoundData();\\n        // silence\\n        roundID;\\n        startedAt;\\n        timeStamp;\\n        answeredInRound;\\n        return price;\\n    }\\n\\n    function ethUsdPrice() external view returns (uint256) {\\n        return (uint256(getLatestPrice(ethFeed)) /\\n            (uint256(10)**(ethDecimals - 6)));\\n    }\\n\\n    function tokUsdPrice(string calldata symbol)\\n        external\\n        view\\n        returns (uint256)\\n    {\\n        bytes32 tokenSymbolHash = keccak256(abi.encodePacked(symbol));\\n        FeedConfig storage config = feeds[tokenSymbolHash];\\n        int256 tPrice = getLatestPrice(config.feed);\\n\\n        require(config.directToUsd, \\\"Price is not direct to usd\\\");\\n        require(tPrice > 0, \\\"Price must be greater than zero\\\");\\n        return uint256(tPrice);\\n    }\\n\\n    function tokEthPrice(string calldata symbol)\\n        external\\n        view\\n        returns (uint256)\\n    {\\n        bytes32 tokenSymbolHash = keccak256(abi.encodePacked(symbol));\\n        FeedConfig storage config = feeds[tokenSymbolHash];\\n        int256 tPrice = getLatestPrice(config.feed);\\n\\n        require(!config.directToUsd, \\\"Price is not in terms of ETH\\\");\\n        require(tPrice > 0, \\\"Price must be greater than zero\\\");\\n        //attempt to return 8 digit precision here\\n        return uint256(tPrice) / (uint256(10)**(config.decimals - 8));\\n    }\\n\\n    // This actually calculate the latest price from outside oracles\\n    // It's a view but substantially more costly in terms of calculation\\n    function price(string calldata symbol) external view returns (uint256) {\\n        bytes32 tokenSymbolHash = keccak256(abi.encodePacked(symbol));\\n\\n        if (ethHash == tokenSymbolHash) {\\n            return (uint256(getLatestPrice(ethFeed)) /\\n                (uint256(10)**(ethDecimals - 6)));\\n        } else {\\n            FeedConfig storage config = feeds[tokenSymbolHash];\\n            int256 tPrice = getLatestPrice(config.feed);\\n\\n            if (config.directToUsd) {\\n                require(tPrice > 0, \\\"Price must be greater than zero\\\");\\n                return uint256(tPrice);\\n            } else {\\n                int256 ethPrice = getLatestPrice(ethFeed); // grab the eth price from the open oracle\\n                require(\\n                    tPrice > 0 && ethPrice > 0,\\n                    \\\"Both eth and price must be greater than zero\\\"\\n                );\\n                //not actually sure why it's 6 units here, this is just to match with openoracle for now\\n                return\\n                    mul(uint256(tPrice), uint256(ethPrice)) /\\n                    (uint256(10)**(ethDecimals + config.decimals - 6));\\n            }\\n        }\\n    }\\n\\n    /// @dev Overflow proof multiplication\\n    function mul(uint256 a, uint256 b) internal pure returns (uint256) {\\n        if (a == 0) return 0;\\n        uint256 c = a * b;\\n        require(c / a == b, \\\"multiplication overflow\\\");\\n        return c;\\n    }\\n}\\n\",\"keccak256\":\"0x19460f074e28faa066c707e60e830d61984d6d02834e441f38fbb33c50b800ce\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"methods": {"claimGovernance()": {"details": "Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor."}, "governor()": {"details": "Returns the address of the current Governor."}, "isGovernor()": {"details": "Returns true if the caller is the current Governor."}, "transferGovernance(address)": {"details": "Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete", "params": {"_newGovernor": "Address of the new Governor"}}}}, "userdoc": {"methods": {}}}