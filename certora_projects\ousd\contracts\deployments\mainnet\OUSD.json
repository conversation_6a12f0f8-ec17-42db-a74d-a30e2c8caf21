{"address": "0x159ea2fA3a92DAE6b9f21D6753AA6a8EA5bf77BA", "abi": [{"constant": true, "inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "string", "name": "_nameArg", "type": "string"}, {"internalType": "string", "name": "_symbolArg", "type": "string"}, {"internalType": "address", "name": "_vault<PERSON><PERSON>ress", "type": "address"}], "name": "initialize", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "rebasingCredits", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_spender", "type": "address"}, {"internalType": "uint256", "name": "_value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "governor", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_from", "type": "address"}, {"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "_value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "_decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_spender", "type": "address"}, {"internalType": "uint256", "name": "_addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "_newTotalSupply", "type": "uint256"}], "name": "changeSupply", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "_totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_account", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "mint", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "rebaseState", "outputs": [{"internalType": "enum OUSD.RebaseOptions", "name": "", "type": "uint8"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "claimGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "nonRebasingCreditsPerToken", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "rebasingCreditsPerToken", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "_account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_spender", "type": "address"}, {"internalType": "uint256", "name": "_subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "_value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "_symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "rebaseOptOut", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "isGovernor", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "_name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_newGovernor", "type": "address"}], "name": "transferGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "address", "name": "_spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "nonRebasingSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "rebaseOptIn", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "_account", "type": "address"}], "name": "creditsBalanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "totalSupply", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "rebasingCredits", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "rebasingCreditsPerToken", "type": "uint256"}], "name": "TotalSupplyUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "PendingGovernorshipTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "GovernorshipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}], "transactionHash": "0x6f9974512abc4f6631cdfe35073df9d4f1528605e9124a4b7432511b6d8ae5ec", "receipt": {"to": null, "from": "0x71F78361537A6f7B6818e7A760c8bC0146D93f50", "contractAddress": "0x159ea2fA3a92DAE6b9f21D6753AA6a8EA5bf77BA", "transactionIndex": 261, "gasUsed": "2046140", "logsBloom": "0x00000000000000000000000040000000000000000000001000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000020000000000000000000800000000000000000000000000000000000004000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000008000000000000200000000000000000020000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x758b9e6553456ce5820e68a9f22cbe309f0c2048a9a24282fb216eddf990f960", "transactionHash": "0x6f9974512abc4f6631cdfe35073df9d4f1528605e9124a4b7432511b6d8ae5ec", "logs": [{"transactionIndex": 261, "blockNumber": 11585982, "transactionHash": "0x6f9974512abc4f6631cdfe35073df9d4f1528605e9124a4b7432511b6d8ae5ec", "address": "0x159ea2fA3a92DAE6b9f21D6753AA6a8EA5bf77BA", "topics": ["0xc7c0c772add429241571afb3805861fb3cfa2af374534088b76cdb4325a87e9a", "0x0000000000000000000000000000000000000000000000000000000000000000", "0x00000000000000000000000071f78361537a6f7b6818e7a760c8bc0146d93f50"], "data": "0x", "logIndex": 122, "blockHash": "0x758b9e6553456ce5820e68a9f22cbe309f0c2048a9a24282fb216eddf990f960"}], "blockNumber": 11585982, "cumulativeGasUsed": "10706118", "status": 1, "byzantium": true}, "args": [], "solcInputHash": "6c5ec0829f89df496fcae564eaa88d66", "metadata": "{\"compiler\":{\"version\":\"0.5.11+commit.22be8592.mod\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"constant\":true,\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"string\",\"name\":\"_nameArg\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"_symbolArg\",\"type\":\"string\"},{\"internalType\":\"address\",\"name\":\"_vaultAddress\",\"type\":\"address\"}],\"name\":\"initialize\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"rebasingCredits\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_value\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"governor\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_value\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"_decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_addedValue\",\"type\":\"uint256\"}],\"name\":\"increaseAllowance\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_newTotalSupply\",\"type\":\"uint256\"}],\"name\":\"changeSupply\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"_totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"}],\"name\":\"mint\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"vaultAddress\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"rebaseState\",\"outputs\":[{\"internalType\":\"enum OUSD.RebaseOptions\",\"name\":\"\",\"type\":\"uint8\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"claimGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"nonRebasingCreditsPerToken\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"rebasingCreditsPerToken\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"burn\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_subtractedValue\",\"type\":\"uint256\"}],\"name\":\"decreaseAllowance\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_value\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"_symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"rebaseOptOut\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"isGovernor\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"_name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_newGovernor\",\"type\":\"address\"}],\"name\":\"transferGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"nonRebasingSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"rebaseOptIn\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_account\",\"type\":\"address\"}],\"name\":\"creditsBalanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"totalSupply\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"rebasingCredits\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"rebasingCreditsPerToken\",\"type\":\"uint256\"}],\"name\":\"TotalSupplyUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"PendingGovernorshipTransfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"GovernorshipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"}],\"devdoc\":{\"methods\":{\"allowance(address,address)\":{\"details\":\"Function to check the _amount of tokens that an owner has allowed to a _spender.\",\"params\":{\"_owner\":\"The address which owns the funds.\",\"_spender\":\"The address which will spend the funds.\"},\"return\":\"The number of tokens still available for the _spender.\"},\"approve(address,uint256)\":{\"details\":\"Approve the passed address to spend the specified _amount of tokens on behalf of msg.sender. This method is included for ERC20 compatibility. increaseAllowance and decreaseAllowance should be used instead. Changing an allowance with this method brings the risk that someone may transfer both the old and the new allowance - if they are both greater than zero - if a transfer transaction is mined before the later approve() call is mined.\",\"params\":{\"_spender\":\"The address which will spend the funds.\",\"_value\":\"The _amount of tokens to be spent.\"}},\"balanceOf(address)\":{\"details\":\"Gets the balance of the specified address.\",\"params\":{\"_account\":\"Address to query the balance of.\"},\"return\":\"A uint256 representing the _amount of base units owned by the        specified address.\"},\"burn(address,uint256)\":{\"details\":\"Burns tokens, decreasing totalSupply.\"},\"changeSupply(uint256)\":{\"details\":\"Modify the supply without minting new tokens. This uses a change in     the exchange rate between \\\"credits\\\" and OUSD tokens to change balances.\",\"params\":{\"_newTotalSupply\":\"New total supply of OUSD.\"},\"return\":\"uint256 representing the new total supply.\"},\"claimGovernance()\":{\"details\":\"Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor.\"},\"creditsBalanceOf(address)\":{\"details\":\"Gets the credits balance of the specified address.\",\"params\":{\"_account\":\"The address to query the balance of.\"},\"return\":\"(uint256, uint256) Credit balance and credits per token of the        address\"},\"decimals()\":{\"details\":\"Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5,05` (`505 / 10 ** 2`).     * Tokens usually opt for a value of 18, imitating the relationship between Ether and Wei.     * NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}.\"},\"decreaseAllowance(address,uint256)\":{\"details\":\"Decrease the _amount of tokens that an owner has allowed to a _spender.\",\"params\":{\"_spender\":\"The address which will spend the funds.\",\"_subtractedValue\":\"The _amount of tokens to decrease the allowance by.\"}},\"governor()\":{\"details\":\"Returns the address of the current Governor.\"},\"increaseAllowance(address,uint256)\":{\"details\":\"Increase the _amount of tokens that an owner has allowed to a _spender. This method should be used instead of approve() to avoid the double approval vulnerability described above.\",\"params\":{\"_addedValue\":\"The _amount of tokens to increase the allowance by.\",\"_spender\":\"The address which will spend the funds.\"}},\"isGovernor()\":{\"details\":\"Returns true if the caller is the current Governor.\"},\"mint(address,uint256)\":{\"details\":\"Mints new tokens, increasing totalSupply.\"},\"name()\":{\"details\":\"Returns the name of the token.\"},\"rebaseOptIn()\":{\"details\":\"Add a contract address to the non rebasing exception list. I.e. the address's balance will be part of rebases so the account will be exposed to upside and downside.\"},\"rebaseOptOut()\":{\"details\":\"Remove a contract address to the non rebasing exception list.\"},\"symbol()\":{\"details\":\"Returns the symbol of the token, usually a shorter version of the name.\"},\"totalSupply()\":{\"return\":\"The total supply of OUSD.\"},\"transfer(address,uint256)\":{\"details\":\"Transfer tokens to a specified address.\",\"params\":{\"_to\":\"the address to transfer to.\",\"_value\":\"the _amount to be transferred.\"},\"return\":\"true on success.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"Transfer tokens from one address to another.\",\"params\":{\"_from\":\"The address you want to send tokens from.\",\"_to\":\"The address you want to transfer to.\",\"_value\":\"The _amount of tokens to be transferred.\"}},\"transferGovernance(address)\":{\"details\":\"Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete\",\"params\":{\"_newGovernor\":\"Address of the new Governor\"}}}},\"userdoc\":{\"methods\":{},\"notice\":\"NOTE that this is an ERC20 token but the invariant that the sum of balanceOf(x) for all x is not >= totalSupply(). This is a consequence of the rebasing design. Any integrations with OUSD should be aware.\"}},\"settings\":{\"compilationTarget\":{\"contracts/token/OUSD.sol\":\"OUSD\"},\"evmVersion\":\"petersburg\",\"libraries\":{},\"metadata\":{\"useLiteralContent\":true},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts/math/SafeMath.sol\":{\"content\":\"pragma solidity ^0.5.0;\\n\\n/**\\n * @dev Wrappers over Solidity's arithmetic operations with added overflow\\n * checks.\\n *\\n * Arithmetic operations in Solidity wrap on overflow. This can easily result\\n * in bugs, because programmers usually assume that an overflow raises an\\n * error, which is the standard behavior in high level programming languages.\\n * `SafeMath` restores this intuition by reverting the transaction when an\\n * operation overflows.\\n *\\n * Using this library instead of the unchecked operations eliminates an entire\\n * class of bugs, so it's recommended to use it always.\\n */\\nlibrary SafeMath {\\n    /**\\n     * @dev Returns the addition of two unsigned integers, reverting on\\n     * overflow.\\n     *\\n     * Counterpart to Solidity's `+` operator.\\n     *\\n     * Requirements:\\n     * - Addition cannot overflow.\\n     */\\n    function add(uint256 a, uint256 b) internal pure returns (uint256) {\\n        uint256 c = a + b;\\n        require(c >= a, \\\"SafeMath: addition overflow\\\");\\n\\n        return c;\\n    }\\n\\n    /**\\n     * @dev Returns the subtraction of two unsigned integers, reverting on\\n     * overflow (when the result is negative).\\n     *\\n     * Counterpart to Solidity's `-` operator.\\n     *\\n     * Requirements:\\n     * - Subtraction cannot overflow.\\n     */\\n    function sub(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return sub(a, b, \\\"SafeMath: subtraction overflow\\\");\\n    }\\n\\n    /**\\n     * @dev Returns the subtraction of two unsigned integers, reverting with custom message on\\n     * overflow (when the result is negative).\\n     *\\n     * Counterpart to Solidity's `-` operator.\\n     *\\n     * Requirements:\\n     * - Subtraction cannot overflow.\\n     *\\n     * _Available since v2.4.0._\\n     */\\n    function sub(uint256 a, uint256 b, string memory errorMessage) internal pure returns (uint256) {\\n        require(b <= a, errorMessage);\\n        uint256 c = a - b;\\n\\n        return c;\\n    }\\n\\n    /**\\n     * @dev Returns the multiplication of two unsigned integers, reverting on\\n     * overflow.\\n     *\\n     * Counterpart to Solidity's `*` operator.\\n     *\\n     * Requirements:\\n     * - Multiplication cannot overflow.\\n     */\\n    function mul(uint256 a, uint256 b) internal pure returns (uint256) {\\n        // Gas optimization: this is cheaper than requiring 'a' not being zero, but the\\n        // benefit is lost if 'b' is also tested.\\n        // See: https://github.com/OpenZeppelin/openzeppelin-contracts/pull/522\\n        if (a == 0) {\\n            return 0;\\n        }\\n\\n        uint256 c = a * b;\\n        require(c / a == b, \\\"SafeMath: multiplication overflow\\\");\\n\\n        return c;\\n    }\\n\\n    /**\\n     * @dev Returns the integer division of two unsigned integers. Reverts on\\n     * division by zero. The result is rounded towards zero.\\n     *\\n     * Counterpart to Solidity's `/` operator. Note: this function uses a\\n     * `revert` opcode (which leaves remaining gas untouched) while Solidity\\n     * uses an invalid opcode to revert (consuming all remaining gas).\\n     *\\n     * Requirements:\\n     * - The divisor cannot be zero.\\n     */\\n    function div(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return div(a, b, \\\"SafeMath: division by zero\\\");\\n    }\\n\\n    /**\\n     * @dev Returns the integer division of two unsigned integers. Reverts with custom message on\\n     * division by zero. The result is rounded towards zero.\\n     *\\n     * Counterpart to Solidity's `/` operator. Note: this function uses a\\n     * `revert` opcode (which leaves remaining gas untouched) while Solidity\\n     * uses an invalid opcode to revert (consuming all remaining gas).\\n     *\\n     * Requirements:\\n     * - The divisor cannot be zero.\\n     *\\n     * _Available since v2.4.0._\\n     */\\n    function div(uint256 a, uint256 b, string memory errorMessage) internal pure returns (uint256) {\\n        // Solidity only automatically asserts when dividing by 0\\n        require(b > 0, errorMessage);\\n        uint256 c = a / b;\\n        // assert(a == b * c + a % b); // There is no case in which this doesn't hold\\n\\n        return c;\\n    }\\n\\n    /**\\n     * @dev Returns the remainder of dividing two unsigned integers. (unsigned integer modulo),\\n     * Reverts when dividing by zero.\\n     *\\n     * Counterpart to Solidity's `%` operator. This function uses a `revert`\\n     * opcode (which leaves remaining gas untouched) while Solidity uses an\\n     * invalid opcode to revert (consuming all remaining gas).\\n     *\\n     * Requirements:\\n     * - The divisor cannot be zero.\\n     */\\n    function mod(uint256 a, uint256 b) internal pure returns (uint256) {\\n        return mod(a, b, \\\"SafeMath: modulo by zero\\\");\\n    }\\n\\n    /**\\n     * @dev Returns the remainder of dividing two unsigned integers. (unsigned integer modulo),\\n     * Reverts with custom message when dividing by zero.\\n     *\\n     * Counterpart to Solidity's `%` operator. This function uses a `revert`\\n     * opcode (which leaves remaining gas untouched) while Solidity uses an\\n     * invalid opcode to revert (consuming all remaining gas).\\n     *\\n     * Requirements:\\n     * - The divisor cannot be zero.\\n     *\\n     * _Available since v2.4.0._\\n     */\\n    function mod(uint256 a, uint256 b, string memory errorMessage) internal pure returns (uint256) {\\n        require(b != 0, errorMessage);\\n        return a % b;\\n    }\\n}\\n\",\"keccak256\":\"0x640b6dee7a4b830bdfd52b5031a07fc2b12209f5b2e29e5d364a7d37f69d8076\"},\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"content\":\"pragma solidity ^0.5.0;\\n\\n/**\\n * @dev Interface of the ERC20 standard as defined in the EIP. Does not include\\n * the optional functions; to access them see {ERC20Detailed}.\\n */\\ninterface IERC20 {\\n    /**\\n     * @dev Returns the amount of tokens in existence.\\n     */\\n    function totalSupply() external view returns (uint256);\\n\\n    /**\\n     * @dev Returns the amount of tokens owned by `account`.\\n     */\\n    function balanceOf(address account) external view returns (uint256);\\n\\n    /**\\n     * @dev Moves `amount` tokens from the caller's account to `recipient`.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transfer(address recipient, uint256 amount) external returns (bool);\\n\\n    /**\\n     * @dev Returns the remaining number of tokens that `spender` will be\\n     * allowed to spend on behalf of `owner` through {transferFrom}. This is\\n     * zero by default.\\n     *\\n     * This value changes when {approve} or {transferFrom} are called.\\n     */\\n    function allowance(address owner, address spender) external view returns (uint256);\\n\\n    /**\\n     * @dev Sets `amount` as the allowance of `spender` over the caller's tokens.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * IMPORTANT: Beware that changing an allowance with this method brings the risk\\n     * that someone may use both the old and the new allowance by unfortunate\\n     * transaction ordering. One possible solution to mitigate this race\\n     * condition is to first reduce the spender's allowance to 0 and set the\\n     * desired value afterwards:\\n     * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\\n     *\\n     * Emits an {Approval} event.\\n     */\\n    function approve(address spender, uint256 amount) external returns (bool);\\n\\n    /**\\n     * @dev Moves `amount` tokens from `sender` to `recipient` using the\\n     * allowance mechanism. `amount` is then deducted from the caller's\\n     * allowance.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transferFrom(address sender, address recipient, uint256 amount) external returns (bool);\\n\\n    /**\\n     * @dev Emitted when `value` tokens are moved from one account (`from`) to\\n     * another (`to`).\\n     *\\n     * Note that `value` may be zero.\\n     */\\n    event Transfer(address indexed from, address indexed to, uint256 value);\\n\\n    /**\\n     * @dev Emitted when the allowance of a `spender` for an `owner` is set by\\n     * a call to {approve}. `value` is the new allowance.\\n     */\\n    event Approval(address indexed owner, address indexed spender, uint256 value);\\n}\\n\",\"keccak256\":\"0xe5bb0f57cff3e299f360052ba50f1ea0fff046df2be070b6943e0e3c3fdad8a9\"},\"@openzeppelin/contracts/utils/Address.sol\":{\"content\":\"pragma solidity ^0.5.5;\\n\\n/**\\n * @dev Collection of functions related to the address type\\n */\\nlibrary Address {\\n    /**\\n     * @dev Returns true if `account` is a contract.\\n     *\\n     * [IMPORTANT]\\n     * ====\\n     * It is unsafe to assume that an address for which this function returns\\n     * false is an externally-owned account (EOA) and not a contract.\\n     *\\n     * Among others, `isContract` will return false for the following \\n     * types of addresses:\\n     *\\n     *  - an externally-owned account\\n     *  - a contract in construction\\n     *  - an address where a contract will be created\\n     *  - an address where a contract lived, but was destroyed\\n     * ====\\n     */\\n    function isContract(address account) internal view returns (bool) {\\n        // According to EIP-1052, 0x0 is the value returned for not-yet created accounts\\n        // and 0xc5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470 is returned\\n        // for accounts without code, i.e. `keccak256('')`\\n        bytes32 codehash;\\n        bytes32 accountHash = 0xc5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470;\\n        // solhint-disable-next-line no-inline-assembly\\n        assembly { codehash := extcodehash(account) }\\n        return (codehash != accountHash && codehash != 0x0);\\n    }\\n\\n    /**\\n     * @dev Converts an `address` into `address payable`. Note that this is\\n     * simply a type cast: the actual underlying value is not changed.\\n     *\\n     * _Available since v2.4.0._\\n     */\\n    function toPayable(address account) internal pure returns (address payable) {\\n        return address(uint160(account));\\n    }\\n\\n    /**\\n     * @dev Replacement for Solidity's `transfer`: sends `amount` wei to\\n     * `recipient`, forwarding all available gas and reverting on errors.\\n     *\\n     * https://eips.ethereum.org/EIPS/eip-1884[EIP1884] increases the gas cost\\n     * of certain opcodes, possibly making contracts go over the 2300 gas limit\\n     * imposed by `transfer`, making them unable to receive funds via\\n     * `transfer`. {sendValue} removes this limitation.\\n     *\\n     * https://diligence.consensys.net/posts/2019/09/stop-using-soliditys-transfer-now/[Learn more].\\n     *\\n     * IMPORTANT: because control is transferred to `recipient`, care must be\\n     * taken to not create reentrancy vulnerabilities. Consider using\\n     * {ReentrancyGuard} or the\\n     * https://solidity.readthedocs.io/en/v0.5.11/security-considerations.html#use-the-checks-effects-interactions-pattern[checks-effects-interactions pattern].\\n     *\\n     * _Available since v2.4.0._\\n     */\\n    function sendValue(address payable recipient, uint256 amount) internal {\\n        require(address(this).balance >= amount, \\\"Address: insufficient balance\\\");\\n\\n        // solhint-disable-next-line avoid-call-value\\n        (bool success, ) = recipient.call.value(amount)(\\\"\\\");\\n        require(success, \\\"Address: unable to send value, recipient may have reverted\\\");\\n    }\\n}\\n\",\"keccak256\":\"0x1a8e5072509c5ea7365eb1d48030b9be865140c8fb779968da0a459a0e174a11\"},\"@openzeppelin/upgrades/contracts/Initializable.sol\":{\"content\":\"pragma solidity >=0.4.24 <0.7.0;\\n\\n\\n/**\\n * @title Initializable\\n *\\n * @dev Helper contract to support initializer functions. To use it, replace\\n * the constructor with a function that has the `initializer` modifier.\\n * WARNING: Unlike constructors, initializer functions must be manually\\n * invoked. This applies both to deploying an Initializable contract, as well\\n * as extending an Initializable contract via inheritance.\\n * WARNING: When used with inheritance, manual care must be taken to not invoke\\n * a parent initializer twice, or ensure that all initializers are idempotent,\\n * because this is not dealt with automatically as with constructors.\\n */\\ncontract Initializable {\\n\\n  /**\\n   * @dev Indicates that the contract has been initialized.\\n   */\\n  bool private initialized;\\n\\n  /**\\n   * @dev Indicates that the contract is in the process of being initialized.\\n   */\\n  bool private initializing;\\n\\n  /**\\n   * @dev Modifier to use in the initializer function of a contract.\\n   */\\n  modifier initializer() {\\n    require(initializing || isConstructor() || !initialized, \\\"Contract instance has already been initialized\\\");\\n\\n    bool isTopLevelCall = !initializing;\\n    if (isTopLevelCall) {\\n      initializing = true;\\n      initialized = true;\\n    }\\n\\n    _;\\n\\n    if (isTopLevelCall) {\\n      initializing = false;\\n    }\\n  }\\n\\n  /// @dev Returns true if and only if the function is running in the constructor\\n  function isConstructor() private view returns (bool) {\\n    // extcodesize checks the size of the code stored in an address, and\\n    // address returns the current address. Since the code is still not\\n    // deployed when running a constructor, any checks on its code size will\\n    // yield zero, making it an effective way to detect if a contract is\\n    // under construction or not.\\n    address self = address(this);\\n    uint256 cs;\\n    assembly { cs := extcodesize(self) }\\n    return cs == 0;\\n  }\\n\\n  // Reserved storage space to allow for layout changes in the future.\\n  uint256[50] private ______gap;\\n}\\n\",\"keccak256\":\"0x9bfec92e36234ecc99b5d37230acb6cd1f99560233753162204104a4897e8721\"},\"contracts/governance/Governable.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\n/**\\n * @title OUSD Governable Contract\\n * @dev Copy of the openzeppelin Ownable.sol contract with nomenclature change\\n *      from owner to governor and renounce methods removed. Does not use\\n *      Context.sol like Ownable.sol does for simplification.\\n * <AUTHOR> Protocol Inc\\n */\\ncontract Governable {\\n    // Storage position of the owner and pendingOwner of the contract\\n    // keccak256(\\\"OUSD.governor\\\");\\n    bytes32\\n        private constant governorPosition = 0x7bea13895fa79d2831e0a9e28edede30099005a50d652d8957cf8a607ee6ca4a;\\n\\n    // keccak256(\\\"OUSD.pending.governor\\\");\\n    bytes32\\n        private constant pendingGovernorPosition = 0x44c4d30b2eaad5130ad70c3ba6972730566f3e6359ab83e800d905c61b1c51db;\\n\\n    // keccak256(\\\"OUSD.reentry.status\\\");\\n    bytes32\\n        private constant reentryStatusPosition = 0x53bf423e48ed90e97d02ab0ebab13b2a235a6bfbe9c321847d5c175333ac4535;\\n\\n    // See OpenZeppelin ReentrancyGuard implementation\\n    uint256 constant _NOT_ENTERED = 1;\\n    uint256 constant _ENTERED = 2;\\n\\n    event PendingGovernorshipTransfer(\\n        address indexed previousGovernor,\\n        address indexed newGovernor\\n    );\\n\\n    event GovernorshipTransferred(\\n        address indexed previousGovernor,\\n        address indexed newGovernor\\n    );\\n\\n    /**\\n     * @dev Initializes the contract setting the deployer as the initial Governor.\\n     */\\n    constructor() internal {\\n        _setGovernor(msg.sender);\\n        emit GovernorshipTransferred(address(0), _governor());\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current Governor.\\n     */\\n    function governor() public view returns (address) {\\n        return _governor();\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current Governor.\\n     */\\n    function _governor() internal view returns (address governorOut) {\\n        bytes32 position = governorPosition;\\n        assembly {\\n            governorOut := sload(position)\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the address of the pending Governor.\\n     */\\n    function _pendingGovernor()\\n        internal\\n        view\\n        returns (address pendingGovernor)\\n    {\\n        bytes32 position = pendingGovernorPosition;\\n        assembly {\\n            pendingGovernor := sload(position)\\n        }\\n    }\\n\\n    /**\\n     * @dev Throws if called by any account other than the Governor.\\n     */\\n    modifier onlyGovernor() {\\n        require(isGovernor(), \\\"Caller is not the Governor\\\");\\n        _;\\n    }\\n\\n    /**\\n     * @dev Returns true if the caller is the current Governor.\\n     */\\n    function isGovernor() public view returns (bool) {\\n        return msg.sender == _governor();\\n    }\\n\\n    function _setGovernor(address newGovernor) internal {\\n        bytes32 position = governorPosition;\\n        assembly {\\n            sstore(position, newGovernor)\\n        }\\n    }\\n\\n    /**\\n     * @dev Prevents a contract from calling itself, directly or indirectly.\\n     * Calling a `nonReentrant` function from another `nonReentrant`\\n     * function is not supported. It is possible to prevent this from happening\\n     * by making the `nonReentrant` function external, and make it call a\\n     * `private` function that does the actual work.\\n     */\\n    modifier nonReentrant() {\\n        bytes32 position = reentryStatusPosition;\\n        uint256 _reentry_status;\\n        assembly {\\n            _reentry_status := sload(position)\\n        }\\n\\n        // On the first call to nonReentrant, _notEntered will be true\\n        require(_reentry_status != _ENTERED, \\\"Reentrant call\\\");\\n\\n        // Any calls to nonReentrant after this point will fail\\n        assembly {\\n            sstore(position, _ENTERED)\\n        }\\n\\n        _;\\n\\n        // By storing the original value once again, a refund is triggered (see\\n        // https://eips.ethereum.org/EIPS/eip-2200)\\n        assembly {\\n            sstore(position, _NOT_ENTERED)\\n        }\\n    }\\n\\n    function _setPendingGovernor(address newGovernor) internal {\\n        bytes32 position = pendingGovernorPosition;\\n        assembly {\\n            sstore(position, newGovernor)\\n        }\\n    }\\n\\n    /**\\n     * @dev Transfers Governance of the contract to a new account (`newGovernor`).\\n     * Can only be called by the current Governor. Must be claimed for this to complete\\n     * @param _newGovernor Address of the new Governor\\n     */\\n    function transferGovernance(address _newGovernor) external onlyGovernor {\\n        _setPendingGovernor(_newGovernor);\\n        emit PendingGovernorshipTransfer(_governor(), _newGovernor);\\n    }\\n\\n    /**\\n     * @dev Claim Governance of the contract to a new account (`newGovernor`).\\n     * Can only be called by the new Governor.\\n     */\\n    function claimGovernance() external {\\n        require(\\n            msg.sender == _pendingGovernor(),\\n            \\\"Only the pending Governor can complete the claim\\\"\\n        );\\n        _changeGovernor(msg.sender);\\n    }\\n\\n    /**\\n     * @dev Change Governance of the contract to a new account (`newGovernor`).\\n     * @param _newGovernor Address of the new Governor\\n     */\\n    function _changeGovernor(address _newGovernor) internal {\\n        require(_newGovernor != address(0), \\\"New Governor is address(0)\\\");\\n        emit GovernorshipTransferred(_governor(), _newGovernor);\\n        _setGovernor(_newGovernor);\\n    }\\n}\\n\",\"keccak256\":\"0x3e51ea48102945bf4b305bf9722a07514a585a29555d92f8c84352d1a4cfcee1\"},\"contracts/token/OUSD.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\n/**\\n * @title OUSD Token Contract\\n * @dev ERC20 compatible contract for OUSD\\n * @dev Implements an elastic supply\\n * <AUTHOR> Protocol Inc\\n */\\nimport { SafeMath } from \\\"@openzeppelin/contracts/math/SafeMath.sol\\\";\\nimport {\\n    Initializable\\n} from \\\"@openzeppelin/upgrades/contracts/Initializable.sol\\\";\\nimport { Address } from \\\"@openzeppelin/contracts/utils/Address.sol\\\";\\n\\nimport {\\n    InitializableERC20Detailed\\n} from \\\"../utils/InitializableERC20Detailed.sol\\\";\\nimport { StableMath } from \\\"../utils/StableMath.sol\\\";\\nimport { Governable } from \\\"../governance/Governable.sol\\\";\\n\\n/**\\n * NOTE that this is an ERC20 token but the invariant that the sum of\\n * balanceOf(x) for all x is not >= totalSupply(). This is a consequence of the\\n * rebasing design. Any integrations with OUSD should be aware.\\n */\\n\\ncontract OUSD is Initializable, InitializableERC20Detailed, Governable {\\n    using SafeMath for uint256;\\n    using StableMath for uint256;\\n\\n    event TotalSupplyUpdated(\\n        uint256 totalSupply,\\n        uint256 rebasingCredits,\\n        uint256 rebasingCreditsPerToken\\n    );\\n\\n    enum RebaseOptions { NotSet, OptOut, OptIn }\\n\\n    uint256 private constant MAX_SUPPLY = ~uint128(0); // (2^128) - 1\\n    uint256 public _totalSupply;\\n    mapping(address => mapping(address => uint256)) private _allowances;\\n    address public vaultAddress = address(0);\\n    mapping(address => uint256) private _creditBalances;\\n    uint256 public rebasingCredits;\\n    uint256 public rebasingCreditsPerToken;\\n    // Frozen address/credits are non rebasing (value is held in contracts which\\n    // do not receive yield unless they explicitly opt in)\\n    uint256 public nonRebasingSupply;\\n    mapping(address => uint256) public nonRebasingCreditsPerToken;\\n    mapping(address => RebaseOptions) public rebaseState;\\n\\n    function initialize(\\n        string calldata _nameArg,\\n        string calldata _symbolArg,\\n        address _vaultAddress\\n    ) external onlyGovernor initializer {\\n        InitializableERC20Detailed._initialize(_nameArg, _symbolArg, 18);\\n        rebasingCreditsPerToken = 1e18;\\n        vaultAddress = _vaultAddress;\\n    }\\n\\n    /**\\n     * @dev Verifies that the caller is the Savings Manager contract\\n     */\\n    modifier onlyVault() {\\n        require(vaultAddress == msg.sender, \\\"Caller is not the Vault\\\");\\n        _;\\n    }\\n\\n    /**\\n     * @return The total supply of OUSD.\\n     */\\n    function totalSupply() public view returns (uint256) {\\n        return _totalSupply;\\n    }\\n\\n    /**\\n     * @dev Gets the balance of the specified address.\\n     * @param _account Address to query the balance of.\\n     * @return A uint256 representing the _amount of base units owned by the\\n     *         specified address.\\n     */\\n    function balanceOf(address _account) public view returns (uint256) {\\n        if (_creditBalances[_account] == 0) return 0;\\n        return\\n            _creditBalances[_account].divPrecisely(_creditsPerToken(_account));\\n    }\\n\\n    /**\\n     * @dev Gets the credits balance of the specified address.\\n     * @param _account The address to query the balance of.\\n     * @return (uint256, uint256) Credit balance and credits per token of the\\n     *         address\\n     */\\n    function creditsBalanceOf(address _account)\\n        public\\n        view\\n        returns (uint256, uint256)\\n    {\\n        return (_creditBalances[_account], _creditsPerToken(_account));\\n    }\\n\\n    /**\\n     * @dev Transfer tokens to a specified address.\\n     * @param _to the address to transfer to.\\n     * @param _value the _amount to be transferred.\\n     * @return true on success.\\n     */\\n    function transfer(address _to, uint256 _value) public returns (bool) {\\n        require(_to != address(0), \\\"Transfer to zero address\\\");\\n        require(\\n            _value <= balanceOf(msg.sender),\\n            \\\"Transfer greater than balance\\\"\\n        );\\n\\n        _executeTransfer(msg.sender, _to, _value);\\n\\n        emit Transfer(msg.sender, _to, _value);\\n\\n        return true;\\n    }\\n\\n    /**\\n     * @dev Transfer tokens from one address to another.\\n     * @param _from The address you want to send tokens from.\\n     * @param _to The address you want to transfer to.\\n     * @param _value The _amount of tokens to be transferred.\\n     */\\n    function transferFrom(\\n        address _from,\\n        address _to,\\n        uint256 _value\\n    ) public returns (bool) {\\n        require(_to != address(0), \\\"Transfer to zero address\\\");\\n        require(_value <= balanceOf(_from), \\\"Transfer greater than balance\\\");\\n\\n        _allowances[_from][msg.sender] = _allowances[_from][msg.sender].sub(\\n            _value\\n        );\\n\\n        _executeTransfer(_from, _to, _value);\\n\\n        emit Transfer(_from, _to, _value);\\n\\n        return true;\\n    }\\n\\n    /**\\n     * @dev Update the count of non rebasing credits in response to a transfer\\n     * @param _from The address you want to send tokens from.\\n     * @param _to The address you want to transfer to.\\n     * @param _value Amount of OUSD to transfer\\n     */\\n    function _executeTransfer(\\n        address _from,\\n        address _to,\\n        uint256 _value\\n    ) internal {\\n        bool isNonRebasingTo = _isNonRebasingAccount(_to);\\n        bool isNonRebasingFrom = _isNonRebasingAccount(_from);\\n\\n        // Credits deducted and credited might be different due to the\\n        // differing creditsPerToken used by each account\\n        uint256 creditsCredited = _value.mulTruncate(_creditsPerToken(_to));\\n        uint256 creditsDeducted = _value.mulTruncate(_creditsPerToken(_from));\\n\\n        _creditBalances[_from] = _creditBalances[_from].sub(\\n            creditsDeducted,\\n            \\\"Transfer amount exceeds balance\\\"\\n        );\\n        _creditBalances[_to] = _creditBalances[_to].add(creditsCredited);\\n\\n        if (isNonRebasingTo && !isNonRebasingFrom) {\\n            // Transfer to non-rebasing account from rebasing account, credits\\n            // are removed from the non rebasing tally\\n            nonRebasingSupply = nonRebasingSupply.add(_value);\\n            // Update rebasingCredits by subtracting the deducted amount\\n            rebasingCredits = rebasingCredits.sub(creditsDeducted);\\n        } else if (!isNonRebasingTo && isNonRebasingFrom) {\\n            // Transfer to rebasing account from non-rebasing account\\n            // Decreasing non-rebasing credits by the amount that was sent\\n            nonRebasingSupply = nonRebasingSupply.sub(_value);\\n            // Update rebasingCredits by adding the credited amount\\n            rebasingCredits = rebasingCredits.add(creditsCredited);\\n        }\\n    }\\n\\n    /**\\n     * @dev Function to check the _amount of tokens that an owner has allowed to a _spender.\\n     * @param _owner The address which owns the funds.\\n     * @param _spender The address which will spend the funds.\\n     * @return The number of tokens still available for the _spender.\\n     */\\n    function allowance(address _owner, address _spender)\\n        public\\n        view\\n        returns (uint256)\\n    {\\n        return _allowances[_owner][_spender];\\n    }\\n\\n    /**\\n     * @dev Approve the passed address to spend the specified _amount of tokens on behalf of\\n     * msg.sender. This method is included for ERC20 compatibility.\\n     * increaseAllowance and decreaseAllowance should be used instead.\\n     * Changing an allowance with this method brings the risk that someone may transfer both\\n     * the old and the new allowance - if they are both greater than zero - if a transfer\\n     * transaction is mined before the later approve() call is mined.\\n     *\\n     * @param _spender The address which will spend the funds.\\n     * @param _value The _amount of tokens to be spent.\\n     */\\n    function approve(address _spender, uint256 _value) public returns (bool) {\\n        _allowances[msg.sender][_spender] = _value;\\n        emit Approval(msg.sender, _spender, _value);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev Increase the _amount of tokens that an owner has allowed to a _spender.\\n     * This method should be used instead of approve() to avoid the double approval vulnerability\\n     * described above.\\n     * @param _spender The address which will spend the funds.\\n     * @param _addedValue The _amount of tokens to increase the allowance by.\\n     */\\n    function increaseAllowance(address _spender, uint256 _addedValue)\\n        public\\n        returns (bool)\\n    {\\n        _allowances[msg.sender][_spender] = _allowances[msg.sender][_spender]\\n            .add(_addedValue);\\n        emit Approval(msg.sender, _spender, _allowances[msg.sender][_spender]);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev Decrease the _amount of tokens that an owner has allowed to a _spender.\\n     * @param _spender The address which will spend the funds.\\n     * @param _subtractedValue The _amount of tokens to decrease the allowance by.\\n     */\\n    function decreaseAllowance(address _spender, uint256 _subtractedValue)\\n        public\\n        returns (bool)\\n    {\\n        uint256 oldValue = _allowances[msg.sender][_spender];\\n        if (_subtractedValue >= oldValue) {\\n            _allowances[msg.sender][_spender] = 0;\\n        } else {\\n            _allowances[msg.sender][_spender] = oldValue.sub(_subtractedValue);\\n        }\\n        emit Approval(msg.sender, _spender, _allowances[msg.sender][_spender]);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev Mints new tokens, increasing totalSupply.\\n     */\\n    function mint(address _account, uint256 _amount) external onlyVault {\\n        return _mint(_account, _amount);\\n    }\\n\\n    /**\\n     * @dev Creates `_amount` tokens and assigns them to `_account`, increasing\\n     * the total supply.\\n     *\\n     * Emits a {Transfer} event with `from` set to the zero address.\\n     *\\n     * Requirements\\n     *\\n     * - `to` cannot be the zero address.\\n     */\\n    function _mint(address _account, uint256 _amount) internal nonReentrant {\\n        require(_account != address(0), \\\"Mint to the zero address\\\");\\n\\n        bool isNonRebasingAccount = _isNonRebasingAccount(_account);\\n\\n        uint256 creditAmount = _amount.mulTruncate(_creditsPerToken(_account));\\n        _creditBalances[_account] = _creditBalances[_account].add(creditAmount);\\n\\n        // If the account is non rebasing and doesn't have a set creditsPerToken\\n        // then set it i.e. this is a mint from a fresh contract\\n        if (isNonRebasingAccount) {\\n            nonRebasingSupply = nonRebasingSupply.add(_amount);\\n        } else {\\n            rebasingCredits = rebasingCredits.add(creditAmount);\\n        }\\n\\n        _totalSupply = _totalSupply.add(_amount);\\n\\n        require(_totalSupply < MAX_SUPPLY, \\\"Max supply\\\");\\n\\n        emit Transfer(address(0), _account, _amount);\\n    }\\n\\n    /**\\n     * @dev Burns tokens, decreasing totalSupply.\\n     */\\n    function burn(address account, uint256 amount) external onlyVault {\\n        return _burn(account, amount);\\n    }\\n\\n    /**\\n     * @dev Destroys `_amount` tokens from `_account`, reducing the\\n     * total supply.\\n     *\\n     * Emits a {Transfer} event with `to` set to the zero address.\\n     *\\n     * Requirements\\n     *\\n     * - `_account` cannot be the zero address.\\n     * - `_account` must have at least `_amount` tokens.\\n     */\\n    function _burn(address _account, uint256 _amount) internal nonReentrant {\\n        require(_account != address(0), \\\"Burn from the zero address\\\");\\n\\n        bool isNonRebasingAccount = _isNonRebasingAccount(_account);\\n        uint256 creditAmount = _amount.mulTruncate(_creditsPerToken(_account));\\n        uint256 currentCredits = _creditBalances[_account];\\n\\n        // Remove the credits, burning rounding errors\\n        if (\\n            currentCredits == creditAmount || currentCredits - 1 == creditAmount\\n        ) {\\n            // Handle dust from rounding\\n            _creditBalances[_account] = 0;\\n        } else if (currentCredits > creditAmount) {\\n            _creditBalances[_account] = _creditBalances[_account].sub(\\n                creditAmount\\n            );\\n        } else {\\n            revert(\\\"Remove exceeds balance\\\");\\n        }\\n\\n        // Remove from the credit tallies and non-rebasing supply\\n        if (isNonRebasingAccount) {\\n            nonRebasingSupply = nonRebasingSupply.sub(_amount);\\n        } else {\\n            rebasingCredits = rebasingCredits.sub(creditAmount);\\n        }\\n\\n        _totalSupply = _totalSupply.sub(_amount);\\n\\n        emit Transfer(_account, address(0), _amount);\\n    }\\n\\n    /**\\n     * @dev Get the credits per token for an account. Returns a fixed amount\\n     *      if the account is non-rebasing.\\n     * @param _account Address of the account.\\n     */\\n    function _creditsPerToken(address _account)\\n        internal\\n        view\\n        returns (uint256)\\n    {\\n        if (nonRebasingCreditsPerToken[_account] != 0) {\\n            return nonRebasingCreditsPerToken[_account];\\n        } else {\\n            return rebasingCreditsPerToken;\\n        }\\n    }\\n\\n    /**\\n     * @dev Is an accounts balance non rebasing, i.e. does not alter with rebases\\n     * @param _account Address of the account.\\n     */\\n    function _isNonRebasingAccount(address _account) internal returns (bool) {\\n        if (Address.isContract(_account)) {\\n            // Contracts by default opt out\\n            if (rebaseState[_account] == RebaseOptions.OptIn) {\\n                // If they've opted in explicitly it is not a non rebasing\\n                // address\\n                return false;\\n            }\\n            // Is a non rebasing account because no explicit opt in\\n            // Make sure the rebasing/non-rebasing supply is updated and\\n            // fixed credits per token is set for this account\\n            _ensureRebasingMigration(_account);\\n            return true;\\n        } else {\\n            // EOAs by default opt in\\n            // Check for explicit opt out\\n            return rebaseState[_account] == RebaseOptions.OptOut;\\n        }\\n    }\\n\\n    /**\\n     * @dev Ensures internal account for rebasing and non-rebasing credits and\\n     *      supply is updated following deployment of frozen yield change.\\n     */\\n    function _ensureRebasingMigration(address _account) internal {\\n        if (nonRebasingCreditsPerToken[_account] == 0) {\\n            // Set fixed credits per token for this account\\n            nonRebasingCreditsPerToken[_account] = rebasingCreditsPerToken;\\n            // Update non rebasing supply\\n            nonRebasingSupply = nonRebasingSupply.add(balanceOf(_account));\\n            // Update credit tallies\\n            rebasingCredits = rebasingCredits.sub(_creditBalances[_account]);\\n        }\\n    }\\n\\n    /**\\n     * @dev Add a contract address to the non rebasing exception list. I.e. the\\n     * address's balance will be part of rebases so the account will be exposed\\n     * to upside and downside.\\n     */\\n    function rebaseOptIn() public nonReentrant {\\n        require(_isNonRebasingAccount(msg.sender), \\\"Account has not opted out\\\");\\n\\n        // Convert balance into the same amount at the current exchange rate\\n        uint256 newCreditBalance = _creditBalances[msg.sender]\\n            .mul(rebasingCreditsPerToken)\\n            .div(_creditsPerToken(msg.sender));\\n\\n        // Decreasing non rebasing supply\\n        nonRebasingSupply = nonRebasingSupply.sub(balanceOf(msg.sender));\\n\\n        _creditBalances[msg.sender] = newCreditBalance;\\n\\n        // Increase rebasing credits, totalSupply remains unchanged so no\\n        // adjustment necessary\\n        rebasingCredits = rebasingCredits.add(_creditBalances[msg.sender]);\\n\\n        rebaseState[msg.sender] = RebaseOptions.OptIn;\\n\\n        // Delete any fixed credits per token\\n        delete nonRebasingCreditsPerToken[msg.sender];\\n    }\\n\\n    /**\\n     * @dev Remove a contract address to the non rebasing exception list.\\n     */\\n    function rebaseOptOut() public nonReentrant {\\n        require(!_isNonRebasingAccount(msg.sender), \\\"Account has not opted in\\\");\\n\\n        // Increase non rebasing supply\\n        nonRebasingSupply = nonRebasingSupply.add(balanceOf(msg.sender));\\n        // Set fixed credits per token\\n        nonRebasingCreditsPerToken[msg.sender] = rebasingCreditsPerToken;\\n\\n        // Decrease rebasing credits, total supply remains unchanged so no\\n        // adjustment necessary\\n        rebasingCredits = rebasingCredits.sub(_creditBalances[msg.sender]);\\n\\n        // Mark explicitly opted out of rebasing\\n        rebaseState[msg.sender] = RebaseOptions.OptOut;\\n    }\\n\\n    /**\\n     * @dev Modify the supply without minting new tokens. This uses a change in\\n     *      the exchange rate between \\\"credits\\\" and OUSD tokens to change balances.\\n     * @param _newTotalSupply New total supply of OUSD.\\n     * @return uint256 representing the new total supply.\\n     */\\n    function changeSupply(uint256 _newTotalSupply)\\n        external\\n        onlyVault\\n        nonReentrant\\n    {\\n        require(_totalSupply > 0, \\\"Cannot increase 0 supply\\\");\\n\\n        if (_totalSupply == _newTotalSupply) {\\n            emit TotalSupplyUpdated(\\n                _totalSupply,\\n                rebasingCredits,\\n                rebasingCreditsPerToken\\n            );\\n            return;\\n        }\\n\\n        _totalSupply = _newTotalSupply > MAX_SUPPLY\\n            ? MAX_SUPPLY\\n            : _newTotalSupply;\\n\\n        rebasingCreditsPerToken = rebasingCredits.divPrecisely(\\n            _totalSupply.sub(nonRebasingSupply)\\n        );\\n\\n        require(rebasingCreditsPerToken > 0, \\\"Invalid change in supply\\\");\\n\\n        _totalSupply = rebasingCredits\\n            .divPrecisely(rebasingCreditsPerToken)\\n            .add(nonRebasingSupply);\\n\\n        emit TotalSupplyUpdated(\\n            _totalSupply,\\n            rebasingCredits,\\n            rebasingCreditsPerToken\\n        );\\n    }\\n}\\n\",\"keccak256\":\"0xa7d771d85f45b629296431dfc8d0bc3d7dc53a78b3321f7fe572e8fac483e22d\"},\"contracts/utils/InitializableERC20Detailed.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\nimport { IERC20 } from \\\"@openzeppelin/contracts/token/ERC20/IERC20.sol\\\";\\n\\n/**\\n * @dev Optional functions from the ERC20 standard.\\n * Converted from openzeppelin/contracts/token/ERC20/ERC20Detailed.sol\\n */\\ncontract InitializableERC20Detailed is IERC20 {\\n    // Storage gap to skip storage from prior to OUSD reset\\n    uint256[100] private _____gap;\\n\\n    string public _name;\\n    string public _symbol;\\n    uint8 public _decimals;\\n\\n    /**\\n     * @dev Sets the values for `name`, `symbol`, and `decimals`. All three of\\n     * these values are immutable: they can only be set once during\\n     * construction.\\n     * @notice To avoid variable shadowing appended `Arg` after arguments name.\\n     */\\n    function _initialize(\\n        string memory nameArg,\\n        string memory symbolArg,\\n        uint8 decimalsArg\\n    ) internal {\\n        _name = nameArg;\\n        _symbol = symbolArg;\\n        _decimals = decimalsArg;\\n    }\\n\\n    /**\\n     * @dev Returns the name of the token.\\n     */\\n    function name() public view returns (string memory) {\\n        return _name;\\n    }\\n\\n    /**\\n     * @dev Returns the symbol of the token, usually a shorter version of the\\n     * name.\\n     */\\n    function symbol() public view returns (string memory) {\\n        return _symbol;\\n    }\\n\\n    /**\\n     * @dev Returns the number of decimals used to get its user representation.\\n     * For example, if `decimals` equals `2`, a balance of `505` tokens should\\n     * be displayed to a user as `5,05` (`505 / 10 ** 2`).\\n     *\\n     * Tokens usually opt for a value of 18, imitating the relationship between\\n     * Ether and Wei.\\n     *\\n     * NOTE: This information is only used for _display_ purposes: it in\\n     * no way affects any of the arithmetic of the contract, including\\n     * {IERC20-balanceOf} and {IERC20-transfer}.\\n     */\\n    function decimals() public view returns (uint8) {\\n        return _decimals;\\n    }\\n}\\n\",\"keccak256\":\"0x11296d772a6ff62d818793a3ea312f21dbeb4c0632020e2aebb53920fe571830\"},\"contracts/utils/StableMath.sol\":{\"content\":\"pragma solidity 0.5.11;\\n\\nimport { SafeMath } from \\\"@openzeppelin/contracts/math/SafeMath.sol\\\";\\n\\n// Based on StableMath from Stability Labs Pty. Ltd.\\n// https://github.com/mstable/mStable-contracts/blob/master/contracts/shared/StableMath.sol\\n\\nlibrary StableMath {\\n    using SafeMath for uint256;\\n\\n    /**\\n     * @dev Scaling unit for use in specific calculations,\\n     * where 1 * 10**18, or 1e18 represents a unit '1'\\n     */\\n    uint256 private constant FULL_SCALE = 1e18;\\n\\n    /***************************************\\n                    Helpers\\n    ****************************************/\\n\\n    /**\\n     * @dev Adjust the scale of an integer\\n     * @param adjustment Amount to adjust by e.g. scaleBy(1e18, -1) == 1e17\\n     */\\n    function scaleBy(uint256 x, int8 adjustment)\\n        internal\\n        pure\\n        returns (uint256)\\n    {\\n        if (adjustment > 0) {\\n            x = x.mul(10**uint256(adjustment));\\n        } else if (adjustment < 0) {\\n            x = x.div(10**uint256(adjustment * -1));\\n        }\\n        return x;\\n    }\\n\\n    /***************************************\\n               Precise Arithmetic\\n    ****************************************/\\n\\n    /**\\n     * @dev Multiplies two precise units, and then truncates by the full scale\\n     * @param x Left hand input to multiplication\\n     * @param y Right hand input to multiplication\\n     * @return Result after multiplying the two inputs and then dividing by the shared\\n     *         scale unit\\n     */\\n    function mulTruncate(uint256 x, uint256 y) internal pure returns (uint256) {\\n        return mulTruncateScale(x, y, FULL_SCALE);\\n    }\\n\\n    /**\\n     * @dev Multiplies two precise units, and then truncates by the given scale. For example,\\n     * when calculating 90% of 10e18, (10e18 * 9e17) / 1e18 = (9e36) / 1e18 = 9e18\\n     * @param x Left hand input to multiplication\\n     * @param y Right hand input to multiplication\\n     * @param scale Scale unit\\n     * @return Result after multiplying the two inputs and then dividing by the shared\\n     *         scale unit\\n     */\\n    function mulTruncateScale(\\n        uint256 x,\\n        uint256 y,\\n        uint256 scale\\n    ) internal pure returns (uint256) {\\n        // e.g. assume scale = fullScale\\n        // z = 10e18 * 9e17 = 9e36\\n        uint256 z = x.mul(y);\\n        // return 9e38 / 1e18 = 9e18\\n        return z.div(scale);\\n    }\\n\\n    /**\\n     * @dev Multiplies two precise units, and then truncates by the full scale, rounding up the result\\n     * @param x Left hand input to multiplication\\n     * @param y Right hand input to multiplication\\n     * @return Result after multiplying the two inputs and then dividing by the shared\\n     *          scale unit, rounded up to the closest base unit.\\n     */\\n    function mulTruncateCeil(uint256 x, uint256 y)\\n        internal\\n        pure\\n        returns (uint256)\\n    {\\n        // e.g. 8e17 * 17268172638 = 138145381104e17\\n        uint256 scaled = x.mul(y);\\n        // e.g. 138145381104e17 + 9.99...e17 = 138145381113.99...e17\\n        uint256 ceil = scaled.add(FULL_SCALE.sub(1));\\n        // e.g. 13814538111.399...e18 / 1e18 = 13814538111\\n        return ceil.div(FULL_SCALE);\\n    }\\n\\n    /**\\n     * @dev Precisely divides two units, by first scaling the left hand operand. Useful\\n     *      for finding percentage weightings, i.e. 8e18/10e18 = 80% (or 8e17)\\n     * @param x Left hand input to division\\n     * @param y Right hand input to division\\n     * @return Result after multiplying the left operand by the scale, and\\n     *         executing the division on the right hand input.\\n     */\\n    function divPrecisely(uint256 x, uint256 y)\\n        internal\\n        pure\\n        returns (uint256)\\n    {\\n        // e.g. 8e18 * 1e18 = 8e36\\n        uint256 z = x.mul(FULL_SCALE);\\n        // e.g. 8e36 / 10e18 = 8e17\\n        return z.div(y);\\n    }\\n}\\n\",\"keccak256\":\"0xa77fccf850feb6d54ba3a6530f92554caef8a67a1ceb573d4f8a5d1bf64ff9d2\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"methods": {"allowance(address,address)": {"details": "Function to check the _amount of tokens that an owner has allowed to a _spender.", "params": {"_owner": "The address which owns the funds.", "_spender": "The address which will spend the funds."}, "return": "The number of tokens still available for the _spender."}, "approve(address,uint256)": {"details": "Approve the passed address to spend the specified _amount of tokens on behalf of msg.sender. This method is included for ERC20 compatibility. increaseAllowance and decreaseAllowance should be used instead. Changing an allowance with this method brings the risk that someone may transfer both the old and the new allowance - if they are both greater than zero - if a transfer transaction is mined before the later approve() call is mined.", "params": {"_spender": "The address which will spend the funds.", "_value": "The _amount of tokens to be spent."}}, "balanceOf(address)": {"details": "Gets the balance of the specified address.", "params": {"_account": "Address to query the balance of."}, "return": "A uint256 representing the _amount of base units owned by the        specified address."}, "burn(address,uint256)": {"details": "Burns tokens, decreasing totalSupply."}, "changeSupply(uint256)": {"details": "Modify the supply without minting new tokens. This uses a change in     the exchange rate between \"credits\" and OUSD tokens to change balances.", "params": {"_newTotalSupply": "New total supply of OUSD."}, "return": "uint256 representing the new total supply."}, "claimGovernance()": {"details": "Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor."}, "creditsBalanceOf(address)": {"details": "Gets the credits balance of the specified address.", "params": {"_account": "The address to query the balance of."}, "return": "(uint256, uint256) Credit balance and credits per token of the        address"}, "decimals()": {"details": "Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5,05` (`505 / 10 ** 2`).     * Tokens usually opt for a value of 18, imitating the relationship between <PERSON><PERSON> and <PERSON>.     * NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}."}, "decreaseAllowance(address,uint256)": {"details": "Decrease the _amount of tokens that an owner has allowed to a _spender.", "params": {"_spender": "The address which will spend the funds.", "_subtractedValue": "The _amount of tokens to decrease the allowance by."}}, "governor()": {"details": "Returns the address of the current Governor."}, "increaseAllowance(address,uint256)": {"details": "Increase the _amount of tokens that an owner has allowed to a _spender. This method should be used instead of approve() to avoid the double approval vulnerability described above.", "params": {"_addedValue": "The _amount of tokens to increase the allowance by.", "_spender": "The address which will spend the funds."}}, "isGovernor()": {"details": "Returns true if the caller is the current Governor."}, "mint(address,uint256)": {"details": "Mints new tokens, increasing totalSupply."}, "name()": {"details": "Returns the name of the token."}, "rebaseOptIn()": {"details": "Add a contract address to the non rebasing exception list. I.e. the address's balance will be part of rebases so the account will be exposed to upside and downside."}, "rebaseOptOut()": {"details": "Remove a contract address to the non rebasing exception list."}, "symbol()": {"details": "Returns the symbol of the token, usually a shorter version of the name."}, "totalSupply()": {"return": "The total supply of OUSD."}, "transfer(address,uint256)": {"details": "Transfer tokens to a specified address.", "params": {"_to": "the address to transfer to.", "_value": "the _amount to be transferred."}, "return": "true on success."}, "transferFrom(address,address,uint256)": {"details": "Transfer tokens from one address to another.", "params": {"_from": "The address you want to send tokens from.", "_to": "The address you want to transfer to.", "_value": "The _amount of tokens to be transferred."}}, "transferGovernance(address)": {"details": "Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete", "params": {"_newGovernor": "Address of the new Governor"}}}}, "userdoc": {"methods": {}, "notice": "NOTE that this is an ERC20 token but the invariant that the sum of balanceOf(x) for all x is not >= totalSupply(). This is a consequence of the rebasing design. Any integrations with OUSD should be aware."}}