[{"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "priorTimestamp", "type": "uint64"}, {"indexed": false, "internalType": "uint256", "name": "messageTimestamp", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "blockTimestamp", "type": "uint256"}], "name": "Not<PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "source", "type": "address"}, {"indexed": false, "internalType": "string", "name": "key", "type": "string"}, {"indexed": false, "internalType": "uint64", "name": "timestamp", "type": "uint64"}, {"indexed": false, "internalType": "uint64", "name": "value", "type": "uint64"}], "name": "Write", "type": "event"}, {"inputs": [{"internalType": "address", "name": "source", "type": "address"}, {"internalType": "string", "name": "key", "type": "string"}], "name": "get", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}, {"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "source", "type": "address"}, {"internalType": "string", "name": "key", "type": "string"}], "name": "getPrice", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "message", "type": "bytes"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "name": "put", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "message", "type": "bytes"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "name": "source", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "pure", "type": "function"}]