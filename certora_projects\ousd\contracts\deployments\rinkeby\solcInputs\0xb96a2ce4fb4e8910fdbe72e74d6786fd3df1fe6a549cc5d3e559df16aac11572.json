{"language": "Solidity", "sources": {"contracts/governance/Governable.sol": {"content": "pragma solidity 0.5.11;\n\n/**\n * @title OUSD Governable Contract\n * @dev Copy of the openzeppelin Ownable.sol contract with nomenclature change\n *      from owner to governor and renounce methods removed. Does not use\n *      Context.sol like Ownable.sol does for simplification.\n * <AUTHOR> Protocol Inc\n */\ncontract Governable {\n    // Storage position of the owner and pendingOwner of the contract\n    bytes32\n        private constant governorPosition = 0x7bea13895fa79d2831e0a9e28edede30099005a50d652d8957cf8a607ee6ca4a;\n    //keccak256(\"OUSD.governor\");\n\n    bytes32\n        private constant pendingGovernorPosition = 0x44c4d30b2eaad5130ad70c3ba6972730566f3e6359ab83e800d905c61b1c51db;\n    //keccak256(\"OUSD.pending.governor\");\n\n    event PendingGovernorshipTransfer(\n        address indexed previousGovernor,\n        address indexed newGovernor\n    );\n\n    event GovernorshipTransferred(\n        address indexed previousGovernor,\n        address indexed newGovernor\n    );\n\n    /**\n     * @dev Initializes the contract setting the deployer as the initial Governor.\n     */\n    constructor() internal {\n        _setGovernor(msg.sender);\n        emit GovernorshipTransferred(address(0), _governor());\n    }\n\n    /**\n     * @dev Returns the address of the current Governor.\n     */\n    function governor() public view returns (address) {\n        return _governor();\n    }\n\n    function _governor() internal view returns (address governorOut) {\n        bytes32 position = governorPosition;\n        assembly {\n            governorOut := sload(position)\n        }\n    }\n\n    function _pendingGovernor()\n        internal\n        view\n        returns (address pendingGovernor)\n    {\n        bytes32 position = pendingGovernorPosition;\n        assembly {\n            pendingGovernor := sload(position)\n        }\n    }\n\n    /**\n     * @dev Throws if called by any account other than the Governor.\n     */\n    modifier onlyGovernor() {\n        require(isGovernor(), \"Caller is not the Governor\");\n        _;\n    }\n\n    /**\n     * @dev Returns true if the caller is the current Governor.\n     */\n    function isGovernor() public view returns (bool) {\n        return msg.sender == _governor();\n    }\n\n    function _setGovernor(address newGovernor) internal {\n        bytes32 position = governorPosition;\n        assembly {\n            sstore(position, newGovernor)\n        }\n    }\n\n    function _setPendingGovernor(address newGovernor) internal {\n        bytes32 position = pendingGovernorPosition;\n        assembly {\n            sstore(position, newGovernor)\n        }\n    }\n\n    /**\n     * @dev Transfers Governance of the contract to a new account (`newGovernor`).\n     * Can only be called by the current Governor. Must be claimed for this to complete\n     * @param _newGovernor Address of the new Governor\n     */\n    function transferGovernance(address _newGovernor) external onlyGovernor {\n        _setPendingGovernor(_newGovernor);\n        emit PendingGovernorshipTransfer(_governor(), _newGovernor);\n    }\n\n    /**\n     * @dev Claim Governance of the contract to a new account (`newGovernor`).\n     * Can only be called by the new Governor.\n     */\n    function claimGovernance() external {\n        require(\n            msg.sender == _pendingGovernor(),\n            \"Only the pending Governor can complete the claim\"\n        );\n        _changeGovernor(msg.sender);\n    }\n\n    /**\n     * @dev Change Governance of the contract to a new account (`newGovernor`).\n     * @param _newGovernor Address of the new Governor\n     */\n    function _changeGovernor(address _newGovernor) internal {\n        require(_newGovernor != address(0), \"New Governor is address(0)\");\n        emit GovernorshipTransferred(_governor(), _newGovernor);\n        _setGovernor(_newGovernor);\n    }\n}\n"}, "contracts/governance/Governor.sol": {"content": "pragma solidity ^0.5.11;\npragma experimental ABIEncoderV2;\n\nimport \"../interfaces/ITimelock.sol\";\n\n// Modeled off of Compound's Governor Alpha\n//    https://github.com/compound-finance/compound-protocol/blob/master/contracts/Governance/GovernorAlpha.sol\ncontract Governor {\n    /// @notice The address of the  Timelock\n    ITimelock public timelock;\n\n    /// @notice The address of the Governor Guardian\n    address public guardian;\n\n    /// @notice The total number of proposals\n    uint256 public proposalCount;\n\n    struct Proposal {\n        /// @notice Unique id for looking up a proposal\n        uint256 id;\n        /// @notice Creator of the proposal\n        address proposer;\n        /// @notice The timestamp that the proposal will be available for execution, set once the vote succeeds\n        uint256 eta;\n        /// @notice the ordered list of target addresses for calls to be made\n        address[] targets;\n        /// @notice The ordered list of values (i.e. msg.value) to be passed to the calls to be made\n        uint256[] values;\n        /// @notice The ordered list of function signatures to be called\n        string[] signatures;\n        /// @notice The ordered list of calldata to be passed to each call\n        bytes[] calldatas;\n        /// @notice Flag marking whether the proposal has been executed\n        bool executed;\n    }\n\n    /// @notice The official record of all proposals ever proposed\n    mapping(uint256 => Proposal) public proposals;\n\n    /// @notice An event emitted when a new proposal is created\n    event ProposalCreated(\n        uint256 id,\n        address proposer,\n        address[] targets,\n        uint256[] values,\n        string[] signatures,\n        bytes[] calldatas,\n        string description\n    );\n\n    /// @notice An event emitted when a proposal has been queued in the Timelock\n    event ProposalQueued(uint256 id, uint256 eta);\n\n    /// @notice An event emitted when a proposal has been executed in the Timelock\n    event ProposalExecuted(uint256 id);\n\n    uint256 public constant MAX_OPERATIONS = 16;\n\n    /// @notice Possible states that a proposal may be in\n    enum ProposalState { Pending, Queued, Expired, Executed }\n\n    constructor(address timelock_, address guardian_) public {\n        timelock = ITimelock(timelock_);\n        guardian = guardian_;\n    }\n\n    function propose(\n        address[] memory targets,\n        uint256[] memory values,\n        string[] memory signatures,\n        bytes[] memory calldatas,\n        string memory description\n    ) public returns (uint256) {\n        // allow anyone to propose for now, since only guardian can queue the transaction it should be harmless, you just need to pay the gas\n        require(\n            targets.length == values.length &&\n                targets.length == signatures.length &&\n                targets.length == calldatas.length,\n            \"Governor::propose: proposal function information arity mismatch\"\n        );\n        require(targets.length != 0, \"Governor::propose: must provide actions\");\n        require(\n            targets.length <= MAX_OPERATIONS,\n            \"Governor::propose: too many actions\"\n        );\n\n        proposalCount++;\n        Proposal memory newProposal = Proposal({\n            id: proposalCount,\n            proposer: msg.sender,\n            eta: 0,\n            targets: targets,\n            values: values,\n            signatures: signatures,\n            calldatas: calldatas,\n            executed: false\n        });\n\n        proposals[newProposal.id] = newProposal;\n\n        emit ProposalCreated(\n            newProposal.id,\n            msg.sender,\n            targets,\n            values,\n            signatures,\n            calldatas,\n            description\n        );\n        return newProposal.id;\n    }\n\n    function queue(uint256 proposalId) public {\n        require(\n            msg.sender == guardian,\n            \"Governor::queue: sender must be gov guardian\"\n        );\n        require(\n            state(proposalId) == ProposalState.Pending,\n            \"Governor::queue: proposal can only be queued if it is pending\"\n        );\n        Proposal storage proposal = proposals[proposalId];\n        proposal.eta = add256(block.timestamp, timelock.delay());\n\n        for (uint256 i = 0; i < proposal.targets.length; i++) {\n            _queueOrRevert(\n                proposal.targets[i],\n                proposal.values[i],\n                proposal.signatures[i],\n                proposal.calldatas[i],\n                proposal.eta\n            );\n        }\n\n        emit ProposalQueued(proposal.id, proposal.eta);\n    }\n\n    function state(uint256 proposalId) public view returns (ProposalState) {\n        require(\n            proposalCount >= proposalId && proposalId > 0,\n            \"Governor::state: invalid proposal id\"\n        );\n        Proposal storage proposal = proposals[proposalId];\n        if (proposal.executed) {\n            return ProposalState.Executed;\n        } else if (proposal.eta == 0) {\n            return ProposalState.Pending;\n        } else if (\n            block.timestamp >= add256(proposal.eta, timelock.GRACE_PERIOD())\n        ) {\n            return ProposalState.Expired;\n        } else {\n            return ProposalState.Queued;\n        }\n    }\n\n    function _queueOrRevert(\n        address target,\n        uint256 value,\n        string memory signature,\n        bytes memory data,\n        uint256 eta\n    ) internal {\n        require(\n            !timelock.queuedTransactions(\n                keccak256(abi.encode(target, value, signature, data, eta))\n            ),\n            \"Governor::_queueOrRevert: proposal action already queued at eta\"\n        );\n        timelock.queueTransaction(target, value, signature, data, eta);\n    }\n\n    function execute(uint256 proposalId) public payable {\n        require(\n            state(proposalId) == ProposalState.Queued,\n            \"Governor::execute: proposal can only be executed if it is queued\"\n        );\n        Proposal storage proposal = proposals[proposalId];\n        proposal.executed = true;\n        for (uint256 i = 0; i < proposal.targets.length; i++) {\n            timelock.executeTransaction.value(proposal.values[i])(\n                proposal.targets[i],\n                proposal.values[i],\n                proposal.signatures[i],\n                proposal.calldatas[i],\n                proposal.eta\n            );\n        }\n        emit ProposalExecuted(proposalId);\n    }\n\n    function getActions(uint256 proposalId)\n        public\n        view\n        returns (\n            address[] memory targets,\n            uint256[] memory values,\n            string[] memory signatures,\n            bytes[] memory calldatas\n        )\n    {\n        Proposal storage p = proposals[proposalId];\n        return (p.targets, p.values, p.signatures, p.calldatas);\n    }\n\n    function __acceptAdmin() public {\n        require(\n            msg.sender == guardian,\n            \"Governor::__acceptAdmin: sender must be gov guardian\"\n        );\n        timelock.acceptAdmin();\n    }\n\n    function __queueSetTimelockPendingAdmin(\n        address newPendingAdmin,\n        uint256 eta\n    ) public {\n        require(\n            msg.sender == guardian,\n            \"Governor::__queueSetTimelockPendingAdmin: sender must be gov guardian\"\n        );\n        timelock.queueTransaction(\n            address(timelock),\n            0,\n            \"setPendingAdmin(address)\",\n            abi.encode(newPendingAdmin),\n            eta\n        );\n    }\n\n    function __executeSetTimelockPendingAdmin(\n        address newPendingAdmin,\n        uint256 eta\n    ) public {\n        require(\n            msg.sender == guardian,\n            \"Governor::__executeSetTimelockPendingAdmin: sender must be gov guardian\"\n        );\n        timelock.executeTransaction(\n            address(timelock),\n            0,\n            \"setPendingAdmin(address)\",\n            abi.encode(newPendingAdmin),\n            eta\n        );\n    }\n\n    function add256(uint256 a, uint256 b) internal pure returns (uint256) {\n        uint256 c = a + b;\n        require(c >= a, \"addition overflow\");\n        return c;\n    }\n\n    function sub256(uint256 a, uint256 b) internal pure returns (uint256) {\n        require(b <= a, \"subtraction underflow\");\n        return a - b;\n    }\n}\n"}, "contracts/interfaces/ITimelock.sol": {"content": "pragma solidity 0.5.11;\n\ninterface ITimelock {\n    function delay() external view returns (uint256);\n\n    function GRACE_PERIOD() external view returns (uint256);\n\n    function acceptAdmin() external;\n\n    function queuedTransactions(bytes32 hash) external view returns (bool);\n\n    function queueTransaction(\n        address target,\n        uint256 value,\n        string calldata signature,\n        bytes calldata data,\n        uint256 eta\n    ) external returns (bytes32);\n\n    function cancelTransaction(\n        address target,\n        uint256 value,\n        string calldata signature,\n        bytes calldata data,\n        uint256 eta\n    ) external;\n\n    function executeTransaction(\n        address target,\n        uint256 value,\n        string calldata signature,\n        bytes calldata data,\n        uint256 eta\n    ) external payable returns (bytes memory);\n}\n"}, "contracts/governance/InitializableGovernable.sol": {"content": "pragma solidity 0.5.11;\n\n/**\n * @title OUSD InitializableGovernable Contract\n * <AUTHOR> Protocol Inc\n */\nimport {\n    Initializable\n} from \"@openzeppelin/upgrades/contracts/Initializable.sol\";\n\nimport { Governable } from \"./Governable.sol\";\n\ncontract InitializableGovernable is Governable, Initializable {\n    function _initialize(address _governor) internal {\n        _changeGovernor(_governor);\n    }\n}\n"}, "@openzeppelin/upgrades/contracts/Initializable.sol": {"content": "pragma solidity >=0.4.24 <0.7.0;\n\n\n/**\n * @title Initializable\n *\n * @dev Helper contract to support initializer functions. To use it, replace\n * the constructor with a function that has the `initializer` modifier.\n * WARNING: Unlike constructors, initializer functions must be manually\n * invoked. This applies both to deploying an Initializable contract, as well\n * as extending an Initializable contract via inheritance.\n * WARNING: When used with inheritance, manual care must be taken to not invoke\n * a parent initializer twice, or ensure that all initializers are idempotent,\n * because this is not dealt with automatically as with constructors.\n */\ncontract Initializable {\n\n  /**\n   * @dev Indicates that the contract has been initialized.\n   */\n  bool private initialized;\n\n  /**\n   * @dev Indicates that the contract is in the process of being initialized.\n   */\n  bool private initializing;\n\n  /**\n   * @dev Modifier to use in the initializer function of a contract.\n   */\n  modifier initializer() {\n    require(initializing || isConstructor() || !initialized, \"Contract instance has already been initialized\");\n\n    bool isTopLevelCall = !initializing;\n    if (isTopLevelCall) {\n      initializing = true;\n      initialized = true;\n    }\n\n    _;\n\n    if (isTopLevelCall) {\n      initializing = false;\n    }\n  }\n\n  /// @dev Returns true if and only if the function is running in the constructor\n  function isConstructor() private view returns (bool) {\n    // extcodesize checks the size of the code stored in an address, and\n    // address returns the current address. Since the code is still not\n    // deployed when running a constructor, any checks on its code size will\n    // yield zero, making it an effective way to detect if a contract is\n    // under construction or not.\n    address self = address(this);\n    uint256 cs;\n    assembly { cs := extcodesize(self) }\n    return cs == 0;\n  }\n\n  // Reserved storage space to allow for layout changes in the future.\n  uint256[50] private ______gap;\n}\n"}, "contracts/interfaces/IBasicToken.sol": {"content": "pragma solidity 0.5.11;\n\ninterface IBasicToken {\n    function symbol() external view returns (string memory);\n\n    function decimals() external view returns (uint8);\n}\n"}, "contracts/interfaces/IEthUsdOracle.sol": {"content": "pragma solidity 0.5.11;\n\ninterface IEthUsdOracle {\n    /**\n     * @notice Returns ETH price in USD.\n     * @return Price in USD with 6 decimal digits.\n     */\n    function ethUsdPrice() external view returns (uint256);\n\n    /**\n     * @notice Returns token price in USD.\n     * @param symbol. Asset symbol. For ex. \"DAI\".\n     * @return Price in USD with 6 decimal digits.\n     */\n    function tokUsdPrice(string calldata symbol)\n        external\n        view\n        returns (uint256);\n\n    /**\n     * @notice Returns the asset price in ETH.\n     * @param symbol. Asset symbol. For ex. \"DAI\".\n     * @return Price in ETH with 8 decimal digits.\n     */\n    function tokEthPrice(string calldata symbol) external returns (uint256);\n}\n\ninterface IViewEthUsdOracle {\n    /**\n     * @notice Returns ETH price in USD.\n     * @return Price in USD with 6 decimal digits.\n     */\n    function ethUsdPrice() external view returns (uint256);\n\n    /**\n     * @notice Returns token price in USD.\n     * @param symbol. Asset symbol. For ex. \"DAI\".\n     * @return Price in USD with 6 decimal digits.\n     */\n    function tokUsdPrice(string calldata symbol)\n        external\n        view\n        returns (uint256);\n\n    /**\n     * @notice Returns the asset price in ETH.\n     * @param symbol. Asset symbol. For ex. \"DAI\".\n     * @return Price in ETH with 8 decimal digits.\n     */\n    function tokEthPrice(string calldata symbol)\n        external\n        view\n        returns (uint256);\n}\n"}, "contracts/interfaces/IMinMaxOracle.sol": {"content": "pragma solidity 0.5.11;\n\ninterface IMinMaxOracle {\n    //Assuming 8 decimals\n    function priceMin(string calldata symbol) external returns (uint256);\n\n    function priceMax(string calldata symbol) external returns (uint256);\n}\n\ninterface IViewMinMaxOracle {\n    function priceMin(string calldata symbol) external view returns (uint256);\n\n    function priceMax(string calldata symbol) external view returns (uint256);\n}\n"}, "contracts/interfaces/IPriceOracle.sol": {"content": "pragma solidity 0.5.11;\n\ninterface IPriceOracle {\n    /**\n     * @dev returns the asset price in USD, 6 decimal digits.\n     * Compatible with the Open Price Feed.\n     */\n    function price(string calldata symbol) external view returns (uint256);\n}\n"}, "contracts/interfaces/IRebaseHooks.sol": {"content": "pragma solidity 0.5.11;\n\ninterface IRebaseHooks {\n    function postRebase(bool sync) external;\n}\n"}, "contracts/interfaces/IStrategy.sol": {"content": "pragma solidity 0.5.11;\n\n/**\n * @title Platform interface to integrate with lending platform like Compound, AAVE etc.\n */\ninterface IStrategy {\n    /**\n     * @dev Deposit the given asset to Lending platform.\n     * @param _asset asset address\n     * @param _amount Amount to deposit\n     */\n    function deposit(address _asset, uint256 _amount)\n        external\n        returns (uint256 amountDeposited);\n\n    /**\n     * @dev Withdraw given asset from Lending platform\n     */\n    function withdraw(\n        address _recipient,\n        address _asset,\n        uint256 _amount\n    ) external returns (uint256 amountWithdrawn);\n\n    /**\n     * @dev Returns the current balance of the given asset.\n     */\n    function checkBalance(address _asset)\n        external\n        view\n        returns (uint256 balance);\n\n    /**\n     * @dev Returns bool indicating whether strategy supports asset.\n     */\n    function supportsAsset(address _asset) external view returns (bool);\n\n    /**\n     * @dev Liquidate all assets in strategy and return them to Vault.\n     */\n    function liquidate() external;\n\n    /**\n     * @dev Collect reward tokens from the Strategy.\n     */\n    function collectRewardToken() external;\n\n    function rewardTokenAddress() external pure returns (address);\n}\n"}, "contracts/interfaces/IVault.sol": {"content": "pragma solidity 0.5.11;\n\ninterface IVault {\n    event AssetSupported(address _asset);\n    event StrategyAdded(address _addr);\n    event StrategyRemoved(address _addr);\n    event Mint(address _addr, uint256 _value);\n    event Redeem(address _addr, uint256 _value);\n    event StrategyWeightsUpdated(\n        address[] _strategyAddresses,\n        uint256[] weights\n    );\n    event DepositsPaused();\n    event DepositsUnpaused();\n\n    // Governable.sol\n    function transferGovernance(address _newGovernor) external;\n\n    function claimGovernance() external;\n\n    function governor() external view returns (address);\n\n    // VaultAdmin.sol\n    function setPriceProvider(address _priceProvider) external;\n\n    function priceProvider() external view returns (address);\n\n    function setRedeemFeeBps(uint256 _redeemFeeBps) external;\n\n    function redeemFeeBps() external view returns (uint256);\n\n    function setVaultBuffer(uint256 _vaultBuffer) external;\n\n    function vaultBuffer() external view returns (uint256);\n\n    function setAutoAllocateThreshold(uint256 _threshold) external;\n\n    function autoAllocateThreshold() external view returns (uint256);\n\n    function setRebaseThreshold(uint256 _threshold) external;\n\n    function rebaseThreshold() external view returns (uint256);\n\n    function setRebaseHooksAddr(address _address) external;\n\n    function rebaseHooksAddr() external view returns (address);\n\n    function setUniswapAddr(address _address) external;\n\n    function uniswapAddr() external view returns (address);\n\n    function supportAsset(address _asset) external;\n\n    function addStrategy(address _addr, uint256 _targetWeight) external;\n\n    function removeStrategy(address _addr) external;\n\n    function setStrategyWeights(\n        address[] calldata _strategyAddresses,\n        uint256[] calldata _weights\n    ) external;\n\n    function pauseRebase() external;\n\n    function unpauseRebase() external;\n\n    function rebasePaused() external view returns (bool);\n\n    function pauseDeposits() external;\n\n    function unpauseDeposits() external;\n\n    function depositPaused() external view returns (bool);\n\n    function transferToken(address _asset, uint256 _amount) external;\n\n    function harvest() external;\n\n    function harvest(address _strategyAddr) external;\n\n    function priceUSDMint(string calldata symbol) external returns (uint256);\n\n    function priceUSDRedeem(string calldata symbol) external returns (uint256);\n\n    // VaultCore.sol\n    function mint(address _asset, uint256 _amount) external;\n\n    function mintMultiple(\n        address[] calldata _assets,\n        uint256[] calldata _amount\n    ) external;\n\n    function redeem(uint256 _amount) external;\n\n    function redeemAll() external;\n\n    function allocate() external;\n\n    function rebase() external returns (uint256);\n\n    function checkBalance() external view returns (uint256);\n\n    function checkBalance(address _asset) external view returns (uint256);\n\n    function calculateRedeemOutputs(uint256 _amount)\n        external\n        returns (uint256[] memory);\n\n    function getAssetCount() external view returns (uint256);\n\n    function getAllAssets() external view returns (address[] memory);\n\n    function getStrategyCount() external view returns (uint256);\n\n    function isSupportedAsset(address _asset) external view returns (bool);\n}\n"}, "contracts/interfaces/IViewVault.sol": {"content": "pragma solidity 0.5.11;\n\ncontract IViewVault {\n    function getAllAssets() public view returns (address[] memory);\n\n    function totalValue() public view returns (uint256 value);\n\n    function priceUSDMint(string calldata symbol)\n        external\n        view\n        returns (uint256);\n\n    function priceUSDRedeem(string calldata symbol)\n        external\n        view\n        returns (uint256);\n\n    function calculateRedeemOutputs(uint256 _amount)\n        external\n        view\n        returns (uint256[] memory);\n}\n"}, "contracts/interfaces/uniswap/IUniswapV2Pair.sol": {"content": "pragma solidity 0.5.11;\n\ninterface IUniswapV2Pair {\n    function token0() external view returns (address);\n\n    function token1() external view returns (address);\n\n    function getReserves()\n        external\n        view\n        returns (\n            uint112 reserve0,\n            uint112 reserve1,\n            uint32 blockTimestampLast\n        );\n\n    function price0CumulativeLast() external view returns (uint256);\n\n    function price1CumulativeLast() external view returns (uint256);\n\n    function sync() external;\n}\n"}, "contracts/interfaces/uniswap/IUniswapV2Router02.sol": {"content": "pragma solidity 0.5.11;\n\ninterface IUniswapV2Router {\n    function WETH() external pure returns (address);\n\n    function swapExactTokensForTokens(\n        uint256 amountIn,\n        uint256 amountOutMin,\n        address[] calldata path,\n        address to,\n        uint256 deadline\n    ) external returns (uint256[] memory amounts);\n}\n"}, "contracts/mocks/curve/Mock3CRV.sol": {"content": "pragma solidity 0.5.11;\n\nimport \"../MintableERC20.sol\";\n\ncontract Mock3CRV is MintableERC20 {\n    uint256 public constant decimals = 18;\n    string public constant symbol = \"3Crv\";\n    string public constant name = \"Curve.fi DAI/USDC/USDT\";\n\n    function mint(address to, uint256 value) public returns (bool) {\n        _mint(to, value);\n        return true;\n    }\n\n    function burnFrom(address from, uint256 value) public returns (bool) {\n        _burn(from, value);\n        return true;\n    }\n}\n"}, "contracts/mocks/MintableERC20.sol": {"content": "pragma solidity 0.5.11;\n\nimport { ERC20 } from \"@openzeppelin/contracts/token/ERC20/ERC20.sol\";\n\ninterface IMintableERC20 {\n    function mint(uint256 value) external returns (bool);\n}\n\n/**\n * @title ERC20Mintable\n * @dev ERC20 minting logic\n */\ncontract MintableERC20 is IMintableERC20, ERC20 {\n    /**\n     * @dev Function to mint tokens\n     * @param value The amount of tokens to mint.\n     * @return A boolean that indicates if the operation was successful.\n     */\n    function mint(uint256 value) public returns (bool) {\n        _mint(msg.sender, value);\n        return true;\n    }\n}\n"}, "@openzeppelin/contracts/token/ERC20/ERC20.sol": {"content": "pragma solidity ^0.5.0;\n\nimport \"../../GSN/Context.sol\";\nimport \"./IERC20.sol\";\nimport \"../../math/SafeMath.sol\";\n\n/**\n * @dev Implementation of the {IERC20} interface.\n *\n * This implementation is agnostic to the way tokens are created. This means\n * that a supply mechanism has to be added in a derived contract using {_mint}.\n * For a generic mechanism see {ERC20Mintable}.\n *\n * TIP: For a detailed writeup see our guide\n * https://forum.zeppelin.solutions/t/how-to-implement-erc20-supply-mechanisms/226[How\n * to implement supply mechanisms].\n *\n * We have followed general OpenZeppelin guidelines: functions revert instead\n * of returning `false` on failure. This behavior is nonetheless conventional\n * and does not conflict with the expectations of ERC20 applications.\n *\n * Additionally, an {Approval} event is emitted on calls to {transferFrom}.\n * This allows applications to reconstruct the allowance for all accounts just\n * by listening to said events. Other implementations of the EIP may not emit\n * these events, as it isn't required by the specification.\n *\n * Finally, the non-standard {decreaseAllowance} and {increaseAllowance}\n * functions have been added to mitigate the well-known issues around setting\n * allowances. See {IERC20-approve}.\n */\ncontract ERC20 is Context, IERC20 {\n    using SafeMath for uint256;\n\n    mapping (address => uint256) private _balances;\n\n    mapping (address => mapping (address => uint256)) private _allowances;\n\n    uint256 private _totalSupply;\n\n    /**\n     * @dev See {IERC20-totalSupply}.\n     */\n    function totalSupply() public view returns (uint256) {\n        return _totalSupply;\n    }\n\n    /**\n     * @dev See {IERC20-balanceOf}.\n     */\n    function balanceOf(address account) public view returns (uint256) {\n        return _balances[account];\n    }\n\n    /**\n     * @dev See {IERC20-transfer}.\n     *\n     * Requirements:\n     *\n     * - `recipient` cannot be the zero address.\n     * - the caller must have a balance of at least `amount`.\n     */\n    function transfer(address recipient, uint256 amount) public returns (bool) {\n        _transfer(_msgSender(), recipient, amount);\n        return true;\n    }\n\n    /**\n     * @dev See {IERC20-allowance}.\n     */\n    function allowance(address owner, address spender) public view returns (uint256) {\n        return _allowances[owner][spender];\n    }\n\n    /**\n     * @dev See {IERC20-approve}.\n     *\n     * Requirements:\n     *\n     * - `spender` cannot be the zero address.\n     */\n    function approve(address spender, uint256 amount) public returns (bool) {\n        _approve(_msgSender(), spender, amount);\n        return true;\n    }\n\n    /**\n     * @dev See {IERC20-transferFrom}.\n     *\n     * Emits an {Approval} event indicating the updated allowance. This is not\n     * required by the EIP. See the note at the beginning of {ERC20};\n     *\n     * Requirements:\n     * - `sender` and `recipient` cannot be the zero address.\n     * - `sender` must have a balance of at least `amount`.\n     * - the caller must have allowance for `sender`'s tokens of at least\n     * `amount`.\n     */\n    function transferFrom(address sender, address recipient, uint256 amount) public returns (bool) {\n        _transfer(sender, recipient, amount);\n        _approve(sender, _msgSender(), _allowances[sender][_msgSender()].sub(amount, \"ERC20: transfer amount exceeds allowance\"));\n        return true;\n    }\n\n    /**\n     * @dev Atomically increases the allowance granted to `spender` by the caller.\n     *\n     * This is an alternative to {approve} that can be used as a mitigation for\n     * problems described in {IERC20-approve}.\n     *\n     * Emits an {Approval} event indicating the updated allowance.\n     *\n     * Requirements:\n     *\n     * - `spender` cannot be the zero address.\n     */\n    function increaseAllowance(address spender, uint256 addedValue) public returns (bool) {\n        _approve(_msgSender(), spender, _allowances[_msgSender()][spender].add(addedValue));\n        return true;\n    }\n\n    /**\n     * @dev Atomically decreases the allowance granted to `spender` by the caller.\n     *\n     * This is an alternative to {approve} that can be used as a mitigation for\n     * problems described in {IERC20-approve}.\n     *\n     * Emits an {Approval} event indicating the updated allowance.\n     *\n     * Requirements:\n     *\n     * - `spender` cannot be the zero address.\n     * - `spender` must have allowance for the caller of at least\n     * `subtractedValue`.\n     */\n    function decreaseAllowance(address spender, uint256 subtractedValue) public returns (bool) {\n        _approve(_msgSender(), spender, _allowances[_msgSender()][spender].sub(subtractedValue, \"ERC20: decreased allowance below zero\"));\n        return true;\n    }\n\n    /**\n     * @dev Moves tokens `amount` from `sender` to `recipient`.\n     *\n     * This is internal function is equivalent to {transfer}, and can be used to\n     * e.g. implement automatic token fees, slashing mechanisms, etc.\n     *\n     * Emits a {Transfer} event.\n     *\n     * Requirements:\n     *\n     * - `sender` cannot be the zero address.\n     * - `recipient` cannot be the zero address.\n     * - `sender` must have a balance of at least `amount`.\n     */\n    function _transfer(address sender, address recipient, uint256 amount) internal {\n        require(sender != address(0), \"ERC20: transfer from the zero address\");\n        require(recipient != address(0), \"ERC20: transfer to the zero address\");\n\n        _balances[sender] = _balances[sender].sub(amount, \"ERC20: transfer amount exceeds balance\");\n        _balances[recipient] = _balances[recipient].add(amount);\n        emit Transfer(sender, recipient, amount);\n    }\n\n    /** @dev Creates `amount` tokens and assigns them to `account`, increasing\n     * the total supply.\n     *\n     * Emits a {Transfer} event with `from` set to the zero address.\n     *\n     * Requirements\n     *\n     * - `to` cannot be the zero address.\n     */\n    function _mint(address account, uint256 amount) internal {\n        require(account != address(0), \"ERC20: mint to the zero address\");\n\n        _totalSupply = _totalSupply.add(amount);\n        _balances[account] = _balances[account].add(amount);\n        emit Transfer(address(0), account, amount);\n    }\n\n    /**\n     * @dev Destroys `amount` tokens from `account`, reducing the\n     * total supply.\n     *\n     * Emits a {Transfer} event with `to` set to the zero address.\n     *\n     * Requirements\n     *\n     * - `account` cannot be the zero address.\n     * - `account` must have at least `amount` tokens.\n     */\n    function _burn(address account, uint256 amount) internal {\n        require(account != address(0), \"ERC20: burn from the zero address\");\n\n        _balances[account] = _balances[account].sub(amount, \"ERC20: burn amount exceeds balance\");\n        _totalSupply = _totalSupply.sub(amount);\n        emit Transfer(account, address(0), amount);\n    }\n\n    /**\n     * @dev Sets `amount` as the allowance of `spender` over the `owner`s tokens.\n     *\n     * This is internal function is equivalent to `approve`, and can be used to\n     * e.g. set automatic allowances for certain subsystems, etc.\n     *\n     * Emits an {Approval} event.\n     *\n     * Requirements:\n     *\n     * - `owner` cannot be the zero address.\n     * - `spender` cannot be the zero address.\n     */\n    function _approve(address owner, address spender, uint256 amount) internal {\n        require(owner != address(0), \"ERC20: approve from the zero address\");\n        require(spender != address(0), \"ERC20: approve to the zero address\");\n\n        _allowances[owner][spender] = amount;\n        emit Approval(owner, spender, amount);\n    }\n\n    /**\n     * @dev Destroys `amount` tokens from `account`.`amount` is then deducted\n     * from the caller's allowance.\n     *\n     * See {_burn} and {_approve}.\n     */\n    function _burnFrom(address account, uint256 amount) internal {\n        _burn(account, amount);\n        _approve(account, _msgSender(), _allowances[account][_msgSender()].sub(amount, \"ERC20: burn amount exceeds allowance\"));\n    }\n}\n"}, "@openzeppelin/contracts/GSN/Context.sol": {"content": "pragma solidity ^0.5.0;\n\n/*\n * @dev Provides information about the current execution context, including the\n * sender of the transaction and its data. While these are generally available\n * via msg.sender and msg.data, they should not be accessed in such a direct\n * manner, since when dealing with GSN meta-transactions the account sending and\n * paying for execution may not be the actual sender (as far as an application\n * is concerned).\n *\n * This contract is only required for intermediate, library-like contracts.\n */\ncontract Context {\n    // Empty internal constructor, to prevent people from mistakenly deploying\n    // an instance of this contract, which should be used via inheritance.\n    constructor () internal { }\n    // solhint-disable-previous-line no-empty-blocks\n\n    function _msgSender() internal view returns (address payable) {\n        return msg.sender;\n    }\n\n    function _msgData() internal view returns (bytes memory) {\n        this; // silence state mutability warning without generating bytecode - see https://github.com/ethereum/solidity/issues/2691\n        return msg.data;\n    }\n}\n"}, "@openzeppelin/contracts/token/ERC20/IERC20.sol": {"content": "pragma solidity ^0.5.0;\n\n/**\n * @dev Interface of the ERC20 standard as defined in the EIP. Does not include\n * the optional functions; to access them see {ERC20Detailed}.\n */\ninterface IERC20 {\n    /**\n     * @dev Returns the amount of tokens in existence.\n     */\n    function totalSupply() external view returns (uint256);\n\n    /**\n     * @dev Returns the amount of tokens owned by `account`.\n     */\n    function balanceOf(address account) external view returns (uint256);\n\n    /**\n     * @dev Moves `amount` tokens from the caller's account to `recipient`.\n     *\n     * Returns a boolean value indicating whether the operation succeeded.\n     *\n     * Emits a {Transfer} event.\n     */\n    function transfer(address recipient, uint256 amount) external returns (bool);\n\n    /**\n     * @dev Returns the remaining number of tokens that `spender` will be\n     * allowed to spend on behalf of `owner` through {transferFrom}. This is\n     * zero by default.\n     *\n     * This value changes when {approve} or {transferFrom} are called.\n     */\n    function allowance(address owner, address spender) external view returns (uint256);\n\n    /**\n     * @dev Sets `amount` as the allowance of `spender` over the caller's tokens.\n     *\n     * Returns a boolean value indicating whether the operation succeeded.\n     *\n     * IMPORTANT: Beware that changing an allowance with this method brings the risk\n     * that someone may use both the old and the new allowance by unfortunate\n     * transaction ordering. One possible solution to mitigate this race\n     * condition is to first reduce the spender's allowance to 0 and set the\n     * desired value afterwards:\n     * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\n     *\n     * Emits an {Approval} event.\n     */\n    function approve(address spender, uint256 amount) external returns (bool);\n\n    /**\n     * @dev Moves `amount` tokens from `sender` to `recipient` using the\n     * allowance mechanism. `amount` is then deducted from the caller's\n     * allowance.\n     *\n     * Returns a boolean value indicating whether the operation succeeded.\n     *\n     * Emits a {Transfer} event.\n     */\n    function transferFrom(address sender, address recipient, uint256 amount) external returns (bool);\n\n    /**\n     * @dev Emitted when `value` tokens are moved from one account (`from`) to\n     * another (`to`).\n     *\n     * Note that `value` may be zero.\n     */\n    event Transfer(address indexed from, address indexed to, uint256 value);\n\n    /**\n     * @dev Emitted when the allowance of a `spender` for an `owner` is set by\n     * a call to {approve}. `value` is the new allowance.\n     */\n    event Approval(address indexed owner, address indexed spender, uint256 value);\n}\n"}, "@openzeppelin/contracts/math/SafeMath.sol": {"content": "pragma solidity ^0.5.0;\n\n/**\n * @dev Wrappers over Solidity's arithmetic operations with added overflow\n * checks.\n *\n * Arithmetic operations in Solidity wrap on overflow. This can easily result\n * in bugs, because programmers usually assume that an overflow raises an\n * error, which is the standard behavior in high level programming languages.\n * `SafeMath` restores this intuition by reverting the transaction when an\n * operation overflows.\n *\n * Using this library instead of the unchecked operations eliminates an entire\n * class of bugs, so it's recommended to use it always.\n */\nlibrary SafeMath {\n    /**\n     * @dev Returns the addition of two unsigned integers, reverting on\n     * overflow.\n     *\n     * Counterpart to Solidity's `+` operator.\n     *\n     * Requirements:\n     * - Addition cannot overflow.\n     */\n    function add(uint256 a, uint256 b) internal pure returns (uint256) {\n        uint256 c = a + b;\n        require(c >= a, \"SafeMath: addition overflow\");\n\n        return c;\n    }\n\n    /**\n     * @dev Returns the subtraction of two unsigned integers, reverting on\n     * overflow (when the result is negative).\n     *\n     * Counterpart to Solidity's `-` operator.\n     *\n     * Requirements:\n     * - Subtraction cannot overflow.\n     */\n    function sub(uint256 a, uint256 b) internal pure returns (uint256) {\n        return sub(a, b, \"SafeMath: subtraction overflow\");\n    }\n\n    /**\n     * @dev Returns the subtraction of two unsigned integers, reverting with custom message on\n     * overflow (when the result is negative).\n     *\n     * Counterpart to Solidity's `-` operator.\n     *\n     * Requirements:\n     * - Subtraction cannot overflow.\n     *\n     * _Available since v2.4.0._\n     */\n    function sub(uint256 a, uint256 b, string memory errorMessage) internal pure returns (uint256) {\n        require(b <= a, errorMessage);\n        uint256 c = a - b;\n\n        return c;\n    }\n\n    /**\n     * @dev Returns the multiplication of two unsigned integers, reverting on\n     * overflow.\n     *\n     * Counterpart to Solidity's `*` operator.\n     *\n     * Requirements:\n     * - Multiplication cannot overflow.\n     */\n    function mul(uint256 a, uint256 b) internal pure returns (uint256) {\n        // Gas optimization: this is cheaper than requiring 'a' not being zero, but the\n        // benefit is lost if 'b' is also tested.\n        // See: https://github.com/OpenZeppelin/openzeppelin-contracts/pull/522\n        if (a == 0) {\n            return 0;\n        }\n\n        uint256 c = a * b;\n        require(c / a == b, \"SafeMath: multiplication overflow\");\n\n        return c;\n    }\n\n    /**\n     * @dev Returns the integer division of two unsigned integers. Reverts on\n     * division by zero. The result is rounded towards zero.\n     *\n     * Counterpart to Solidity's `/` operator. Note: this function uses a\n     * `revert` opcode (which leaves remaining gas untouched) while Solidity\n     * uses an invalid opcode to revert (consuming all remaining gas).\n     *\n     * Requirements:\n     * - The divisor cannot be zero.\n     */\n    function div(uint256 a, uint256 b) internal pure returns (uint256) {\n        return div(a, b, \"SafeMath: division by zero\");\n    }\n\n    /**\n     * @dev Returns the integer division of two unsigned integers. Reverts with custom message on\n     * division by zero. The result is rounded towards zero.\n     *\n     * Counterpart to Solidity's `/` operator. Note: this function uses a\n     * `revert` opcode (which leaves remaining gas untouched) while Solidity\n     * uses an invalid opcode to revert (consuming all remaining gas).\n     *\n     * Requirements:\n     * - The divisor cannot be zero.\n     *\n     * _Available since v2.4.0._\n     */\n    function div(uint256 a, uint256 b, string memory errorMessage) internal pure returns (uint256) {\n        // Solidity only automatically asserts when dividing by 0\n        require(b > 0, errorMessage);\n        uint256 c = a / b;\n        // assert(a == b * c + a % b); // There is no case in which this doesn't hold\n\n        return c;\n    }\n\n    /**\n     * @dev Returns the remainder of dividing two unsigned integers. (unsigned integer modulo),\n     * Reverts when dividing by zero.\n     *\n     * Counterpart to Solidity's `%` operator. This function uses a `revert`\n     * opcode (which leaves remaining gas untouched) while Solidity uses an\n     * invalid opcode to revert (consuming all remaining gas).\n     *\n     * Requirements:\n     * - The divisor cannot be zero.\n     */\n    function mod(uint256 a, uint256 b) internal pure returns (uint256) {\n        return mod(a, b, \"SafeMath: modulo by zero\");\n    }\n\n    /**\n     * @dev Returns the remainder of dividing two unsigned integers. (unsigned integer modulo),\n     * Reverts with custom message when dividing by zero.\n     *\n     * Counterpart to Solidity's `%` operator. This function uses a `revert`\n     * opcode (which leaves remaining gas untouched) while Solidity uses an\n     * invalid opcode to revert (consuming all remaining gas).\n     *\n     * Requirements:\n     * - The divisor cannot be zero.\n     *\n     * _Available since v2.4.0._\n     */\n    function mod(uint256 a, uint256 b, string memory errorMessage) internal pure returns (uint256) {\n        require(b != 0, errorMessage);\n        return a % b;\n    }\n}\n"}, "contracts/mocks/curve/MockCRV.sol": {"content": "pragma solidity 0.5.11;\n\nimport \"../MintableERC20.sol\";\n\ncontract MockCRV is MintableERC20 {\n    uint256 public constant decimals = 18;\n    string public constant symbol = \"CRV\";\n    string public constant name = \"Curve DAO Token\";\n}\n"}, "contracts/mocks/curve/MockCRVMinter.sol": {"content": "pragma solidity 0.5.11;\n\nimport { IERC20 } from \"@openzeppelin/contracts/token/ERC20/IERC20.sol\";\n\nimport { IMintableERC20 } from \"../MintableERC20.sol\";\n\ncontract MockCRVMinter {\n    address crv;\n\n    constructor(address _crv) public {\n        crv = _crv;\n    }\n\n    function mint(address _address) external {\n        uint256 amount = 2e18;\n        IMintableERC20(crv).mint(amount);\n        IERC20(crv).transfer(_address, amount);\n    }\n}\n"}, "contracts/mocks/curve/MockCurveGauge.sol": {"content": "pragma solidity 0.5.11;\n\nimport \"@openzeppelin/contracts/token/ERC20/ERC20.sol\";\n\nimport { ICurveGauge } from \"../../strategies/ICurveGauge.sol\";\n\ncontract MockCurveGauge is ICurveGauge {\n    mapping(address => uint256) private _balances;\n    address lpToken;\n\n    constructor(address _lpToken) public {\n        lpToken = _lpToken;\n    }\n\n    function balanceOf(address account) public view returns (uint256) {\n        return _balances[account];\n    }\n\n    function deposit(uint256 _value, address _account) external {\n        IERC20(lpToken).transferFrom(msg.sender, address(this), _value);\n        _balances[_account] += _value;\n    }\n\n    function withdraw(uint256 _value) external {\n        IERC20(lpToken).transfer(msg.sender, _value);\n        _balances[msg.sender] -= _value;\n    }\n}\n"}, "contracts/strategies/ICurveGauge.sol": {"content": "pragma solidity 0.5.11;\n\ninterface ICurveGauge {\n    function balanceOf(address account) external view returns (uint256);\n\n    function deposit(uint256 value, address account) external;\n\n    function withdraw(uint256 value) external;\n}\n"}, "contracts/mocks/curve/MockCurvePool.sol": {"content": "pragma solidity 0.5.11;\n\nimport \"@nomiclabs/buidler/console.sol\";\nimport { ERC20 } from \"@openzeppelin/contracts/token/ERC20/ERC20.sol\";\nimport { IERC20 } from \"@openzeppelin/contracts/token/ERC20/IERC20.sol\";\n\nimport { IMintableERC20 } from \"../MintableERC20.sol\";\nimport { ICurvePool } from \"../../strategies/ICurvePool.sol\";\nimport { StableMath } from \"../../utils/StableMath.sol\";\nimport \"../../utils/Helpers.sol\";\n\ncontract MockCurvePool is ERC20 {\n    using StableMath for uint256;\n\n    address[] public coins;\n    address lpToken;\n\n    constructor(address[3] memory _coins, address _lpToken) public {\n        coins = _coins;\n        lpToken = _lpToken;\n    }\n\n    // Returns the same amount of LP tokens in 1e18 decimals\n    function add_liquidity(uint256[3] calldata _amounts, uint256 _minAmount)\n        external\n    {\n        uint256 sum = 0;\n        for (uint256 i = 0; i < _amounts.length; i++) {\n            if (_amounts[i] > 0) {\n                IERC20(coins[i]).transferFrom(\n                    msg.sender,\n                    address(this),\n                    _amounts[i]\n                );\n                uint256 assetDecimals = Helpers.getDecimals(coins[i]);\n                // Convert to 1e18 and add to sum\n                sum += _amounts[i].scaleBy(int8(18 - assetDecimals));\n            }\n        }\n        // Hacky way of simulating slippage to check _minAmount\n        if (sum == 29000e18) sum = 14500e18;\n        require(sum > _minAmount, \"Slippage ruined your day\");\n        // Send LP token to sender, e.g. 3CRV\n        IMintableERC20(lpToken).mint(sum);\n        IERC20(lpToken).transfer(msg.sender, sum);\n    }\n\n    // Dumb implementation that returns the same amount\n    function calc_withdraw_one_coin(uint256 _amount, int128 _index)\n        public\n        view\n        returns (uint256)\n    {\n        uint256 assetDecimals = Helpers.getDecimals(coins[uint256(_index)]);\n        return _amount.scaleBy(int8(assetDecimals - 18));\n    }\n\n    function remove_liquidity_one_coin(\n        uint256 _amount,\n        int128 _index,\n        uint256 _minAmount\n    ) external {\n        IERC20(lpToken).transferFrom(msg.sender, address(this), _amount);\n        uint256[] memory amounts = new uint256[](coins.length);\n        amounts[uint256(_index)] = _amount;\n        uint256 amount = calc_withdraw_one_coin(_amount, _index);\n        IERC20(coins[uint256(_index)]).transfer(msg.sender, amount);\n    }\n}\n"}, "@nomiclabs/buidler/console.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity >= 0.4.22 <0.8.0;\n\nlibrary console {\n\taddress constant CONSOLE_ADDRESS = address(0x000000000000000000636F6e736F6c652e6c6f67);\n\n\tfunction _sendLogPayload(bytes memory payload) private view {\n\t\tuint256 payloadLength = payload.length;\n\t\taddress consoleAddress = CONSOLE_ADDRESS;\n\t\tassembly {\n\t\t\tlet payloadStart := add(payload, 32)\n\t\t\tlet r := staticcall(gas(), consoleAddress, payloadStart, payloadLength, 0, 0)\n\t\t}\n\t}\n\n\tfunction log() internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log()\"));\n\t}\n\n\tfunction logInt(int p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(int)\", p0));\n\t}\n\n\tfunction logUint(uint p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint)\", p0));\n\t}\n\n\tfunction logString(string memory p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string)\", p0));\n\t}\n\n\tfunction logBool(bool p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool)\", p0));\n\t}\n\n\tfunction logAddress(address p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address)\", p0));\n\t}\n\n\tfunction logBytes(bytes memory p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes)\", p0));\n\t}\n\n\tfunction logByte(byte p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(byte)\", p0));\n\t}\n\n\tfunction logBytes1(bytes1 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes1)\", p0));\n\t}\n\n\tfunction logBytes2(bytes2 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes2)\", p0));\n\t}\n\n\tfunction logBytes3(bytes3 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes3)\", p0));\n\t}\n\n\tfunction logBytes4(bytes4 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes4)\", p0));\n\t}\n\n\tfunction logBytes5(bytes5 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes5)\", p0));\n\t}\n\n\tfunction logBytes6(bytes6 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes6)\", p0));\n\t}\n\n\tfunction logBytes7(bytes7 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes7)\", p0));\n\t}\n\n\tfunction logBytes8(bytes8 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes8)\", p0));\n\t}\n\n\tfunction logBytes9(bytes9 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes9)\", p0));\n\t}\n\n\tfunction logBytes10(bytes10 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes10)\", p0));\n\t}\n\n\tfunction logBytes11(bytes11 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes11)\", p0));\n\t}\n\n\tfunction logBytes12(bytes12 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes12)\", p0));\n\t}\n\n\tfunction logBytes13(bytes13 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes13)\", p0));\n\t}\n\n\tfunction logBytes14(bytes14 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes14)\", p0));\n\t}\n\n\tfunction logBytes15(bytes15 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes15)\", p0));\n\t}\n\n\tfunction logBytes16(bytes16 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes16)\", p0));\n\t}\n\n\tfunction logBytes17(bytes17 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes17)\", p0));\n\t}\n\n\tfunction logBytes18(bytes18 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes18)\", p0));\n\t}\n\n\tfunction logBytes19(bytes19 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes19)\", p0));\n\t}\n\n\tfunction logBytes20(bytes20 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes20)\", p0));\n\t}\n\n\tfunction logBytes21(bytes21 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes21)\", p0));\n\t}\n\n\tfunction logBytes22(bytes22 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes22)\", p0));\n\t}\n\n\tfunction logBytes23(bytes23 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes23)\", p0));\n\t}\n\n\tfunction logBytes24(bytes24 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes24)\", p0));\n\t}\n\n\tfunction logBytes25(bytes25 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes25)\", p0));\n\t}\n\n\tfunction logBytes26(bytes26 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes26)\", p0));\n\t}\n\n\tfunction logBytes27(bytes27 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes27)\", p0));\n\t}\n\n\tfunction logBytes28(bytes28 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes28)\", p0));\n\t}\n\n\tfunction logBytes29(bytes29 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes29)\", p0));\n\t}\n\n\tfunction logBytes30(bytes30 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes30)\", p0));\n\t}\n\n\tfunction logBytes31(bytes31 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes31)\", p0));\n\t}\n\n\tfunction logBytes32(bytes32 p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bytes32)\", p0));\n\t}\n\n\tfunction log(uint p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint)\", p0));\n\t}\n\n\tfunction log(string memory p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string)\", p0));\n\t}\n\n\tfunction log(bool p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool)\", p0));\n\t}\n\n\tfunction log(address p0) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address)\", p0));\n\t}\n\n\tfunction log(uint p0, uint p1) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint)\", p0, p1));\n\t}\n\n\tfunction log(uint p0, string memory p1) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string)\", p0, p1));\n\t}\n\n\tfunction log(uint p0, bool p1) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool)\", p0, p1));\n\t}\n\n\tfunction log(uint p0, address p1) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address)\", p0, p1));\n\t}\n\n\tfunction log(string memory p0, uint p1) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint)\", p0, p1));\n\t}\n\n\tfunction log(string memory p0, string memory p1) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string)\", p0, p1));\n\t}\n\n\tfunction log(string memory p0, bool p1) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool)\", p0, p1));\n\t}\n\n\tfunction log(string memory p0, address p1) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address)\", p0, p1));\n\t}\n\n\tfunction log(bool p0, uint p1) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint)\", p0, p1));\n\t}\n\n\tfunction log(bool p0, string memory p1) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string)\", p0, p1));\n\t}\n\n\tfunction log(bool p0, bool p1) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool)\", p0, p1));\n\t}\n\n\tfunction log(bool p0, address p1) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address)\", p0, p1));\n\t}\n\n\tfunction log(address p0, uint p1) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint)\", p0, p1));\n\t}\n\n\tfunction log(address p0, string memory p1) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string)\", p0, p1));\n\t}\n\n\tfunction log(address p0, bool p1) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool)\", p0, p1));\n\t}\n\n\tfunction log(address p0, address p1) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address)\", p0, p1));\n\t}\n\n\tfunction log(uint p0, uint p1, uint p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint,uint)\", p0, p1, p2));\n\t}\n\n\tfunction log(uint p0, uint p1, string memory p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint,string)\", p0, p1, p2));\n\t}\n\n\tfunction log(uint p0, uint p1, bool p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint,bool)\", p0, p1, p2));\n\t}\n\n\tfunction log(uint p0, uint p1, address p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint,address)\", p0, p1, p2));\n\t}\n\n\tfunction log(uint p0, string memory p1, uint p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string,uint)\", p0, p1, p2));\n\t}\n\n\tfunction log(uint p0, string memory p1, string memory p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string,string)\", p0, p1, p2));\n\t}\n\n\tfunction log(uint p0, string memory p1, bool p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string,bool)\", p0, p1, p2));\n\t}\n\n\tfunction log(uint p0, string memory p1, address p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string,address)\", p0, p1, p2));\n\t}\n\n\tfunction log(uint p0, bool p1, uint p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool,uint)\", p0, p1, p2));\n\t}\n\n\tfunction log(uint p0, bool p1, string memory p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool,string)\", p0, p1, p2));\n\t}\n\n\tfunction log(uint p0, bool p1, bool p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool,bool)\", p0, p1, p2));\n\t}\n\n\tfunction log(uint p0, bool p1, address p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool,address)\", p0, p1, p2));\n\t}\n\n\tfunction log(uint p0, address p1, uint p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address,uint)\", p0, p1, p2));\n\t}\n\n\tfunction log(uint p0, address p1, string memory p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address,string)\", p0, p1, p2));\n\t}\n\n\tfunction log(uint p0, address p1, bool p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address,bool)\", p0, p1, p2));\n\t}\n\n\tfunction log(uint p0, address p1, address p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address,address)\", p0, p1, p2));\n\t}\n\n\tfunction log(string memory p0, uint p1, uint p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint,uint)\", p0, p1, p2));\n\t}\n\n\tfunction log(string memory p0, uint p1, string memory p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint,string)\", p0, p1, p2));\n\t}\n\n\tfunction log(string memory p0, uint p1, bool p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint,bool)\", p0, p1, p2));\n\t}\n\n\tfunction log(string memory p0, uint p1, address p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint,address)\", p0, p1, p2));\n\t}\n\n\tfunction log(string memory p0, string memory p1, uint p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string,uint)\", p0, p1, p2));\n\t}\n\n\tfunction log(string memory p0, string memory p1, string memory p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string,string)\", p0, p1, p2));\n\t}\n\n\tfunction log(string memory p0, string memory p1, bool p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string,bool)\", p0, p1, p2));\n\t}\n\n\tfunction log(string memory p0, string memory p1, address p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string,address)\", p0, p1, p2));\n\t}\n\n\tfunction log(string memory p0, bool p1, uint p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool,uint)\", p0, p1, p2));\n\t}\n\n\tfunction log(string memory p0, bool p1, string memory p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool,string)\", p0, p1, p2));\n\t}\n\n\tfunction log(string memory p0, bool p1, bool p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool,bool)\", p0, p1, p2));\n\t}\n\n\tfunction log(string memory p0, bool p1, address p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool,address)\", p0, p1, p2));\n\t}\n\n\tfunction log(string memory p0, address p1, uint p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address,uint)\", p0, p1, p2));\n\t}\n\n\tfunction log(string memory p0, address p1, string memory p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address,string)\", p0, p1, p2));\n\t}\n\n\tfunction log(string memory p0, address p1, bool p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address,bool)\", p0, p1, p2));\n\t}\n\n\tfunction log(string memory p0, address p1, address p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address,address)\", p0, p1, p2));\n\t}\n\n\tfunction log(bool p0, uint p1, uint p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint,uint)\", p0, p1, p2));\n\t}\n\n\tfunction log(bool p0, uint p1, string memory p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint,string)\", p0, p1, p2));\n\t}\n\n\tfunction log(bool p0, uint p1, bool p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint,bool)\", p0, p1, p2));\n\t}\n\n\tfunction log(bool p0, uint p1, address p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint,address)\", p0, p1, p2));\n\t}\n\n\tfunction log(bool p0, string memory p1, uint p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string,uint)\", p0, p1, p2));\n\t}\n\n\tfunction log(bool p0, string memory p1, string memory p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string,string)\", p0, p1, p2));\n\t}\n\n\tfunction log(bool p0, string memory p1, bool p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string,bool)\", p0, p1, p2));\n\t}\n\n\tfunction log(bool p0, string memory p1, address p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string,address)\", p0, p1, p2));\n\t}\n\n\tfunction log(bool p0, bool p1, uint p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool,uint)\", p0, p1, p2));\n\t}\n\n\tfunction log(bool p0, bool p1, string memory p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool,string)\", p0, p1, p2));\n\t}\n\n\tfunction log(bool p0, bool p1, bool p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool,bool)\", p0, p1, p2));\n\t}\n\n\tfunction log(bool p0, bool p1, address p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool,address)\", p0, p1, p2));\n\t}\n\n\tfunction log(bool p0, address p1, uint p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address,uint)\", p0, p1, p2));\n\t}\n\n\tfunction log(bool p0, address p1, string memory p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address,string)\", p0, p1, p2));\n\t}\n\n\tfunction log(bool p0, address p1, bool p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address,bool)\", p0, p1, p2));\n\t}\n\n\tfunction log(bool p0, address p1, address p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address,address)\", p0, p1, p2));\n\t}\n\n\tfunction log(address p0, uint p1, uint p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint,uint)\", p0, p1, p2));\n\t}\n\n\tfunction log(address p0, uint p1, string memory p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint,string)\", p0, p1, p2));\n\t}\n\n\tfunction log(address p0, uint p1, bool p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint,bool)\", p0, p1, p2));\n\t}\n\n\tfunction log(address p0, uint p1, address p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint,address)\", p0, p1, p2));\n\t}\n\n\tfunction log(address p0, string memory p1, uint p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string,uint)\", p0, p1, p2));\n\t}\n\n\tfunction log(address p0, string memory p1, string memory p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string,string)\", p0, p1, p2));\n\t}\n\n\tfunction log(address p0, string memory p1, bool p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string,bool)\", p0, p1, p2));\n\t}\n\n\tfunction log(address p0, string memory p1, address p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string,address)\", p0, p1, p2));\n\t}\n\n\tfunction log(address p0, bool p1, uint p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool,uint)\", p0, p1, p2));\n\t}\n\n\tfunction log(address p0, bool p1, string memory p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool,string)\", p0, p1, p2));\n\t}\n\n\tfunction log(address p0, bool p1, bool p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool,bool)\", p0, p1, p2));\n\t}\n\n\tfunction log(address p0, bool p1, address p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool,address)\", p0, p1, p2));\n\t}\n\n\tfunction log(address p0, address p1, uint p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address,uint)\", p0, p1, p2));\n\t}\n\n\tfunction log(address p0, address p1, string memory p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address,string)\", p0, p1, p2));\n\t}\n\n\tfunction log(address p0, address p1, bool p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address,bool)\", p0, p1, p2));\n\t}\n\n\tfunction log(address p0, address p1, address p2) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address,address)\", p0, p1, p2));\n\t}\n\n\tfunction log(uint p0, uint p1, uint p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint,uint,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, uint p1, uint p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint,uint,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, uint p1, uint p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint,uint,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, uint p1, uint p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint,uint,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, uint p1, string memory p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint,string,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, uint p1, string memory p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint,string,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, uint p1, string memory p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint,string,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, uint p1, string memory p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint,string,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, uint p1, bool p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint,bool,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, uint p1, bool p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint,bool,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, uint p1, bool p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint,bool,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, uint p1, bool p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint,bool,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, uint p1, address p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint,address,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, uint p1, address p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint,address,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, uint p1, address p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint,address,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, uint p1, address p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,uint,address,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, string memory p1, uint p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string,uint,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, string memory p1, uint p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string,uint,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, string memory p1, uint p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string,uint,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, string memory p1, uint p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string,uint,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, string memory p1, string memory p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string,string,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, string memory p1, string memory p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string,string,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, string memory p1, string memory p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string,string,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, string memory p1, string memory p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string,string,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, string memory p1, bool p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string,bool,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, string memory p1, bool p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string,bool,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, string memory p1, bool p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string,bool,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, string memory p1, bool p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string,bool,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, string memory p1, address p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string,address,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, string memory p1, address p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string,address,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, string memory p1, address p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string,address,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, string memory p1, address p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,string,address,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, bool p1, uint p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool,uint,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, bool p1, uint p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool,uint,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, bool p1, uint p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool,uint,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, bool p1, uint p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool,uint,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, bool p1, string memory p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool,string,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, bool p1, string memory p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool,string,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, bool p1, string memory p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool,string,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, bool p1, string memory p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool,string,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, bool p1, bool p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool,bool,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, bool p1, bool p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool,bool,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, bool p1, bool p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool,bool,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, bool p1, bool p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool,bool,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, bool p1, address p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool,address,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, bool p1, address p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool,address,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, bool p1, address p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool,address,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, bool p1, address p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,bool,address,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, address p1, uint p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address,uint,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, address p1, uint p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address,uint,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, address p1, uint p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address,uint,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, address p1, uint p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address,uint,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, address p1, string memory p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address,string,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, address p1, string memory p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address,string,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, address p1, string memory p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address,string,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, address p1, string memory p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address,string,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, address p1, bool p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address,bool,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, address p1, bool p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address,bool,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, address p1, bool p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address,bool,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, address p1, bool p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address,bool,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, address p1, address p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address,address,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, address p1, address p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address,address,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, address p1, address p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address,address,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(uint p0, address p1, address p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(uint,address,address,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, uint p1, uint p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint,uint,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, uint p1, uint p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint,uint,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, uint p1, uint p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint,uint,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, uint p1, uint p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint,uint,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, uint p1, string memory p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint,string,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, uint p1, string memory p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint,string,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, uint p1, string memory p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint,string,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, uint p1, string memory p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint,string,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, uint p1, bool p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint,bool,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, uint p1, bool p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint,bool,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, uint p1, bool p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint,bool,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, uint p1, bool p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint,bool,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, uint p1, address p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint,address,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, uint p1, address p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint,address,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, uint p1, address p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint,address,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, uint p1, address p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,uint,address,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, string memory p1, uint p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string,uint,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, string memory p1, uint p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string,uint,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, string memory p1, uint p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string,uint,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, string memory p1, uint p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string,uint,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, string memory p1, string memory p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string,string,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, string memory p1, string memory p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string,string,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, string memory p1, string memory p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string,string,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, string memory p1, string memory p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string,string,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, string memory p1, bool p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string,bool,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, string memory p1, bool p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string,bool,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, string memory p1, bool p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string,bool,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, string memory p1, bool p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string,bool,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, string memory p1, address p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string,address,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, string memory p1, address p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string,address,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, string memory p1, address p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string,address,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, string memory p1, address p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,string,address,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, bool p1, uint p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool,uint,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, bool p1, uint p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool,uint,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, bool p1, uint p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool,uint,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, bool p1, uint p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool,uint,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, bool p1, string memory p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool,string,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, bool p1, string memory p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool,string,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, bool p1, string memory p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool,string,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, bool p1, string memory p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool,string,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, bool p1, bool p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool,bool,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, bool p1, bool p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool,bool,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, bool p1, bool p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool,bool,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, bool p1, bool p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool,bool,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, bool p1, address p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool,address,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, bool p1, address p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool,address,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, bool p1, address p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool,address,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, bool p1, address p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,bool,address,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, address p1, uint p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address,uint,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, address p1, uint p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address,uint,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, address p1, uint p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address,uint,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, address p1, uint p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address,uint,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, address p1, string memory p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address,string,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, address p1, string memory p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address,string,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, address p1, string memory p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address,string,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, address p1, string memory p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address,string,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, address p1, bool p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address,bool,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, address p1, bool p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address,bool,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, address p1, bool p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address,bool,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, address p1, bool p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address,bool,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, address p1, address p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address,address,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, address p1, address p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address,address,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, address p1, address p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address,address,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(string memory p0, address p1, address p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(string,address,address,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, uint p1, uint p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint,uint,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, uint p1, uint p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint,uint,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, uint p1, uint p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint,uint,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, uint p1, uint p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint,uint,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, uint p1, string memory p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint,string,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, uint p1, string memory p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint,string,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, uint p1, string memory p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint,string,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, uint p1, string memory p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint,string,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, uint p1, bool p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint,bool,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, uint p1, bool p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint,bool,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, uint p1, bool p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint,bool,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, uint p1, bool p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint,bool,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, uint p1, address p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint,address,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, uint p1, address p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint,address,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, uint p1, address p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint,address,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, uint p1, address p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,uint,address,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, string memory p1, uint p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string,uint,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, string memory p1, uint p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string,uint,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, string memory p1, uint p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string,uint,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, string memory p1, uint p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string,uint,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, string memory p1, string memory p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string,string,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, string memory p1, string memory p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string,string,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, string memory p1, string memory p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string,string,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, string memory p1, string memory p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string,string,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, string memory p1, bool p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string,bool,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, string memory p1, bool p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string,bool,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, string memory p1, bool p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string,bool,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, string memory p1, bool p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string,bool,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, string memory p1, address p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string,address,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, string memory p1, address p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string,address,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, string memory p1, address p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string,address,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, string memory p1, address p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,string,address,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, bool p1, uint p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool,uint,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, bool p1, uint p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool,uint,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, bool p1, uint p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool,uint,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, bool p1, uint p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool,uint,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, bool p1, string memory p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool,string,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, bool p1, string memory p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool,string,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, bool p1, string memory p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool,string,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, bool p1, string memory p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool,string,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, bool p1, bool p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool,bool,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, bool p1, bool p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool,bool,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, bool p1, bool p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool,bool,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, bool p1, bool p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool,bool,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, bool p1, address p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool,address,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, bool p1, address p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool,address,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, bool p1, address p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool,address,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, bool p1, address p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,bool,address,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, address p1, uint p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address,uint,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, address p1, uint p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address,uint,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, address p1, uint p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address,uint,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, address p1, uint p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address,uint,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, address p1, string memory p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address,string,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, address p1, string memory p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address,string,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, address p1, string memory p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address,string,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, address p1, string memory p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address,string,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, address p1, bool p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address,bool,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, address p1, bool p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address,bool,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, address p1, bool p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address,bool,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, address p1, bool p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address,bool,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, address p1, address p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address,address,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, address p1, address p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address,address,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, address p1, address p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address,address,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(bool p0, address p1, address p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(bool,address,address,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, uint p1, uint p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint,uint,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, uint p1, uint p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint,uint,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, uint p1, uint p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint,uint,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, uint p1, uint p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint,uint,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, uint p1, string memory p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint,string,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, uint p1, string memory p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint,string,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, uint p1, string memory p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint,string,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, uint p1, string memory p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint,string,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, uint p1, bool p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint,bool,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, uint p1, bool p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint,bool,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, uint p1, bool p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint,bool,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, uint p1, bool p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint,bool,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, uint p1, address p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint,address,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, uint p1, address p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint,address,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, uint p1, address p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint,address,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, uint p1, address p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,uint,address,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, string memory p1, uint p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string,uint,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, string memory p1, uint p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string,uint,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, string memory p1, uint p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string,uint,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, string memory p1, uint p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string,uint,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, string memory p1, string memory p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string,string,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, string memory p1, string memory p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string,string,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, string memory p1, string memory p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string,string,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, string memory p1, string memory p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string,string,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, string memory p1, bool p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string,bool,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, string memory p1, bool p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string,bool,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, string memory p1, bool p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string,bool,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, string memory p1, bool p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string,bool,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, string memory p1, address p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string,address,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, string memory p1, address p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string,address,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, string memory p1, address p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string,address,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, string memory p1, address p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,string,address,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, bool p1, uint p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool,uint,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, bool p1, uint p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool,uint,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, bool p1, uint p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool,uint,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, bool p1, uint p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool,uint,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, bool p1, string memory p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool,string,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, bool p1, string memory p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool,string,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, bool p1, string memory p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool,string,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, bool p1, string memory p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool,string,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, bool p1, bool p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool,bool,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, bool p1, bool p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool,bool,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, bool p1, bool p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool,bool,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, bool p1, bool p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool,bool,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, bool p1, address p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool,address,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, bool p1, address p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool,address,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, bool p1, address p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool,address,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, bool p1, address p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,bool,address,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, address p1, uint p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address,uint,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, address p1, uint p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address,uint,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, address p1, uint p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address,uint,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, address p1, uint p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address,uint,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, address p1, string memory p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address,string,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, address p1, string memory p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address,string,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, address p1, string memory p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address,string,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, address p1, string memory p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address,string,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, address p1, bool p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address,bool,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, address p1, bool p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address,bool,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, address p1, bool p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address,bool,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, address p1, bool p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address,bool,address)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, address p1, address p2, uint p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address,address,uint)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, address p1, address p2, string memory p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address,address,string)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, address p1, address p2, bool p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address,address,bool)\", p0, p1, p2, p3));\n\t}\n\n\tfunction log(address p0, address p1, address p2, address p3) internal view {\n\t\t_sendLogPayload(abi.encodeWithSignature(\"log(address,address,address,address)\", p0, p1, p2, p3));\n\t}\n\n}\n"}, "contracts/strategies/ICurvePool.sol": {"content": "pragma solidity 0.5.11;\n\ninterface ICurvePool {\n    function get_virtual_price() external returns (uint256);\n\n    function add_liquidity(uint256[3] calldata _amounts, uint256 _min) external;\n\n    function calc_token_amount(uint256[3] calldata _amounts, bool _deposit)\n        external\n        returns (uint256);\n\n    function remove_liquidity_one_coin(\n        uint256 _amount,\n        int128 _index,\n        uint256 _minAmount\n    ) external;\n\n    function calc_withdraw_one_coin(uint256 _amount, int128 _index)\n        external\n        view\n        returns (uint256);\n\n    function coins(uint256 _index) external view returns (address);\n}\n"}, "contracts/utils/StableMath.sol": {"content": "pragma solidity 0.5.11;\n\nimport { SafeMath } from \"@openzeppelin/contracts/math/SafeMath.sol\";\n\n// Based on StableMath from Stability Labs Pty. Ltd.\n// https://github.com/mstable/mStable-contracts/blob/master/contracts/shared/StableMath.sol\n\nlibrary StableMath {\n    using SafeMath for uint256;\n\n    /**\n     * @dev Scaling unit for use in specific calculations,\n     * where 1 * 10**18, or 1e18 represents a unit '1'\n     */\n    uint256 private constant FULL_SCALE = 1e18;\n\n    /***************************************\n                    Helpers\n    ****************************************/\n\n    /**\n     * @dev Adjust the scale of an integer\n     * @param adjustment Amount to adjust by e.g. scaleBy(1e18, -1) == 1e17\n     */\n    function scaleBy(uint256 x, int8 adjustment)\n        internal\n        pure\n        returns (uint256)\n    {\n        if (adjustment > 0) {\n            x = x.mul(10**uint256(adjustment));\n        } else if (adjustment < 0) {\n            x = x.div(10**uint256(adjustment * -1));\n        }\n        return x;\n    }\n\n    /***************************************\n               Precise Arithmetic\n    ****************************************/\n\n    /**\n     * @dev Multiplies two precise units, and then truncates by the full scale\n     * @param x Left hand input to multiplication\n     * @param y Right hand input to multiplication\n     * @return Result after multiplying the two inputs and then dividing by the shared\n     *         scale unit\n     */\n    function mulTruncate(uint256 x, uint256 y) internal pure returns (uint256) {\n        return mulTruncateScale(x, y, FULL_SCALE);\n    }\n\n    /**\n     * @dev Multiplies two precise units, and then truncates by the given scale. For example,\n     * when calculating 90% of 10e18, (10e18 * 9e17) / 1e18 = (9e36) / 1e18 = 9e18\n     * @param x Left hand input to multiplication\n     * @param y Right hand input to multiplication\n     * @param scale Scale unit\n     * @return Result after multiplying the two inputs and then dividing by the shared\n     *         scale unit\n     */\n    function mulTruncateScale(\n        uint256 x,\n        uint256 y,\n        uint256 scale\n    ) internal pure returns (uint256) {\n        // e.g. assume scale = fullScale\n        // z = 10e18 * 9e17 = 9e36\n        uint256 z = x.mul(y);\n        // return 9e38 / 1e18 = 9e18\n        return z.div(scale);\n    }\n\n    /**\n     * @dev Multiplies two precise units, and then truncates by the full scale, rounding up the result\n     * @param x Left hand input to multiplication\n     * @param y Right hand input to multiplication\n     * @return Result after multiplying the two inputs and then dividing by the shared\n     *          scale unit, rounded up to the closest base unit.\n     */\n    function mulTruncateCeil(uint256 x, uint256 y)\n        internal\n        pure\n        returns (uint256)\n    {\n        // e.g. 8e17 * 17268172638 = 138145381104e17\n        uint256 scaled = x.mul(y);\n        // e.g. 138145381104e17 + 9.99...e17 = 138145381113.99...e17\n        uint256 ceil = scaled.add(FULL_SCALE.sub(1));\n        // e.g. 13814538111.399...e18 / 1e18 = 13814538111\n        return ceil.div(FULL_SCALE);\n    }\n\n    /**\n     * @dev Precisely divides two units, by first scaling the left hand operand. Useful\n     *      for finding percentage weightings, i.e. 8e18/10e18 = 80% (or 8e17)\n     * @param x Left hand input to division\n     * @param y Right hand input to division\n     * @return Result after multiplying the left operand by the scale, and\n     *         executing the division on the right hand input.\n     */\n    function divPrecisely(uint256 x, uint256 y)\n        internal\n        pure\n        returns (uint256)\n    {\n        // e.g. 8e18 * 1e18 = 8e36\n        uint256 z = x.mul(FULL_SCALE);\n        // e.g. 8e36 / 10e18 = 8e17\n        return z.div(y);\n    }\n}\n"}, "contracts/utils/Helpers.sol": {"content": "pragma solidity 0.5.11;\n\nimport { IBasicToken } from \"../interfaces/IBasicToken.sol\";\n\nlibrary Helpers {\n    /**\n     * @notice Fetch the `symbol()` from an ERC20 token\n     * @dev Grabs the `symbol()` from a contract\n     * @param _token Address of the ERC20 token\n     * @return string Symbol of the ERC20 token\n     */\n    function getSymbol(address _token) internal view returns (string memory) {\n        string memory symbol = IBasicToken(_token).symbol();\n        return symbol;\n    }\n\n    /**\n     * @notice Fetch the `decimals()` from an ERC20 token\n     * @dev Grabs the `decimals()` from a contract and fails if\n     *      the decimal value does not live within a certain range\n     * @param _token Address of the ERC20 token\n     * @return uint256 Decimals of the ERC20 token\n     */\n    function getDecimals(address _token) internal view returns (uint256) {\n        uint256 decimals = IBasicToken(_token).decimals();\n        require(\n            decimals >= 4 && decimals <= 18,\n            \"Token must have sufficient decimal places\"\n        );\n\n        return decimals;\n    }\n}\n"}, "contracts/mocks/MockChainlinkOracleFeed.sol": {"content": "pragma solidity 0.5.11;\n\nimport \"../oracle/AggregatorV3Interface.sol\";\n\ncontract MockChainlinkOracleFeed is AggregatorV3Interface {\n    int256 price;\n    uint8 numDecimals;\n\n    constructor(int256 _price, uint8 _decimals) public {\n        price = _price;\n        numDecimals = _decimals;\n    }\n\n    function decimals() external view returns (uint8) {\n        return numDecimals;\n    }\n\n    function description() external view returns (string memory) {\n        return \"MockOracleEthFeed\";\n    }\n\n    function version() external view returns (uint256) {\n        return 1;\n    }\n\n    function setPrice(int256 _price) public {\n        price = _price;\n    }\n\n    function setDecimals(uint8 _decimals) public {\n        numDecimals = _decimals;\n    }\n\n    // getRoundData and latestRoundData should both raise \"No data present\"\n    // if they do not have data to report, instead of returning unset values\n    // which could be misinterpreted as actual reported values.\n    function getRoundData(uint80 _roundId)\n        external\n        view\n        returns (\n            uint80 roundId,\n            int256 answer,\n            uint256 startedAt,\n            uint256 updatedAt,\n            uint80 answeredInRound\n        )\n    {\n        roundId = _roundId;\n        answer = price;\n        startedAt = 0;\n        updatedAt = 0;\n        answeredInRound = 0;\n    }\n\n    function latestRoundData()\n        external\n        view\n        returns (\n            uint80 roundId,\n            int256 answer,\n            uint256 startedAt,\n            uint256 updatedAt,\n            uint80 answeredInRound\n        )\n    {\n        roundId = 0;\n        answer = price;\n        startedAt = 0;\n        updatedAt = 0;\n        answeredInRound = 0;\n    }\n}\n"}, "contracts/oracle/AggregatorV3Interface.sol": {"content": "pragma solidity ^0.5.11;\n\ninterface AggregatorV3Interface {\n    function decimals() external view returns (uint8);\n\n    function description() external view returns (string memory);\n\n    function version() external view returns (uint256);\n\n    // getRoundData and latestRoundData should both raise \"No data present\"\n    // if they do not have data to report, instead of returning unset values\n    // which could be misinterpreted as actual reported values.\n    function getRoundData(uint80 _roundId)\n        external\n        view\n        returns (\n            uint80 roundId,\n            int256 answer,\n            uint256 startedAt,\n            uint256 updatedAt,\n            uint80 answeredInRound\n        );\n\n    function latestRoundData()\n        external\n        view\n        returns (\n            uint80 roundId,\n            int256 answer,\n            uint256 startedAt,\n            uint256 updatedAt,\n            uint80 answeredInRound\n        );\n}\n"}, "contracts/mocks/MockCOMP.sol": {"content": "pragma solidity 0.5.11;\n\nimport \"./MintableERC20.sol\";\n\ncontract MockCOMP is MintableERC20 {\n    uint256 public constant decimals = 18;\n    string public constant symbol = \"COMP\";\n    string public constant name = \"COMP\";\n}\n"}, "contracts/mocks/MockCToken.sol": {"content": "pragma solidity 0.5.11;\n\nimport {\n    IERC20,\n    ERC20,\n    ERC20Mintable\n} from \"@openzeppelin/contracts/token/ERC20/ERC20Mintable.sol\";\nimport {\n    ERC20Detailed\n} from \"@openzeppelin/contracts/token/ERC20/ERC20Detailed.sol\";\n\nimport { ICERC20 } from \"../strategies/ICompound.sol\";\nimport { StableMath } from \"../utils/StableMath.sol\";\n\ncontract MockCToken is ICERC20, ERC20, ERC20Detailed, ERC20Mintable {\n    using StableMath for uint256;\n\n    IERC20 public underlyingToken;\n    // underlying = cToken * exchangeRate\n    // cToken = underlying / exchangeRate\n    uint256 exchangeRate;\n\n    constructor(ERC20Detailed _underlyingToken)\n        public\n        ERC20Detailed(\"cMock\", \"cMK\", 8)\n    {\n        uint8 underlyingDecimals = _underlyingToken.decimals();\n        // if has 18 dp, exchange rate should be 1e26\n        // if has 8 dp, exchange rate should be 1e18\n        if (underlyingDecimals > 8) {\n            exchangeRate = 10**uint256(18 + underlyingDecimals - 10);\n        } else if (underlyingDecimals < 8) {\n            // e.g. 18-8+6 = 16\n            exchangeRate = 10**uint256(18 - 8 + underlyingDecimals);\n        } else {\n            exchangeRate = 1e18;\n        }\n        underlyingToken = _underlyingToken;\n    }\n\n    function mint(uint256 mintAmount) external returns (uint256) {\n        // Credit them with cToken\n        _mint(msg.sender, mintAmount.divPrecisely(exchangeRate));\n        // Take their reserve\n        underlyingToken.transferFrom(msg.sender, address(this), mintAmount);\n        return 0;\n    }\n\n    function redeem(uint256 redeemAmount) external returns (uint256) {\n        uint256 tokenAmount = redeemAmount.mulTruncate(exchangeRate);\n        // Burn the cToken\n        _burn(msg.sender, redeemAmount);\n        // Transfer underlying to caller\n        underlyingToken.transfer(msg.sender, tokenAmount);\n        return 0;\n    }\n\n    function redeemUnderlying(uint256 redeemAmount) external returns (uint256) {\n        uint256 cTokens = redeemAmount.divPrecisely(exchangeRate);\n        // Burn the cToken\n        _burn(msg.sender, cTokens);\n        // Transfer underlying to caller\n        underlyingToken.transfer(msg.sender, redeemAmount);\n        return 0;\n    }\n\n    function balanceOfUnderlying(address owner) external returns (uint256) {\n        uint256 cTokenBal = this.balanceOf(owner);\n        return cTokenBal.mulTruncate(exchangeRate);\n    }\n\n    function updateExchangeRate() internal returns (uint256) {\n        uint256 factor = 100002 * (10**13); // 0.002%\n        exchangeRate = exchangeRate.mulTruncate(factor);\n    }\n\n    function exchangeRateStored() external view returns (uint256) {\n        return exchangeRate;\n    }\n\n    function supplyRatePerBlock() external view returns (uint256) {\n        return 141 * (10**8);\n    }\n}\n"}, "@openzeppelin/contracts/token/ERC20/ERC20Mintable.sol": {"content": "pragma solidity ^0.5.0;\n\nimport \"./ERC20.sol\";\nimport \"../../access/roles/MinterRole.sol\";\n\n/**\n * @dev Extension of {ERC20} that adds a set of accounts with the {MinterRole},\n * which have permission to mint (create) new tokens as they see fit.\n *\n * At construction, the deployer of the contract is the only minter.\n */\ncontract ERC20Mintable is ERC20, MinterRole {\n    /**\n     * @dev See {ERC20-_mint}.\n     *\n     * Requirements:\n     *\n     * - the caller must have the {MinterRole}.\n     */\n    function mint(address account, uint256 amount) public onlyMinter returns (bool) {\n        _mint(account, amount);\n        return true;\n    }\n}\n"}, "@openzeppelin/contracts/access/roles/MinterRole.sol": {"content": "pragma solidity ^0.5.0;\n\nimport \"../../GSN/Context.sol\";\nimport \"../Roles.sol\";\n\ncontract MinterRole is Context {\n    using Roles for Roles.Role;\n\n    event MinterAdded(address indexed account);\n    event MinterRemoved(address indexed account);\n\n    Roles.Role private _minters;\n\n    constructor () internal {\n        _addMinter(_msgSender());\n    }\n\n    modifier onlyMinter() {\n        require(isMinter(_msgSender()), \"MinterRole: caller does not have the Mint<PERSON> role\");\n        _;\n    }\n\n    function isMinter(address account) public view returns (bool) {\n        return _minters.has(account);\n    }\n\n    function addMinter(address account) public onlyMinter {\n        _addMinter(account);\n    }\n\n    function renounceMinter() public {\n        _removeMinter(_msgSender());\n    }\n\n    function _addMinter(address account) internal {\n        _minters.add(account);\n        emit MinterAdded(account);\n    }\n\n    function _removeMinter(address account) internal {\n        _minters.remove(account);\n        emit MinterRemoved(account);\n    }\n}\n"}, "@openzeppelin/contracts/access/Roles.sol": {"content": "pragma solidity ^0.5.0;\n\n/**\n * @title Roles\n * @dev Library for managing addresses assigned to a Role.\n */\nlibrary Roles {\n    struct Role {\n        mapping (address => bool) bearer;\n    }\n\n    /**\n     * @dev Give an account access to this role.\n     */\n    function add(Role storage role, address account) internal {\n        require(!has(role, account), \"Roles: account already has role\");\n        role.bearer[account] = true;\n    }\n\n    /**\n     * @dev Remove an account's access to this role.\n     */\n    function remove(Role storage role, address account) internal {\n        require(has(role, account), \"Roles: account does not have role\");\n        role.bearer[account] = false;\n    }\n\n    /**\n     * @dev Check if an account has this role.\n     * @return bool\n     */\n    function has(Role storage role, address account) internal view returns (bool) {\n        require(account != address(0), \"Roles: account is the zero address\");\n        return role.bearer[account];\n    }\n}\n"}, "@openzeppelin/contracts/token/ERC20/ERC20Detailed.sol": {"content": "pragma solidity ^0.5.0;\n\nimport \"./IERC20.sol\";\n\n/**\n * @dev Optional functions from the ERC20 standard.\n */\ncontract ERC20Detailed is IERC20 {\n    string private _name;\n    string private _symbol;\n    uint8 private _decimals;\n\n    /**\n     * @dev Sets the values for `name`, `symbol`, and `decimals`. All three of\n     * these values are immutable: they can only be set once during\n     * construction.\n     */\n    constructor (string memory name, string memory symbol, uint8 decimals) public {\n        _name = name;\n        _symbol = symbol;\n        _decimals = decimals;\n    }\n\n    /**\n     * @dev Returns the name of the token.\n     */\n    function name() public view returns (string memory) {\n        return _name;\n    }\n\n    /**\n     * @dev Returns the symbol of the token, usually a shorter version of the\n     * name.\n     */\n    function symbol() public view returns (string memory) {\n        return _symbol;\n    }\n\n    /**\n     * @dev Returns the number of decimals used to get its user representation.\n     * For example, if `decimals` equals `2`, a balance of `505` tokens should\n     * be displayed to a user as `5,05` (`505 / 10 ** 2`).\n     *\n     * Tokens usually opt for a value of 18, imitating the relationship between\n     * <PERSON><PERSON> and <PERSON>.\n     *\n     * NOTE: This information is only used for _display_ purposes: it in\n     * no way affects any of the arithmetic of the contract, including\n     * {IERC20-balanceOf} and {IERC20-transfer}.\n     */\n    function decimals() public view returns (uint8) {\n        return _decimals;\n    }\n}\n"}, "contracts/strategies/ICompound.sol": {"content": "pragma solidity 0.5.11;\n\n/**\n * @dev Compound C Token interface\n * Documentation: https://compound.finance/developers/ctokens\n */\ninterface ICERC20 {\n    /**\n     * @notice The mint function transfers an asset into the protocol, which begins accumulating\n     * interest based on the current Supply Rate for the asset. The user receives a quantity of\n     * cTokens equal to the underlying tokens supplied, divided by the current Exchange Rate.\n     * @param mintAmount The amount of the asset to be supplied, in units of the underlying asset.\n     * @return 0 on success, otherwise an Error codes\n     */\n    function mint(uint256 mintAmount) external returns (uint256);\n\n    /**\n     * @notice Sender redeems cTokens in exchange for the underlying asset\n     * @dev Accrues interest whether or not the operation succeeds, unless reverted\n     * @param redeemTokens The number of cTokens to redeem into underlying\n     * @return uint 0=success, otherwise an error code.\n     */\n    function redeem(uint256 redeemTokens) external returns (uint256);\n\n    /**\n     * @notice The redeem underlying function converts cTokens into a specified quantity of the underlying\n     * asset, and returns them to the user. The amount of cTokens redeemed is equal to the quantity of\n     * underlying tokens received, divided by the current Exchange Rate. The amount redeemed must be less\n     * than the user's Account Liquidity and the market's available liquidity.\n     * @param redeemAmount The amount of underlying to be redeemed.\n     * @return 0 on success, otherwise an error code.\n     */\n    function redeemUnderlying(uint256 redeemAmount) external returns (uint256);\n\n    /**\n     * @notice The user's underlying balance, representing their assets in the protocol, is equal to\n     * the user's cToken balance multiplied by the Exchange Rate.\n     * @param owner The account to get the underlying balance of.\n     * @return The amount of underlying currently owned by the account.\n     */\n    function balanceOfUnderlying(address owner) external returns (uint256);\n\n    /**\n     * @notice Calculates the exchange rate from the underlying to the CToken\n     * @dev This function does not accrue interest before calculating the exchange rate\n     * @return Calculated exchange rate scaled by 1e18\n     */\n    function exchangeRateStored() external view returns (uint256);\n\n    /**\n     * @notice Get the token balance of the `owner`\n     * @param owner The address of the account to query\n     * @return The number of tokens owned by `owner`\n     */\n    function balanceOf(address owner) external view returns (uint256);\n\n    /**\n     * @notice Get the supply rate per block for supplying the token to Compound.\n     */\n    function supplyRatePerBlock() external view returns (uint256);\n}\n"}, "contracts/mocks/MockDAI.sol": {"content": "pragma solidity 0.5.11;\n\nimport \"./MintableERC20.sol\";\n\ncontract MockDAI is MintableERC20 {\n    uint256 public constant decimals = 18;\n    string public constant symbol = \"DAI\";\n    string public constant name = \"DAI\";\n}\n"}, "contracts/mocks/MockNonStandardToken.sol": {"content": "pragma solidity 0.5.11;\n\nimport \"./MintableERC20.sol\";\n\n/**\n * <PERSON>ck token contract to simulate tokens that don't\n * throw/revert when a transfer/transferFrom call fails\n */\ncontract MockNonStandardToken is MintableERC20 {\n    uint256 public constant decimals = 6;\n    string public constant symbol = \"NonStandardToken\";\n    string public constant name = \"NonStandardToken\";\n\n    function transfer(address recipient, uint256 amount) public returns (bool) {\n        if (balanceOf(msg.sender) < amount) {\n            // Fail silently\n            return false;\n        }\n\n        _transfer(_msgSender(), recipient, amount);\n        return true;\n    }\n\n    function transferFrom(\n        address sender,\n        address recipient,\n        uint256 amount\n    ) public returns (bool) {\n        if (balanceOf(sender) < amount) {\n            // Fail silently\n            return false;\n        }\n\n        _transfer(sender, recipient, amount);\n        _approve(\n            sender,\n            _msgSender(),\n            allowance(sender, _msgSender()).sub(\n                amount,\n                \"ERC20: transfer amount exceeds allowance\"\n            )\n        );\n        return true;\n    }\n}\n"}, "contracts/mocks/MockOracle.sol": {"content": "pragma solidity 0.5.11;\n\nimport \"../interfaces/IPriceOracle.sol\";\nimport \"../interfaces/IMinMaxOracle.sol\";\n\n/**\n * Mock of both price Oracle and min max oracles\n */\ncontract MockOracle is IPriceOracle, IMinMaxOracle {\n    mapping(bytes32 => uint256) prices;\n    mapping(bytes32 => uint256[]) pricesMinMax;\n    uint256 ethMin;\n    uint256 ethMax;\n\n    /**\n     * @dev returns the asset price in USD, 6 decimal digits.\n     * Compatible with the Open Price Feed.\n     */\n    function price(string calldata symbol) external view returns (uint256) {\n        return prices[keccak256(abi.encodePacked(symbol))];\n    }\n\n    /**\n     * @dev sets the price of the asset in USD, 6 decimal digits\n     *\n     */\n    function setPrice(string calldata symbol, uint256 _price) external {\n        prices[keccak256(abi.encodePacked(symbol))] = _price;\n    }\n\n    /**\n     * @dev sets the min and max price of ETH in USD, 6 decimal digits\n     *\n     */\n    function setEthPriceMinMax(uint256 _min, uint256 _max) external {\n        ethMin = _min;\n        ethMax = _max;\n    }\n\n    /**\n     * @dev sets the prices Min Max for a specific symbol in ETH, 8 decimal digits\n     *\n     */\n    function setTokPriceMinMax(\n        string calldata symbol,\n        uint256 _min,\n        uint256 _max\n    ) external {\n        pricesMinMax[keccak256(abi.encodePacked(symbol))] = [_min, _max];\n    }\n\n    /**\n     * @dev get the price of asset in ETH, 8 decimal digits.\n     */\n    function priceMin(string calldata symbol) external returns (uint256) {\n        uint256[] storage pMinMax = pricesMinMax[keccak256(\n            abi.encodePacked(symbol)\n        )];\n        return (pMinMax[0] * ethMin) / 1e6;\n    }\n\n    /**\n     * @dev get the price of asset in USD, 8 decimal digits.\n     * Not needed for now\n     */\n    function priceMax(string calldata symbol) external returns (uint256) {\n        uint256[] storage pMinMax = pricesMinMax[keccak256(\n            abi.encodePacked(symbol)\n        )];\n        return (pMinMax[1] * ethMax) / 1e6;\n    }\n}\n"}, "contracts/mocks/MockTUSD.sol": {"content": "pragma solidity 0.5.11;\n\nimport \"./MintableERC20.sol\";\n\ncontract MockTUSD is MintableERC20 {\n    uint256 public constant decimals = 18;\n    string public constant symbol = \"TUSD\";\n    string public constant name = \"TrueUSD\";\n}\n"}, "contracts/mocks/MockUniswapPair.sol": {"content": "pragma solidity 0.5.11;\n\nimport \"../oracle/UniswapLib.sol\";\nimport { IUniswapV2Pair } from \"../interfaces/uniswap/IUniswapV2Pair.sol\";\n\ncontract MockUniswapPair is IUniswapV2Pair {\n    address tok0;\n    address tok1;\n    uint112 reserve0;\n    uint112 reserve1;\n    uint256 blockTimestampLast;\n\n    bool public hasSynced = false;\n\n    constructor(\n        address _token0,\n        address _token1,\n        uint112 _reserve0,\n        uint112 _reserve1\n    ) public {\n        tok0 = _token0;\n        tok1 = _token1;\n        reserve0 = _reserve0;\n        reserve1 = _reserve1;\n        blockTimestampLast = block.timestamp;\n    }\n\n    function token0() external view returns (address) {\n        return tok0;\n    }\n\n    function token1() external view returns (address) {\n        return tok1;\n    }\n\n    function getReserves()\n        external\n        view\n        returns (\n            uint112,\n            uint112,\n            uint32\n        )\n    {\n        return (reserve0, reserve1, uint32(blockTimestampLast));\n    }\n\n    function setReserves(uint112 _reserve0, uint112 _reserve1) public {\n        reserve0 = _reserve0;\n        reserve1 = _reserve1;\n        blockTimestampLast = block.timestamp;\n    }\n\n    // CAUTION This will not work if you setReserves multiple times over multiple different blocks because then it wouldn't be a continuous reserve factor over that blockTimestamp,\n    // this assumes an even reserve ratio all the way through\n    function price0CumulativeLast() external view returns (uint256) {\n        return\n            uint256(FixedPoint.fraction(reserve1, reserve0)._x) *\n            blockTimestampLast;\n    }\n\n    function price1CumulativeLast() external view returns (uint256) {\n        return\n            uint256(FixedPoint.fraction(reserve0, reserve1)._x) *\n            blockTimestampLast;\n    }\n\n    function sync() external {\n        hasSynced = true;\n    }\n\n    function checkHasSynced() external view {\n        require(hasSynced, \"Not synced\");\n    }\n}\n"}, "contracts/oracle/UniswapLib.sol": {"content": "// SPDX-License-Identifier: GPL-3.0\n\npragma solidity ^0.5.11;\n\nimport { IUniswapV2Pair } from \"../interfaces/uniswap/IUniswapV2Pair.sol\";\n\n// Based on code from https://github.com/Uniswap/uniswap-v2-periphery\n\n// a library for handling binary fixed point numbers (https://en.wikipedia.org/wiki/Q_(number_format))\nlibrary FixedPoint {\n    // range: [0, 2**112 - 1]\n    // resolution: 1 / 2**112\n    struct uq112x112 {\n        uint224 _x;\n    }\n\n    // returns a uq112x112 which represents the ratio of the numerator to the denominator\n    // equivalent to encode(numerator).div(denominator)\n    function fraction(uint112 numerator, uint112 denominator)\n        internal\n        pure\n        returns (uq112x112 memory)\n    {\n        require(denominator > 0, \"FixedPoint: DIV_BY_ZERO\");\n        return uq112x112((uint224(numerator) << 112) / denominator);\n    }\n\n    // decode a uq112x112 into a uint with 18 decimals of precision\n    function decode112with18(uq112x112 memory self)\n        internal\n        pure\n        returns (uint256)\n    {\n        // we only have 256 - 224 = 32 bits to spare, so scaling up by ~60 bits is dangerous\n        // instead, get close to:\n        //  (x * 1e18) >> 112\n        // without risk of overflowing, e.g.:\n        //  (x) / 2 ** (112 - lg(1e18))\n        return uint256(self._x) / 5192296858534827;\n    }\n}\n\n// library with helper methods for oracles that are concerned with computing average prices\nlibrary UniswapV2OracleLibrary {\n    using FixedPoint for *;\n\n    // helper function that returns the current block timestamp within the range of uint32, i.e. [0, 2**32 - 1]\n    function currentBlockTimestamp() internal view returns (uint32) {\n        return uint32(block.timestamp % 2**32);\n    }\n\n    // produces the cumulative price using counterfactuals to save gas and avoid a call to sync.\n    function currentCumulativePrices(address pair)\n        internal\n        view\n        returns (\n            uint256 price0Cumulative,\n            uint256 price1Cumulative,\n            uint32 blockTimestamp\n        )\n    {\n        blockTimestamp = currentBlockTimestamp();\n        price0Cumulative = IUniswapV2Pair(pair).price0CumulativeLast();\n        price1Cumulative = IUniswapV2Pair(pair).price1CumulativeLast();\n\n        // if time has elapsed since the last update on the pair, mock the accumulated price values\n        (\n            uint112 reserve0,\n            uint112 reserve1,\n            uint32 blockTimestampLast\n        ) = IUniswapV2Pair(pair).getReserves();\n        if (blockTimestampLast != blockTimestamp) {\n            // subtraction overflow is desired\n            uint32 timeElapsed = blockTimestamp - blockTimestampLast;\n            // addition overflow is desired\n            // counterfactual\n            price0Cumulative +=\n                uint256(FixedPoint.fraction(reserve1, reserve0)._x) *\n                timeElapsed;\n            // counterfactual\n            price1Cumulative +=\n                uint256(FixedPoint.fraction(reserve0, reserve1)._x) *\n                timeElapsed;\n        }\n    }\n}\n"}, "contracts/mocks/MockUniswapRouter.sol": {"content": "pragma solidity 0.5.11;\n\nimport { IERC20 } from \"@openzeppelin/contracts/token/ERC20/IERC20.sol\";\n\nimport { IUniswapV2Router } from \"../interfaces/uniswap/IUniswapV2Router02.sol\";\nimport { Helpers } from \"../utils/Helpers.sol\";\nimport { StableMath } from \"../utils/StableMath.sol\";\n\ncontract MockUniswapRouter is IUniswapV2Router {\n    using StableMath for uint256;\n\n    address tok0;\n    address tok1;\n\n    address public WETH = address(0);\n\n    function initialize(address _token0, address _token1) public {\n        tok0 = _token0;\n        tok1 = _token1;\n    }\n\n    function swapExactTokensForTokens(\n        uint256 amountIn,\n        uint256 amountOutMin,\n        address[] calldata path,\n        address to,\n        uint256 deadline\n    ) external returns (uint256[] memory amounts) {\n        IERC20(tok0).transferFrom(msg.sender, address(this), amountIn);\n        IERC20(tok1).transfer(\n            to,\n            amountIn.scaleBy(\n                int8(Helpers.getDecimals(tok1) - Helpers.getDecimals(tok0))\n            )\n        );\n    }\n}\n"}, "contracts/mocks/MockUSDC.sol": {"content": "pragma solidity 0.5.11;\n\nimport \"./MintableERC20.sol\";\n\ncontract MockUSDC is MintableERC20 {\n    uint256 public constant decimals = 6;\n    string public constant symbol = \"USDC\";\n    string public constant name = \"USD Coin\";\n}\n"}, "contracts/mocks/MockUSDT.sol": {"content": "pragma solidity 0.5.11;\n\nimport \"./MintableERC20.sol\";\n\ncontract MockUSDT is MintableERC20 {\n    uint256 public constant decimals = 6;\n    string public constant symbol = \"USDT\";\n    string public constant name = \"USDT Coin\";\n}\n"}, "contracts/mocks/MockVault.sol": {"content": "pragma solidity 0.5.11;\n\nimport { VaultCore } from \"../vault/VaultCore.sol\";\nimport { VaultInitializer } from \"../vault/VaultInitializer.sol\";\n\ncontract MockVault is VaultCore, VaultInitializer {\n    uint256 storedTotalValue;\n\n    function setTotalValue(uint256 _totalValue) public {\n        storedTotalValue = _totalValue;\n    }\n\n    function totalValue() external view returns (uint256) {\n        return storedTotalValue;\n    }\n\n    function _totalValue() internal view returns (uint256) {\n        return storedTotalValue;\n    }\n}\n"}, "contracts/vault/VaultCore.sol": {"content": "pragma solidity 0.5.11;\n\n/**\n * @title OUSD Vault Contract\n * @notice The Vault contract stores assets. On a deposit, OUSD will be minted\n           and sent to the depositor. On a withdrawal, OUSD will be burned and\n           assets will be sent to the withdrawer. The Vault accepts deposits of\n           interest form yield bearing strategies which will modify the supply\n           of OUSD.\n * <AUTHOR> Protocol Inc\n */\n\nimport \"./VaultStorage.sol\";\nimport { IMinMaxOracle } from \"../interfaces/IMinMaxOracle.sol\";\nimport { IRebaseHooks } from \"../interfaces/IRebaseHooks.sol\";\n\ncontract VaultCore is VaultStorage {\n    /**\n     * @dev Verifies that the rebasing is not paused.\n     */\n    modifier whenNotRebasePaused() {\n        require(!rebasePaused, \"Rebasing paused\");\n        _;\n    }\n\n    /**\n     * @dev Verifies that the deposits are not paused.\n     */\n    modifier whenNotDepositPaused() {\n        require(!depositPaused, \"Deposits paused\");\n        _;\n    }\n\n    /**\n     * @dev Deposit a supported asset and mint OUSD.\n     * @param _asset Address of the asset being deposited\n     * @param _amount Amount of the asset being deposited\n     */\n    function mint(address _asset, uint256 _amount)\n        external\n        whenNotDepositPaused\n    {\n        require(assets[_asset].isSupported, \"Asset is not supported\");\n        require(_amount > 0, \"Amount must be greater than 0\");\n\n        uint256 price = IMinMaxOracle(priceProvider).priceMin(\n            Helpers.getSymbol(_asset)\n        );\n        if (price > 1e8) {\n            price = 1e8;\n        }\n        uint256 priceAdjustedDeposit = _amount.mulTruncateScale(\n            price.scaleBy(int8(10)), // 18-8 because oracles have 8 decimals precision\n            10**Helpers.getDecimals(_asset)\n        );\n\n        // Rebase must happen before any transfers occur.\n        if (priceAdjustedDeposit > rebaseThreshold && !rebasePaused) {\n            rebase(true);\n        }\n\n        // Transfer the deposited coins to the vault\n        IERC20 asset = IERC20(_asset);\n        asset.safeTransferFrom(msg.sender, address(this), _amount);\n\n        // Mint matching OUSD\n        oUSD.mint(msg.sender, priceAdjustedDeposit);\n        emit Mint(msg.sender, priceAdjustedDeposit);\n\n        if (priceAdjustedDeposit >= autoAllocateThreshold) {\n            allocate();\n        }\n    }\n\n    /**\n     * @dev Mint for multiple assets in the same call.\n     * @param _assets Addresses of assets being deposited\n     * @param _amounts Amount of each asset at the same index in the _assets\n     *                 to deposit.\n     */\n    function mintMultiple(\n        address[] calldata _assets,\n        uint256[] calldata _amounts\n    ) external whenNotDepositPaused {\n        require(_assets.length == _amounts.length, \"Parameter length mismatch\");\n\n        uint256 priceAdjustedTotal = 0;\n        uint256[] memory assetPrices = _getAssetPrices(false);\n        for (uint256 i = 0; i < allAssets.length; i++) {\n            for (uint256 j = 0; j < _assets.length; j++) {\n                if (_assets[j] == allAssets[i]) {\n                    if (_amounts[j] > 0) {\n                        uint256 assetDecimals = Helpers.getDecimals(\n                            allAssets[i]\n                        );\n                        uint256 price = assetPrices[i];\n                        if (price > 1e18) {\n                            price = 1e18;\n                        }\n                        priceAdjustedTotal += _amounts[j].mulTruncateScale(\n                            price,\n                            10**assetDecimals\n                        );\n                    }\n                }\n            }\n        }\n        // Rebase must happen before any transfers occur.\n        if (priceAdjustedTotal > rebaseThreshold && !rebasePaused) {\n            rebase(true);\n        }\n\n        for (uint256 i = 0; i < _assets.length; i++) {\n            IERC20 asset = IERC20(_assets[i]);\n            asset.safeTransferFrom(msg.sender, address(this), _amounts[i]);\n        }\n\n        oUSD.mint(msg.sender, priceAdjustedTotal);\n        emit Mint(msg.sender, priceAdjustedTotal);\n\n        if (priceAdjustedTotal >= autoAllocateThreshold) {\n            allocate();\n        }\n    }\n\n    /**\n     * @dev Withdraw a supported asset and burn OUSD.\n     * @param _amount Amount of OUSD to burn\n     */\n    function redeem(uint256 _amount) public {\n        if (_amount > rebaseThreshold && !rebasePaused) {\n            rebase(false);\n        }\n        _redeem(_amount);\n    }\n\n    function _redeem(uint256 _amount) internal {\n        require(_amount > 0, \"Amount must be greater than 0\");\n\n        // Calculate redemption outputs\n        uint256[] memory outputs = _calculateRedeemOutputs(_amount);\n        // Send outputs\n        for (uint256 i = 0; i < allAssets.length; i++) {\n            if (outputs[i] == 0) continue;\n\n            IERC20 asset = IERC20(allAssets[i]);\n\n            if (asset.balanceOf(address(this)) >= outputs[i]) {\n                // Use Vault funds first if sufficient\n                asset.safeTransfer(msg.sender, outputs[i]);\n            } else {\n                address strategyAddr = _selectWithdrawStrategyAddr(\n                    allAssets[i],\n                    outputs[i]\n                );\n\n                if (strategyAddr != address(0)) {\n                    // Nothing in Vault, but something in Strategy, send from there\n                    IStrategy strategy = IStrategy(strategyAddr);\n                    strategy.withdraw(msg.sender, allAssets[i], outputs[i]);\n                } else {\n                    // Cant find funds anywhere\n                    revert(\"Liquidity error\");\n                }\n            }\n        }\n\n        oUSD.burn(msg.sender, _amount);\n\n        // Until we can prove that we won't affect the prices of our assets\n        // by withdrawing them, this should be here.\n        // It's possible that a strategy was off on its asset total, perhaps\n        // a reward token sold for more or for less than anticipated.\n        if (_amount > rebaseThreshold && !rebasePaused) {\n            rebase(true);\n        }\n\n        emit Redeem(msg.sender, _amount);\n    }\n\n    /**\n     * @notice Withdraw a supported asset and burn all OUSD.\n     */\n    function redeemAll() external {\n        //unfortunately we have to do balanceOf twice\n        if (oUSD.balanceOf(msg.sender) > rebaseThreshold && !rebasePaused) {\n            rebase(false);\n        }\n        _redeem(oUSD.balanceOf(msg.sender));\n    }\n\n    /**\n     * @notice Allocate unallocated funds on Vault to strategies.\n     * @dev Allocate unallocated funds on Vault to strategies.\n     **/\n    function allocate() public {\n        _allocate();\n    }\n\n    /**\n     * @notice Allocate unallocated funds on Vault to strategies.\n     * @dev Allocate unallocated funds on Vault to strategies.\n     **/\n    function _allocate() internal {\n        uint256 vaultValue = _totalValueInVault();\n        // Nothing in vault to allocate\n        if (vaultValue == 0) return;\n        uint256 strategiesValue = _totalValueInStrategies();\n        // We have a method that does the same as this, gas optimisation\n        uint256 totalValue = vaultValue + strategiesValue;\n\n        // We want to maintain a buffer on the Vault so calculate a percentage\n        // modifier to multiply each amount being allocated by to enforce the\n        // vault buffer\n        uint256 vaultBufferModifier;\n        if (strategiesValue == 0) {\n            // Nothing in Strategies, allocate 100% minus the vault buffer to\n            // strategies\n            vaultBufferModifier = 1e18 - vaultBuffer;\n        } else {\n            vaultBufferModifier = vaultBuffer.mul(totalValue).div(vaultValue);\n            if (1e18 > vaultBufferModifier) {\n                // E.g. 1e18 - (1e17 * 10e18)/5e18 = 8e17\n                // (5e18 * 8e17) / 1e18 = 4e18 allocated from Vault\n                vaultBufferModifier = 1e18 - vaultBufferModifier;\n            } else {\n                // We need to let the buffer fill\n                return;\n            }\n        }\n        if (vaultBufferModifier == 0) return;\n\n        // Iterate over all assets in the Vault and allocate the the appropriate\n        // strategy\n        for (uint256 i = 0; i < allAssets.length; i++) {\n            IERC20 asset = IERC20(allAssets[i]);\n            uint256 assetBalance = asset.balanceOf(address(this));\n            // No balance, nothing to do here\n            if (assetBalance == 0) continue;\n\n            // Multiply the balance by the vault buffer modifier and truncate\n            // to the scale of the asset decimals\n            uint256 allocateAmount = assetBalance.mulTruncate(\n                vaultBufferModifier\n            );\n\n            // Get the target Strategy to maintain weightings\n            address depositStrategyAddr = _selectDepositStrategyAddr(\n                address(asset)\n            );\n\n            if (depositStrategyAddr != address(0) && allocateAmount > 0) {\n                IStrategy strategy = IStrategy(depositStrategyAddr);\n                // Transfer asset to Strategy and call deposit method to\n                // mint or take required action\n                asset.safeTransfer(address(strategy), allocateAmount);\n                strategy.deposit(address(asset), allocateAmount);\n            }\n        }\n    }\n\n    /**\n     * @dev Calculate the total value of assets held by the Vault and all\n     *         strategies and update the supply of oUSD\n     */\n    function rebase() public whenNotRebasePaused returns (uint256) {\n        rebase(true);\n    }\n\n    /**\n     * @dev Calculate the total value of assets held by the Vault and all\n     *         strategies and update the supply of oUSD\n     */\n    function rebase(bool sync) internal whenNotRebasePaused returns (uint256) {\n        if (oUSD.totalSupply() == 0) return 0;\n        uint256 oldTotalSupply = oUSD.totalSupply();\n        uint256 newTotalSupply = _totalValue();\n        // Only rachet upwards\n        if (newTotalSupply > oldTotalSupply) {\n            oUSD.changeSupply(newTotalSupply);\n            if (rebaseHooksAddr != address(0)) {\n                IRebaseHooks(rebaseHooksAddr).postRebase(sync);\n            }\n        }\n    }\n\n    /**\n     * @dev Determine the total value of assets held by the vault and its\n     *         strategies.\n     * @return uint256 value Total value in USD (1e18)\n     */\n    function totalValue() external view returns (uint256 value) {\n        value = _totalValue();\n    }\n\n    /**\n     * @dev Internal Calculate the total value of the assets held by the\n     *         vault and its strategies.\n     * @return uint256 value Total value in USD (1e18)\n     */\n    function _totalValue() internal view returns (uint256 value) {\n        return _totalValueInVault() + _totalValueInStrategies();\n    }\n\n    /**\n     * @dev Internal to calculate total value of all assets held in Vault.\n     * @return uint256 Total value in ETH (1e18)\n     */\n    function _totalValueInVault() internal view returns (uint256 value) {\n        value = 0;\n        for (uint256 y = 0; y < allAssets.length; y++) {\n            IERC20 asset = IERC20(allAssets[y]);\n            uint256 assetDecimals = Helpers.getDecimals(allAssets[y]);\n            uint256 balance = asset.balanceOf(address(this));\n            if (balance > 0) {\n                value += balance.scaleBy(int8(18 - assetDecimals));\n            }\n        }\n    }\n\n    /**\n     * @dev Internal to calculate total value of all assets held in Strategies.\n     * @return uint256 Total value in ETH (1e18)\n     */\n    function _totalValueInStrategies() internal view returns (uint256 value) {\n        value = 0;\n        for (uint256 i = 0; i < allStrategies.length; i++) {\n            value += _totalValueInStrategy(allStrategies[i]);\n        }\n    }\n\n    /**\n     * @dev Internal to calculate total value of all assets held by strategy.\n     * @param _strategyAddr Address of the strategy\n     * @return uint256 Total value in ETH (1e18)\n     */\n    function _totalValueInStrategy(address _strategyAddr)\n        internal\n        view\n        returns (uint256 value)\n    {\n        value = 0;\n        IStrategy strategy = IStrategy(_strategyAddr);\n        for (uint256 y = 0; y < allAssets.length; y++) {\n            uint256 assetDecimals = Helpers.getDecimals(allAssets[y]);\n            if (strategy.supportsAsset(allAssets[y])) {\n                uint256 balance = strategy.checkBalance(allAssets[y]);\n                if (balance > 0) {\n                    value += balance.scaleBy(int8(18 - assetDecimals));\n                }\n            }\n        }\n    }\n\n    /**\n     * @dev Calculate difference in percent of asset allocation for a\n               strategy.\n     * @param _strategyAddr Address of the strategy\n     * @return int256 Difference between current and target. 18 decimals. For ex. 10%=1e17.\n     */\n    function _strategyWeightDifference(address _strategyAddr)\n        internal\n        view\n        returns (int256 difference)\n    {\n        difference =\n            int256(strategies[_strategyAddr].targetWeight) -\n            int256(\n                _totalValueInStrategy(_strategyAddr).divPrecisely(_totalValue())\n            );\n    }\n\n    /**\n     * @dev Select a strategy for allocating an asset to.\n     * @param _asset Address of asset\n     * @return address Address of the target strategy\n     */\n    function _selectDepositStrategyAddr(address _asset)\n        internal\n        view\n        returns (address depositStrategyAddr)\n    {\n        depositStrategyAddr = address(0);\n        int256 maxDifference = 0;\n        for (uint256 i = 0; i < allStrategies.length; i++) {\n            IStrategy strategy = IStrategy(allStrategies[i]);\n            if (strategy.supportsAsset(_asset)) {\n                int256 diff = _strategyWeightDifference(allStrategies[i]);\n                if (diff >= maxDifference) {\n                    maxDifference = diff;\n                    depositStrategyAddr = allStrategies[i];\n                }\n            }\n        }\n    }\n\n    /**\n     * @dev Select a strategy for withdrawing an asset from.\n     * @param _asset Address of asset\n     * @return address Address of the target strategy for withdrawal\n     */\n    function _selectWithdrawStrategyAddr(address _asset, uint256 _amount)\n        internal\n        view\n        returns (address withdrawStrategyAddr)\n    {\n        withdrawStrategyAddr = address(0);\n        int256 minDifference = 1e18;\n\n        for (uint256 i = 0; i < allStrategies.length; i++) {\n            IStrategy strategy = IStrategy(allStrategies[i]);\n            if (\n                strategy.supportsAsset(_asset) &&\n                strategy.checkBalance(_asset) > _amount\n            ) {\n                int256 diff = _strategyWeightDifference(allStrategies[i]);\n                if (diff <= minDifference) {\n                    minDifference = diff;\n                    withdrawStrategyAddr = allStrategies[i];\n                }\n            }\n        }\n    }\n\n    /**\n     * @notice Get the balance of an asset held in Vault and all strategies.\n     * @param _asset Address of asset\n     * @return uint256 Balance of asset in decimals of asset\n     */\n    function checkBalance(address _asset) external view returns (uint256) {\n        return _checkBalance(_asset);\n    }\n\n    /**\n     * @notice Get the balance of an asset held in Vault and all strategies.\n     * @param _asset Address of asset\n     * @return uint256 Balance of asset in decimals of asset\n     */\n    function _checkBalance(address _asset)\n        internal\n        view\n        returns (uint256 balance)\n    {\n        IERC20 asset = IERC20(_asset);\n        balance = asset.balanceOf(address(this));\n        for (uint256 i = 0; i < allStrategies.length; i++) {\n            IStrategy strategy = IStrategy(allStrategies[i]);\n            if (strategy.supportsAsset(_asset)) {\n                balance += strategy.checkBalance(_asset);\n            }\n        }\n    }\n\n    /**\n     * @notice Get the balance of all assets held in Vault and all strategies.\n     * @return uint256 Balance of all assets (1e18)\n     */\n    function _checkBalance() internal view returns (uint256 balance) {\n        balance = 0;\n        for (uint256 i = 0; i < allAssets.length; i++) {\n            uint256 assetDecimals = Helpers.getDecimals(allAssets[i]);\n            balance += _checkBalance(allAssets[i]).scaleBy(\n                int8(18 - assetDecimals)\n            );\n        }\n    }\n\n    /**\n     * @notice Calculate the outputs for a redeem function, i.e. the mix of\n     * coins that will be returned\n     */\n    function calculateRedeemOutputs(uint256 _amount)\n        external\n        returns (uint256[] memory)\n    {\n        return _calculateRedeemOutputs(_amount);\n    }\n\n    /**\n     * @notice Calculate the outputs for a redeem function, i.e. the mix of\n     * coins that will be returned.\n     * @return Array of amounts respective to the supported assets\n     */\n    function _calculateRedeemOutputs(uint256 _amount)\n        internal\n        returns (uint256[] memory outputs)\n    {\n        // We always give out coins in proportion to how many we have,\n        // Now if all coins were the same value, this math would easy,\n        // just take the percentage of each coin, and multiply by the\n        // value to be given out. But if coins are worth more than $1,\n        // then we would end up handing out too many coins. We need to\n        // adjust by the total value of coins.\n        //\n        // To do this, we total up the value of our coins, by their\n        // percentages. Then divide what we would otherwise give out by\n        // this number.\n        //\n        // Let say we have 100 DAI at $1.06  and 200 USDT at $1.00.\n        // So for every 1 DAI we give out, we'll be handing out 2 USDT\n        // Our total output ratio is: 33% * 1.06 + 66% * 1.00 = 1.02\n        //\n        // So when calculating the output, we take the percentage of\n        // each coin, times the desired output value, divided by the\n        // totalOutputRatio.\n        //\n        // For example, withdrawing: 30 OUSD:\n        // DAI 33% * 30 / 1.02 = 9.80 DAI\n        // USDT = 66 % * 30 / 1.02 = 19.60 USDT\n        //\n        // Checking these numbers:\n        // 9.80 DAI * 1.06 = $10.40\n        // 19.60 USDT * 1.00 = $19.60\n        //\n        // And so the user gets $10.40 + $19.60 = $30 worth of value.\n\n        uint256 assetCount = getAssetCount();\n        uint256[] memory assetPrices = _getAssetPrices(true);\n        uint256[] memory assetBalances = new uint256[](assetCount);\n        uint256[] memory assetDecimals = new uint256[](assetCount);\n        uint256 totalBalance = 0;\n        uint256 totalOutputRatio = 0;\n        outputs = new uint256[](assetCount);\n\n        // Calculate redeem fee\n        if (redeemFeeBps > 0) {\n            uint256 redeemFee = _amount.mul(redeemFeeBps).div(10000);\n            _amount = _amount.sub(redeemFee);\n        }\n\n        // Calculate assets balances and decimals once,\n        // for a large gas savings.\n        for (uint256 i = 0; i < allAssets.length; i++) {\n            uint256 balance = _checkBalance(allAssets[i]);\n            uint256 decimals = Helpers.getDecimals(allAssets[i]);\n            assetBalances[i] = balance;\n            assetDecimals[i] = decimals;\n            totalBalance += balance.scaleBy(int8(18 - decimals));\n        }\n        // Calculate totalOutputRatio\n        for (uint256 i = 0; i < allAssets.length; i++) {\n            uint256 price = assetPrices[i];\n            // Never give out more than one\n            // stablecoin per dollar of OUSD\n            if (price < 1e18) {\n                price = 1e18;\n            }\n            uint256 ratio = assetBalances[i]\n                .scaleBy(int8(18 - assetDecimals[i]))\n                .mul(price)\n                .div(totalBalance);\n            totalOutputRatio += ratio;\n        }\n        // Calculate final outputs\n        uint256 factor = _amount.divPrecisely(totalOutputRatio);\n        for (uint256 i = 0; i < allAssets.length; i++) {\n            outputs[i] = assetBalances[i].mul(factor).div(totalBalance);\n        }\n    }\n\n    /**\n     * @notice Get an array of the supported asset prices in USD.\n     * @return uint256[] Array of asset prices in USD (1e18)\n     */\n    function _getAssetPrices(bool useMax)\n        internal\n        returns (uint256[] memory assetPrices)\n    {\n        assetPrices = new uint256[](getAssetCount());\n\n        IMinMaxOracle oracle = IMinMaxOracle(priceProvider);\n        // Price from Oracle is returned with 8 decimals\n        // _amount is in assetDecimals\n\n        for (uint256 i = 0; i < allAssets.length; i++) {\n            string memory symbol = Helpers.getSymbol(allAssets[i]);\n            // Get all the USD prices of the asset in 1e18\n            if (useMax) {\n                assetPrices[i] = oracle.priceMax(symbol).scaleBy(int8(18 - 8));\n            } else {\n                assetPrices[i] = oracle.priceMin(symbol).scaleBy(int8(18 - 8));\n            }\n        }\n    }\n\n    /***************************************\n                    Utils\n    ****************************************/\n\n    /**\n     * @dev Return the number of assets suppported by the Vault.\n     */\n    function getAssetCount() public view returns (uint256) {\n        return allAssets.length;\n    }\n\n    /**\n     * @dev Return all asset addresses in order\n     */\n    function getAllAssets() external view returns (address[] memory) {\n        return allAssets;\n    }\n\n    /**\n     * @dev Return the number of strategies active on the Vault.\n     */\n    function getStrategyCount() public view returns (uint256) {\n        return allStrategies.length;\n    }\n\n    function isSupportedAsset(address _asset) external view returns (bool) {\n        return assets[_asset].isSupported;\n    }\n\n    /**\n     * @dev Falldown to the admin implementation\n     * @notice This is a catch all for all functions not declared in core\n     */\n    function() external payable {\n        bytes32 slot = adminImplPosition;\n        assembly {\n            // Copy msg.data. We take full control of memory in this inline assembly\n            // block because it will not return to Solidity code. We overwrite the\n            // Solidity scratch pad at memory position 0.\n            calldatacopy(0, 0, calldatasize)\n\n            // Call the implementation.\n            // out and outsize are 0 because we don't know the size yet.\n            let result := delegatecall(gas, sload(slot), 0, calldatasize, 0, 0)\n\n            // Copy the returned data.\n            returndatacopy(0, 0, returndatasize)\n\n            switch result\n                // delegatecall returns 0 on error.\n                case 0 {\n                    revert(0, returndatasize)\n                }\n                default {\n                    return(0, returndatasize)\n                }\n        }\n    }\n}\n"}, "contracts/vault/VaultStorage.sol": {"content": "pragma solidity 0.5.11;\n\n/**\n * @title OUSD VaultStorage Contract\n * @notice The VaultStorage contract defines the storage for the Vault contracts\n * <AUTHOR> Protocol Inc\n */\n\nimport { IERC20 } from \"@openzeppelin/contracts/token/ERC20/IERC20.sol\";\nimport { SafeERC20 } from \"@openzeppelin/contracts/token/ERC20/SafeERC20.sol\";\nimport { SafeMath } from \"@openzeppelin/contracts/math/SafeMath.sol\";\nimport {\n    Initializable\n} from \"@openzeppelin/upgrades/contracts/Initializable.sol\";\n\nimport { IStrategy } from \"../interfaces/IStrategy.sol\";\nimport { Governable } from \"../governance/Governable.sol\";\nimport { OUSD } from \"../token/OUSD.sol\";\nimport \"../utils/Helpers.sol\";\nimport { StableMath } from \"../utils/StableMath.sol\";\n\ncontract VaultStorage is Initializable, Governable {\n    using SafeMath for uint256;\n    using StableMath for uint256;\n    using SafeMath for int256;\n    using SafeERC20 for IERC20;\n\n    event AssetSupported(address _asset);\n    event StrategyAdded(address _addr);\n    event StrategyRemoved(address _addr);\n    event Mint(address _addr, uint256 _value);\n    event Redeem(address _addr, uint256 _value);\n    event StrategyWeightsUpdated(\n        address[] _strategyAddresses,\n        uint256[] weights\n    );\n    event DepositsPaused();\n    event DepositsUnpaused();\n\n    // Assets supported by the Vault, i.e. Stablecoins\n    struct Asset {\n        bool isSupported;\n    }\n    mapping(address => Asset) assets;\n    address[] allAssets;\n\n    // Strategies supported by the Vault\n    struct Strategy {\n        bool isSupported;\n        uint256 targetWeight; // 18 decimals. 100% = 1e18\n    }\n    mapping(address => Strategy) strategies;\n    address[] allStrategies;\n\n    // Address of the Oracle price provider contract\n    address public priceProvider;\n    // Pausing bools\n    bool public rebasePaused = false;\n    bool public depositPaused = true;\n    // Redemption fee in basis points\n    uint256 public redeemFeeBps;\n    // Buffer of assets to keep in Vault to handle (most) withdrawals\n    uint256 public vaultBuffer;\n    // Mints over this amount automatically allocate funds. 18 decimals.\n    uint256 public autoAllocateThreshold;\n    // Mints over this amount automatically rebase. 18 decimals.\n    uint256 public rebaseThreshold;\n\n    OUSD oUSD;\n\n    //keccak256(\"OUSD.vault.governor.admin.impl\");\n    bytes32 constant adminImplPosition = 0xa2bd3d3cf188a41358c8b401076eb59066b09dec5775650c0de4c55187d17bd9;\n\n    // Address of the contract responsible for post rebase syncs with AMMs\n    address public rebaseHooksAddr = address(0);\n\n    // Address of Uniswap\n    address public uniswapAddr = address(0);\n\n    /**\n     * @dev set the implementation for the admin, this needs to be in a base class else we cannot set it\n     * @param newImpl address pf the implementation\n     */\n    function setAdminImpl(address newImpl) external onlyGovernor {\n        bytes32 position = adminImplPosition;\n        assembly {\n            sstore(position, newImpl)\n        }\n    }\n}\n"}, "@openzeppelin/contracts/token/ERC20/SafeERC20.sol": {"content": "pragma solidity ^0.5.0;\n\nimport \"./IERC20.sol\";\nimport \"../../math/SafeMath.sol\";\nimport \"../../utils/Address.sol\";\n\n/**\n * @title SafeERC20\n * @dev Wrappers around ERC20 operations that throw on failure (when the token\n * contract returns false). Tokens that return no value (and instead revert or\n * throw on failure) are also supported, non-reverting calls are assumed to be\n * successful.\n * To use this library you can add a `using SafeERC20 for ERC20;` statement to your contract,\n * which allows you to call the safe operations as `token.safeTransfer(...)`, etc.\n */\nlibrary SafeERC20 {\n    using SafeMath for uint256;\n    using Address for address;\n\n    function safeTransfer(IERC20 token, address to, uint256 value) internal {\n        callOptionalReturn(token, abi.encodeWithSelector(token.transfer.selector, to, value));\n    }\n\n    function safeTransferFrom(IERC20 token, address from, address to, uint256 value) internal {\n        callOptionalReturn(token, abi.encodeWithSelector(token.transferFrom.selector, from, to, value));\n    }\n\n    function safeApprove(IERC20 token, address spender, uint256 value) internal {\n        // safeApprove should only be called when setting an initial allowance,\n        // or when resetting it to zero. To increase and decrease it, use\n        // 'safeIncreaseAllowance' and 'safeDecreaseAllowance'\n        // solhint-disable-next-line max-line-length\n        require((value == 0) || (token.allowance(address(this), spender) == 0),\n            \"SafeERC20: approve from non-zero to non-zero allowance\"\n        );\n        callOptionalReturn(token, abi.encodeWithSelector(token.approve.selector, spender, value));\n    }\n\n    function safeIncreaseAllowance(IERC20 token, address spender, uint256 value) internal {\n        uint256 newAllowance = token.allowance(address(this), spender).add(value);\n        callOptionalReturn(token, abi.encodeWithSelector(token.approve.selector, spender, newAllowance));\n    }\n\n    function safeDecreaseAllowance(IERC20 token, address spender, uint256 value) internal {\n        uint256 newAllowance = token.allowance(address(this), spender).sub(value, \"SafeERC20: decreased allowance below zero\");\n        callOptionalReturn(token, abi.encodeWithSelector(token.approve.selector, spender, newAllowance));\n    }\n\n    /**\n     * @dev Imitates a Solidity high-level call (i.e. a regular function call to a contract), relaxing the requirement\n     * on the return value: the return value is optional (but if data is returned, it must not be false).\n     * @param token The token targeted by the call.\n     * @param data The call data (encoded using abi.encode or one of its variants).\n     */\n    function callOptionalReturn(IERC20 token, bytes memory data) private {\n        // We need to perform a low level call here, to bypass Solidity's return data size checking mechanism, since\n        // we're implementing it ourselves.\n\n        // A Solidity high level call has three parts:\n        //  1. The target address is checked to verify it contains contract code\n        //  2. The call itself is made, and success asserted\n        //  3. The return value is decoded, which in turn checks the size of the returned data.\n        // solhint-disable-next-line max-line-length\n        require(address(token).isContract(), \"SafeERC20: call to non-contract\");\n\n        // solhint-disable-next-line avoid-low-level-calls\n        (bool success, bytes memory returndata) = address(token).call(data);\n        require(success, \"SafeERC20: low-level call failed\");\n\n        if (returndata.length > 0) { // Return data is optional\n            // solhint-disable-next-line max-line-length\n            require(abi.decode(returndata, (bool)), \"SafeERC20: ERC20 operation did not succeed\");\n        }\n    }\n}\n"}, "@openzeppelin/contracts/utils/Address.sol": {"content": "pragma solidity ^0.5.5;\n\n/**\n * @dev Collection of functions related to the address type\n */\nlibrary Address {\n    /**\n     * @dev Returns true if `account` is a contract.\n     *\n     * [IMPORTANT]\n     * ====\n     * It is unsafe to assume that an address for which this function returns\n     * false is an externally-owned account (EOA) and not a contract.\n     *\n     * Among others, `isContract` will return false for the following \n     * types of addresses:\n     *\n     *  - an externally-owned account\n     *  - a contract in construction\n     *  - an address where a contract will be created\n     *  - an address where a contract lived, but was destroyed\n     * ====\n     */\n    function isContract(address account) internal view returns (bool) {\n        // According to EIP-1052, 0x0 is the value returned for not-yet created accounts\n        // and 0xc5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470 is returned\n        // for accounts without code, i.e. `keccak256('')`\n        bytes32 codehash;\n        bytes32 accountHash = 0xc5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470;\n        // solhint-disable-next-line no-inline-assembly\n        assembly { codehash := extcodehash(account) }\n        return (codehash != accountHash && codehash != 0x0);\n    }\n\n    /**\n     * @dev Converts an `address` into `address payable`. Note that this is\n     * simply a type cast: the actual underlying value is not changed.\n     *\n     * _Available since v2.4.0._\n     */\n    function toPayable(address account) internal pure returns (address payable) {\n        return address(uint160(account));\n    }\n\n    /**\n     * @dev Replacement for Solidity's `transfer`: sends `amount` wei to\n     * `recipient`, forwarding all available gas and reverting on errors.\n     *\n     * https://eips.ethereum.org/EIPS/eip-1884[EIP1884] increases the gas cost\n     * of certain opcodes, possibly making contracts go over the 2300 gas limit\n     * imposed by `transfer`, making them unable to receive funds via\n     * `transfer`. {sendValue} removes this limitation.\n     *\n     * https://diligence.consensys.net/posts/2019/09/stop-using-soliditys-transfer-now/[Learn more].\n     *\n     * IMPORTANT: because control is transferred to `recipient`, care must be\n     * taken to not create reentrancy vulnerabilities. Consider using\n     * {ReentrancyGuard} or the\n     * https://solidity.readthedocs.io/en/v0.5.11/security-considerations.html#use-the-checks-effects-interactions-pattern[checks-effects-interactions pattern].\n     *\n     * _Available since v2.4.0._\n     */\n    function sendValue(address payable recipient, uint256 amount) internal {\n        require(address(this).balance >= amount, \"Address: insufficient balance\");\n\n        // solhint-disable-next-line avoid-call-value\n        (bool success, ) = recipient.call.value(amount)(\"\");\n        require(success, \"Address: unable to send value, recipient may have reverted\");\n    }\n}\n"}, "contracts/token/OUSD.sol": {"content": "pragma solidity 0.5.11;\n\n/**\n * @title OUSD Token Contract\n * @notice ERC20 compatible contract for OUSD\n * @dev Implements an elastic supply\n * <AUTHOR> Protocol Inc\n */\nimport { SafeMath } from \"@openzeppelin/contracts/math/SafeMath.sol\";\nimport {\n    Initializable\n} from \"@openzeppelin/upgrades/contracts/Initializable.sol\";\n\nimport { InitializableToken } from \"../utils/InitializableToken.sol\";\nimport \"../utils/StableMath.sol\";\nimport { Governable } from \"../governance/Governable.sol\";\n\ncontract OUSD is Initializable, InitializableToken, Governable {\n    using SafeMath for uint256;\n    using StableMath for uint256;\n\n    event TotalSupplyUpdated(\n        uint256 totalSupply,\n        uint256 totalCredits,\n        uint256 creditsPerToken\n    );\n\n    uint256 private constant MAX_SUPPLY = ~uint128(0); // (2^128) - 1\n\n    uint256 private _totalSupply;\n    uint256 private totalCredits;\n    // Exchange rate between internal credits and OUSD\n    uint256 private creditsPerToken;\n\n    mapping(address => uint256) private _creditBalances;\n\n    // Allowances denominated in OUSD\n    mapping(address => mapping(address => uint256)) private _allowances;\n\n    address public vaultAddress = address(0);\n\n    function initialize(\n        string calldata _nameArg,\n        string calldata _symbolArg,\n        address _vaultAddress\n    ) external onlyGovernor initializer {\n        InitializableToken._initialize(_nameArg, _symbolArg);\n\n        _totalSupply = 0;\n        totalCredits = 0;\n        creditsPerToken = 1e18;\n\n        vaultAddress = _vaultAddress;\n    }\n\n    /**\n     * @dev Verifies that the caller is the Savings Manager contract\n     */\n    modifier onlyVault() {\n        require(vaultAddress == msg.sender, \"Caller is not the Vault\");\n        _;\n    }\n\n    /**\n     * @return The total supply of OUSD.\n     */\n    function totalSupply() public view returns (uint256) {\n        return _totalSupply;\n    }\n\n    /**\n     * @dev Gets the balance of the specified address.\n     * @param _account The address to query the balance of.\n     * @return A unit256 representing the _amount of base units owned by the\n     *         specified address.\n     */\n    function balanceOf(address _account) public view returns (uint256) {\n        if (creditsPerToken == 0) return 0;\n        return _creditBalances[_account].divPrecisely(creditsPerToken);\n    }\n\n    /**\n     * @dev Gets the credits balance of the specified address.\n     * @param _account The address to query the balance of.\n     * @return A uint256 representing the _amount of base units owned by the\n     *         specified address.\n     */\n    function creditsBalanceOf(address _account) public view returns (uint256) {\n        return _creditBalances[_account];\n    }\n\n    /**\n     * @dev Transfer tokens to a specified address.\n     * @param _to the address to transfer to.\n     * @param _value the _amount to be transferred.\n     * @return true on success.\n     */\n    function transfer(address _to, uint256 _value) public returns (bool) {\n        uint256 creditValue = _removeCredits(msg.sender, _value);\n        _creditBalances[_to] = _creditBalances[_to].add(creditValue);\n\n        emit Transfer(msg.sender, _to, _value);\n\n        return true;\n    }\n\n    /**\n     * @dev Transfer tokens from one address to another.\n     * @param _from The address you want to send tokens from.\n     * @param _to The address you want to transfer to.\n     * @param _value The _amount of tokens to be transferred.\n     */\n    function transferFrom(\n        address _from,\n        address _to,\n        uint256 _value\n    ) public returns (bool) {\n        _allowances[_from][msg.sender] = _allowances[_from][msg.sender].sub(\n            _value\n        );\n\n        uint256 creditValue = _removeCredits(_from, _value);\n        _creditBalances[_to] = _creditBalances[_to].add(creditValue);\n\n        emit Transfer(_from, _to, _value);\n\n        return true;\n    }\n\n    /**\n     * @dev Function to check the _amount of tokens that an owner has allowed to a _spender.\n     * @param _owner The address which owns the funds.\n     * @param _spender The address which will spend the funds.\n     * @return The number of tokens still available for the _spender.\n     */\n    function allowance(address _owner, address _spender)\n        public\n        view\n        returns (uint256)\n    {\n        return _allowances[_owner][_spender];\n    }\n\n    /**\n     * @dev Approve the passed address to spend the specified _amount of tokens on behalf of\n     * msg.sender. This method is included for ERC20 compatibility.\n     * increaseAllowance and decreaseAllowance should be used instead.\n     * Changing an allowance with this method brings the risk that someone may transfer both\n     * the old and the new allowance - if they are both greater than zero - if a transfer\n     * transaction is mined before the later approve() call is mined.\n     *\n     * @param _spender The address which will spend the funds.\n     * @param _value The _amount of tokens to be spent.\n     */\n    function approve(address _spender, uint256 _value) public returns (bool) {\n        _allowances[msg.sender][_spender] = _value;\n        emit Approval(msg.sender, _spender, _value);\n        return true;\n    }\n\n    /**\n     * @dev Increase the _amount of tokens that an owner has allowed to a _spender.\n     * This method should be used instead of approve() to avoid the double approval vulnerability\n     * described above.\n     * @param _spender The address which will spend the funds.\n     * @param _addedValue The _amount of tokens to increase the allowance by.\n     */\n    function increaseAllowance(address _spender, uint256 _addedValue)\n        public\n        returns (bool)\n    {\n        _allowances[msg.sender][_spender] = _allowances[msg.sender][_spender]\n            .add(_addedValue);\n        emit Approval(msg.sender, _spender, _allowances[msg.sender][_spender]);\n        return true;\n    }\n\n    /**\n     * @dev Decrease the _amount of tokens that an owner has allowed to a _spender.\n     * @param _spender The address which will spend the funds.\n     * @param _subtractedValue The _amount of tokens to decrease the allowance by.\n     */\n    function decreaseAllowance(address _spender, uint256 _subtractedValue)\n        public\n        returns (bool)\n    {\n        uint256 oldValue = _allowances[msg.sender][_spender];\n        if (_subtractedValue >= oldValue) {\n            _allowances[msg.sender][_spender] = 0;\n        } else {\n            _allowances[msg.sender][_spender] = oldValue.sub(_subtractedValue);\n        }\n        emit Approval(msg.sender, _spender, _allowances[msg.sender][_spender]);\n        return true;\n    }\n\n    /**\n     * @notice Mints new tokens, increasing totalSupply.\n     */\n    function mint(address _account, uint256 _amount) external onlyVault {\n        return _mint(_account, _amount);\n    }\n\n    /**\n     * @dev Creates `_amount` tokens and assigns them to `_account`, increasing\n     * the total supply.\n     *\n     * Emits a {Transfer} event with `from` set to the zero address.\n     *\n     * Requirements\n     *\n     * - `to` cannot be the zero address.\n     */\n    function _mint(address _account, uint256 _amount) internal {\n        require(_account != address(0), \"Mint to the zero address\");\n\n        _totalSupply = _totalSupply.add(_amount);\n\n        uint256 creditAmount = _amount.mulTruncate(creditsPerToken);\n        _creditBalances[_account] = _creditBalances[_account].add(creditAmount);\n        totalCredits = totalCredits.add(creditAmount);\n\n        emit Transfer(address(0), _account, _amount);\n    }\n\n    /**\n     * @notice Burns tokens, decreasing totalSupply.\n     */\n    function burn(address account, uint256 amount) external onlyVault {\n        return _burn(account, amount);\n    }\n\n    /**\n     * @dev Destroys `_amount` tokens from `_account`, reducing the\n     * total supply.\n     *\n     * Emits a {Transfer} event with `to` set to the zero address.\n     *\n     * Requirements\n     *\n     * - `_account` cannot be the zero address.\n     * - `_account` must have at least `_amount` tokens.\n     */\n    function _burn(address _account, uint256 _amount) internal {\n        require(_account != address(0), \"Burn from the zero address\");\n\n        _totalSupply = _totalSupply.sub(_amount);\n        uint256 creditAmount = _removeCredits(_account, _amount);\n        totalCredits = totalCredits.sub(creditAmount);\n\n        emit Transfer(_account, address(0), _amount);\n    }\n\n    /**\n     * @dev Removes credits from a credit balance and burns rounding errors.\n     * @param _account Account to remove credits from\n     * @param _amount Amount in OUSD which will be converted to credits and\n     *                removed\n     */\n    function _removeCredits(address _account, uint256 _amount)\n        internal\n        returns (uint256 creditAmount)\n    {\n        creditAmount = _amount.mulTruncate(creditsPerToken);\n        uint256 currentCredits = _creditBalances[_account];\n        if (\n            currentCredits == creditAmount || currentCredits - 1 == creditAmount\n        ) {\n            _creditBalances[_account] = 0;\n        } else if (currentCredits > creditAmount) {\n            _creditBalances[_account] = currentCredits - creditAmount;\n        } else {\n            revert(\"Remove exceeds balance\");\n        }\n    }\n\n    /**\n     * @dev Modify the supply without minting new tokens. This uses a change in\n     *      the exchange rate between \"credits\" and OUSD tokens to change balances.\n     * @param _newTotalSupply New total supply of OUSD.\n     * @return uint256 representing the new total supply.\n     */\n    function changeSupply(uint256 _newTotalSupply)\n        external\n        onlyVault\n        returns (uint256)\n    {\n        require(_totalSupply > 0, \"Cannot increase 0 supply\");\n\n        if (_totalSupply == _newTotalSupply) {\n            emit TotalSupplyUpdated(\n                _totalSupply,\n                totalCredits,\n                creditsPerToken\n            );\n            return _totalSupply;\n        }\n\n        _totalSupply = _newTotalSupply;\n\n        if (_totalSupply > MAX_SUPPLY) _totalSupply = MAX_SUPPLY;\n\n        creditsPerToken = totalCredits.divPrecisely(_totalSupply);\n\n        emit TotalSupplyUpdated(_totalSupply, totalCredits, creditsPerToken);\n        return _totalSupply;\n    }\n}\n"}, "contracts/utils/InitializableToken.sol": {"content": "pragma solidity 0.5.11;\n\nimport { ERC20 } from \"@openzeppelin/contracts/token/ERC20/ERC20.sol\";\nimport { InitializableERC20Detailed } from \"./InitializableERC20Detailed.sol\";\n\ncontract InitializableToken is ERC20, InitializableERC20Detailed {\n    /**\n     * @dev Initialization function for implementing contract\n     * @notice To avoid variable shadowing appended `Arg` after arguments name.\n     */\n    function _initialize(string memory _nameArg, string memory _symbolArg)\n        internal\n    {\n        InitializableERC20Detailed._initialize(_nameArg, _symbolArg, 18);\n    }\n}\n"}, "contracts/utils/InitializableERC20Detailed.sol": {"content": "pragma solidity 0.5.11;\n\nimport { IERC20 } from \"@openzeppelin/contracts/token/ERC20/IERC20.sol\";\n\n/**\n * @dev Optional functions from the ERC20 standard.\n * Converted from openzeppelin/contracts/token/ERC20/ERC20Detailed.sol\n */\ncontract InitializableERC20Detailed is IERC20 {\n    string private _name;\n    string private _symbol;\n    uint8 private _decimals;\n\n    /**\n     * @dev Sets the values for `name`, `symbol`, and `decimals`. All three of\n     * these values are immutable: they can only be set once during\n     * construction.\n     * @notice To avoid variable shadowing appended `Arg` after arguments name.\n     */\n    function _initialize(\n        string memory nameArg,\n        string memory symbolArg,\n        uint8 decimalsArg\n    ) internal {\n        _name = nameArg;\n        _symbol = symbolArg;\n        _decimals = decimalsArg;\n    }\n\n    /**\n     * @dev Returns the name of the token.\n     */\n    function name() public view returns (string memory) {\n        return _name;\n    }\n\n    /**\n     * @dev Returns the symbol of the token, usually a shorter version of the\n     * name.\n     */\n    function symbol() public view returns (string memory) {\n        return _symbol;\n    }\n\n    /**\n     * @dev Returns the number of decimals used to get its user representation.\n     * For example, if `decimals` equals `2`, a balance of `505` tokens should\n     * be displayed to a user as `5,05` (`505 / 10 ** 2`).\n     *\n     * Tokens usually opt for a value of 18, imitating the relationship between\n     * Ether and Wei.\n     *\n     * NOTE: This information is only used for _display_ purposes: it in\n     * no way affects any of the arithmetic of the contract, including\n     * {IERC20-balanceOf} and {IERC20-transfer}.\n     */\n    function decimals() public view returns (uint8) {\n        return _decimals;\n    }\n}\n"}, "contracts/vault/VaultInitializer.sol": {"content": "pragma solidity 0.5.11;\n\n/**\n * @title OUSD VaultInitializer Contract\n * @notice The Vault contract initializes the vault.\n * <AUTHOR> Protocol Inc\n */\n\nimport \"./VaultStorage.sol\";\n\ncontract VaultInitializer is VaultStorage {\n    function initialize(address _priceProvider, address _ousd)\n        external\n        onlyGovernor\n        initializer\n    {\n        require(_priceProvider != address(0), \"PriceProvider address is zero\");\n        require(_ousd != address(0), \"oUSD address is zero\");\n\n        oUSD = OUSD(_ousd);\n\n        priceProvider = _priceProvider;\n\n        rebasePaused = false;\n        depositPaused = true;\n\n        // Initial redeem fee of 0 basis points\n        redeemFeeBps = 0;\n        // Initial Vault buffer of 0%\n        vaultBuffer = 0;\n        // Initial allocate threshold of 25,000 OUSD\n        autoAllocateThreshold = 25000e18;\n        // Threshold for rebasing\n        rebaseThreshold = 1000e18;\n    }\n}\n"}, "contracts/mocks/MockWETH.sol": {"content": "pragma solidity 0.5.11;\n\nimport \"./MintableERC20.sol\";\n\ncontract MockWETH is MintableERC20 {\n    uint256 public constant decimals = 18;\n    string public constant symbol = \"WETH\";\n    string public constant name = \"WETH\";\n}\n"}, "contracts/oracle/ChainlinkOracle.sol": {"content": "pragma solidity 0.5.11;\n\n/**\n * @title OUSD ChainlinkOracle Contract\n * <AUTHOR> Protocol Inc\n */\nimport \"./AggregatorV3Interface.sol\";\nimport { IEthUsdOracle } from \"../interfaces/IEthUsdOracle.sol\";\nimport {\n    InitializableGovernable\n} from \"../governance/InitializableGovernable.sol\";\n\ncontract ChainlinkOracle is IEthUsdOracle, InitializableGovernable {\n    address ethFeed;\n\n    struct FeedConfig {\n        address feed;\n        uint8 decimals;\n        bool directToUsd;\n    }\n\n    mapping(bytes32 => FeedConfig) feeds;\n\n    uint8 ethDecimals;\n\n    string constant ethSymbol = \"ETH\";\n    bytes32 constant ethHash = keccak256(abi.encodePacked(ethSymbol));\n\n    constructor(address ethFeed_) public {\n        ethFeed = ethFeed_;\n        ethDecimals = AggregatorV3Interface(ethFeed_).decimals();\n    }\n\n    function registerFeed(\n        address feed,\n        string memory symbol,\n        bool directToUsd\n    ) public onlyGovernor {\n        FeedConfig storage config = feeds[keccak256(abi.encodePacked(symbol))];\n\n        config.feed = feed;\n        config.decimals = AggregatorV3Interface(feed).decimals();\n        config.directToUsd = directToUsd;\n    }\n\n    function getLatestPrice(address feed) internal view returns (int256) {\n        (\n            uint80 roundID,\n            int256 price,\n            uint256 startedAt,\n            uint256 timeStamp,\n            uint80 answeredInRound\n        ) = AggregatorV3Interface(feed).latestRoundData();\n        // silence\n        roundID;\n        startedAt;\n        timeStamp;\n        answeredInRound;\n        return price;\n    }\n\n    function ethUsdPrice() external view returns (uint256) {\n        return (uint256(getLatestPrice(ethFeed)) /\n            (uint256(10)**(ethDecimals - 6)));\n    }\n\n    function tokUsdPrice(string calldata symbol)\n        external\n        view\n        returns (uint256)\n    {\n        bytes32 tokenSymbolHash = keccak256(abi.encodePacked(symbol));\n        FeedConfig storage config = feeds[tokenSymbolHash];\n        int256 tPrice = getLatestPrice(config.feed);\n\n        require(config.directToUsd, \"Price is not direct to usd\");\n        require(tPrice > 0, \"Price must be greater than zero\");\n        return uint256(tPrice);\n    }\n\n    function tokEthPrice(string calldata symbol) external returns (uint256) {\n        bytes32 tokenSymbolHash = keccak256(abi.encodePacked(symbol));\n        FeedConfig storage config = feeds[tokenSymbolHash];\n        int256 tPrice = getLatestPrice(config.feed);\n\n        require(!config.directToUsd, \"Price is not in terms of ETH\");\n        require(tPrice > 0, \"Price must be greater than zero\");\n        //attempt to return 8 digit precision here\n        return uint256(tPrice) / (uint256(10)**(config.decimals - 8));\n    }\n\n    // This actually calculate the latest price from outside oracles\n    // It's a view but substantially more costly in terms of calculation\n    function price(string calldata symbol) external view returns (uint256) {\n        bytes32 tokenSymbolHash = keccak256(abi.encodePacked(symbol));\n\n        if (ethHash == tokenSymbolHash) {\n            return (uint256(getLatestPrice(ethFeed)) /\n                (uint256(10)**(ethDecimals - 6)));\n        } else {\n            FeedConfig storage config = feeds[tokenSymbolHash];\n            int256 tPrice = getLatestPrice(config.feed);\n\n            if (config.directToUsd) {\n                require(tPrice > 0, \"Price must be greater than zero\");\n                return uint256(tPrice);\n            } else {\n                int256 ethPrice = getLatestPrice(ethFeed); // grab the eth price from the open oracle\n                require(\n                    tPrice > 0 && ethPrice > 0,\n                    \"Both eth and price must be greater than zero\"\n                );\n                //not actually sure why it's 6 units here, this is just to match with openoracle for now\n                return\n                    mul(uint256(tPrice), uint256(ethPrice)) /\n                    (uint256(10)**(ethDecimals + config.decimals - 6));\n            }\n        }\n    }\n\n    /// @dev Overflow proof multiplication\n    function mul(uint256 a, uint256 b) internal pure returns (uint256) {\n        if (a == 0) return 0;\n        uint256 c = a * b;\n        require(c / a == b, \"multiplication overflow\");\n        return c;\n    }\n}\n"}, "contracts/oracle/MixOracle.sol": {"content": "pragma solidity 0.5.11;\n\n/**\n * @title OUSD MixOracle Contract\n * @notice The MixOracle pulls exchange rate from multiple oracles and returns\n *         min and max values.\n * <AUTHOR> Protocol Inc\n */\nimport { IPriceOracle } from \"../interfaces/IPriceOracle.sol\";\nimport { IEthUsdOracle } from \"../interfaces/IEthUsdOracle.sol\";\nimport { IMinMaxOracle } from \"../interfaces/IMinMaxOracle.sol\";\nimport {\n    InitializableGovernable\n} from \"../governance/InitializableGovernable.sol\";\n\ncontract MixOracle is IMinMaxOracle, InitializableGovernable {\n    address[] public ethUsdOracles;\n\n    struct MixConfig {\n        address[] usdOracles;\n        address[] ethOracles;\n    }\n\n    mapping(bytes32 => MixConfig) configs;\n\n    uint256 constant MAX_INT = 2**256 - 1;\n    uint256 public maxDrift;\n    uint256 public minDrift;\n\n    constructor(uint256 _maxDrift, uint256 _minDrift) public {\n        maxDrift = _maxDrift;\n        minDrift = _minDrift;\n    }\n\n    function setMinMaxDrift(uint256 _maxDrift, uint256 _minDrift)\n        public\n        onlyGovernor\n    {\n        maxDrift = _maxDrift;\n        minDrift = _minDrift;\n    }\n\n    /**\n     * @notice Adds an oracle to the list of oracles to pull data from.\n     * @param oracle Address of an oracle that implements the IEthUsdOracle interface.\n     **/\n    function registerEthUsdOracle(address oracle) public onlyGovernor {\n        for (uint256 i = 0; i < ethUsdOracles.length; i++) {\n            require(ethUsdOracles[i] != oracle, \"Oracle already registered.\");\n        }\n        ethUsdOracles.push(oracle);\n    }\n\n    /**\n     * @notice Removes an oracle to the list of oracles to pull data from.\n     * @param oracle Address of an oracle that implements the IEthUsdOracle interface.\n     **/\n    function unregisterEthUsdOracle(address oracle) public onlyGovernor {\n        for (uint256 i = 0; i < ethUsdOracles.length; i++) {\n            if (ethUsdOracles[i] == oracle) {\n                // swap with the last element of the array, and then delete last element (could be itself)\n                ethUsdOracles[i] = ethUsdOracles[ethUsdOracles.length - 1];\n                delete ethUsdOracles[ethUsdOracles.length - 1];\n                ethUsdOracles.length--;\n                return;\n            }\n        }\n        revert(\"Oracle not found\");\n    }\n\n    /**\n     * @notice Adds an oracle to the list of oracles to pull data from.\n     * @param ethOracles Addresses of oracles that implements the IEthUsdOracle interface and answers for this asset\n     * @param usdOracles Addresses of oracles that implements the IPriceOracle interface and answers for this asset\n     **/\n    function registerTokenOracles(\n        string calldata symbol,\n        address[] calldata ethOracles,\n        address[] calldata usdOracles\n    ) external onlyGovernor {\n        MixConfig storage config = configs[keccak256(abi.encodePacked(symbol))];\n        config.ethOracles = ethOracles;\n        config.usdOracles = usdOracles;\n    }\n\n    /**\n     * @notice Returns the min price of an asset in USD.\n     * @return symbol Asset symbol. Example: \"DAI\"\n     * @return price Min price from all the oracles, in USD with 8 decimal digits.\n     **/\n    function priceMin(string calldata symbol) external returns (uint256 price) {\n        MixConfig storage config = configs[keccak256(abi.encodePacked(symbol))];\n        uint256 ep;\n        uint256 p; //holder variables\n        price = MAX_INT;\n        if (config.ethOracles.length > 0) {\n            ep = MAX_INT;\n            for (uint256 i = 0; i < config.ethOracles.length; i++) {\n                p = IEthUsdOracle(config.ethOracles[i]).tokEthPrice(symbol);\n                if (ep > p) {\n                    ep = p;\n                }\n            }\n            price = ep;\n            ep = MAX_INT;\n            for (uint256 i = 0; i < ethUsdOracles.length; i++) {\n                p = IEthUsdOracle(ethUsdOracles[i]).ethUsdPrice();\n                if (ep > p) {\n                    ep = p;\n                }\n            }\n            if (price != MAX_INT && ep != MAX_INT) {\n                // tokEthPrice has precision of 8 which ethUsdPrice has precision of 6\n                // we want precision of 8\n                price = (price * ep) / 1e6;\n            }\n        }\n\n        if (config.usdOracles.length > 0) {\n            for (uint256 i = 0; i < config.usdOracles.length; i++) {\n                // upscale by 2 since price oracles are precision 6\n                p = IPriceOracle(config.usdOracles[i]).price(symbol) * 1e2;\n                if (price > p) {\n                    price = p;\n                }\n            }\n        }\n        require(price < maxDrift, \"Price exceeds max value.\");\n        require(price > minDrift, \"Price lower than min value.\");\n        require(\n            price != MAX_INT,\n            \"None of our oracles returned a valid min price!\"\n        );\n    }\n\n    /**\n     * @notice Returns max price of an asset in USD.\n     * @return symbol Asset symbol. Example: \"DAI\"\n     * @return price Max price from all the oracles, in USD with 8 decimal digits.\n     **/\n    function priceMax(string calldata symbol) external returns (uint256 price) {\n        MixConfig storage config = configs[keccak256(abi.encodePacked(symbol))];\n        uint256 ep;\n        uint256 p; //holder variables\n        price = 0;\n        if (config.ethOracles.length > 0) {\n            ep = 0;\n            for (uint256 i = 0; i < config.ethOracles.length; i++) {\n                p = IEthUsdOracle(config.ethOracles[i]).tokEthPrice(symbol);\n                if (ep < p) {\n                    ep = p;\n                }\n            }\n            price = ep;\n            ep = 0;\n            for (uint256 i = 0; i < ethUsdOracles.length; i++) {\n                p = IEthUsdOracle(ethUsdOracles[i]).ethUsdPrice();\n                if (ep < p) {\n                    ep = p;\n                }\n            }\n            if (price != 0 && ep != 0) {\n                // tokEthPrice has precision of 8 which ethUsdPrice has precision of 6\n                // we want precision of 8\n                price = (price * ep) / 1e6;\n            }\n        }\n\n        if (config.usdOracles.length > 0) {\n            for (uint256 i = 0; i < config.usdOracles.length; i++) {\n                // upscale by 2 since price oracles are precision 6\n                p = IPriceOracle(config.usdOracles[i]).price(symbol) * 1e2;\n                if (price < p) {\n                    price = p;\n                }\n            }\n        }\n\n        require(price < maxDrift, \"Price above max value.\");\n        require(price > minDrift, \"Price below min value.\");\n        require(price != 0, \"None of our oracles returned a valid max price!\");\n    }\n}\n"}, "contracts/oracle/OpenUniswapOracle.sol": {"content": "pragma solidity 0.5.11;\npragma experimental ABIEncoderV2;\n\n/**\n * @title OUSD OpenUniswapOracle Contract\n * <AUTHOR> Protocol Inc\n */\nimport \"./UniswapLib.sol\";\nimport { IPriceOracle } from \"../interfaces/IPriceOracle.sol\";\nimport { IEthUsdOracle } from \"../interfaces/IEthUsdOracle.sol\";\nimport {\n    InitializableGovernable\n} from \"../governance/InitializableGovernable.sol\";\n\ncontract OpenUniswapOracle is IEthUsdOracle, InitializableGovernable {\n    using FixedPoint for *;\n    uint256 public constant PERIOD = 2 minutes;\n\n    struct SwapConfig {\n        bool ethOnFirst; // whether the weth is the first in pair\n        address swap; // address of the uniswap pair\n        uint256 blockTimestampLast;\n        uint256 latestBlockTimestampLast;\n        uint256 priceCumulativeLast;\n        uint256 latestPriceCumulativeLast;\n        uint256 baseUnit;\n    }\n\n    mapping(bytes32 => SwapConfig) swaps;\n\n    IPriceOracle public ethPriceOracle; //price oracle for getting the Eth->USD price OPEN oracle..\n    address ethToken;\n    string constant ethSymbol = \"ETH\";\n    bytes32 constant ethHash = keccak256(abi.encodePacked(ethSymbol));\n\n    constructor(address ethPriceOracle_, address ethToken_) public {\n        ethPriceOracle = IPriceOracle(ethPriceOracle_);\n        ethToken = ethToken_;\n    }\n\n    function registerEthPriceOracle(address ethPriceOracle_)\n        public\n        onlyGovernor\n    {\n        ethPriceOracle = IPriceOracle(ethPriceOracle_);\n    }\n\n    function registerPair(address pair_) public onlyGovernor {\n        IUniswapV2Pair pair = IUniswapV2Pair(pair_);\n        address token;\n        bool ethOnFirst = true;\n        if (pair.token0() == ethToken) {\n            token = pair.token1();\n        } else {\n            token = pair.token0();\n            ethOnFirst = false;\n        }\n        SymboledERC20 st = SymboledERC20(token);\n        string memory symbol = st.symbol();\n        SwapConfig storage config = swaps[keccak256(abi.encodePacked(symbol))];\n\n        // is the first token the eth Token\n        config.ethOnFirst = ethOnFirst;\n        config.swap = pair_;\n        config.baseUnit = uint256(10)**st.decimals();\n\n        // we want everything relative to first\n        config.priceCumulativeLast = currentCumulativePrice(config);\n        config.blockTimestampLast = block.timestamp;\n        config.latestBlockTimestampLast = config.blockTimestampLast;\n        config.latestPriceCumulativeLast = config.priceCumulativeLast;\n    }\n\n    function currentCumulativePrice(SwapConfig storage config)\n        internal\n        view\n        returns (uint256)\n    {\n        (\n            uint256 cumulativePrice0,\n            uint256 cumulativePrice1,\n\n        ) = UniswapV2OracleLibrary.currentCumulativePrices(config.swap);\n        if (config.ethOnFirst) {\n            return cumulativePrice1;\n        } else {\n            return cumulativePrice0;\n        }\n    }\n\n    // This needs to be called regularly to update the pricing window\n    function pokePriceWindow(SwapConfig storage config)\n        internal\n        returns (\n            uint256,\n            uint256,\n            uint256\n        )\n    {\n        uint256 priceCumulative = currentCumulativePrice(config);\n\n        uint256 timeElapsed = block.timestamp - config.latestBlockTimestampLast;\n\n        if (timeElapsed >= PERIOD) {\n            config.blockTimestampLast = config.latestBlockTimestampLast;\n            config.priceCumulativeLast = config.latestPriceCumulativeLast;\n\n            config.latestBlockTimestampLast = block.timestamp;\n            config.latestPriceCumulativeLast = priceCumulative;\n        }\n\n        return (\n            priceCumulative,\n            config.priceCumulativeLast,\n            config.blockTimestampLast\n        );\n    }\n\n    // update to the latest window\n    function updatePriceWindows(bytes32[] calldata symbolHashes) external {\n        for (uint256 i = 0; i < symbolHashes.length; i++) {\n            SwapConfig storage config = swaps[symbolHashes[i]];\n            pokePriceWindow(config);\n        }\n    }\n\n    //eth to usd price\n    //precision from open is 6\n    function ethUsdPrice() external view returns (uint256) {\n        return ethPriceOracle.price(ethSymbol); // grab the eth price from the open oracle\n    }\n\n    //tok to Usd price\n    //Note: for USDC and USDT this is fixed to 1 on openoracle\n    // precision here is 8\n    function tokUsdPrice(string calldata symbol)\n        external\n        view\n        returns (uint256)\n    {\n        return ethPriceOracle.price(symbol); // grab the eth price from the open oracle\n    }\n\n    //tok to Eth price\n    function tokEthPrice(string calldata symbol) external returns (uint256) {\n        bytes32 tokenSymbolHash = keccak256(abi.encodePacked(symbol));\n        SwapConfig storage config = swaps[tokenSymbolHash];\n        (\n            uint256 priceCumulative,\n            uint256 priceCumulativeLast,\n            uint256 blockTimestampLast\n        ) = pokePriceWindow(config);\n\n        require(\n            priceCumulative > priceCumulativeLast,\n            \"There has been no cumulative change\"\n        );\n        // This should be impossible, but better safe than sorry\n        require(\n            block.timestamp > blockTimestampLast,\n            \"now must come after before\"\n        );\n        uint256 timeElapsed = block.timestamp - blockTimestampLast;\n\n        // overflow is desired, casting never truncates\n        // cumulative price is in (uq112x112 price * seconds) units so we simply wrap it after division by time elapsed\n        FixedPoint.uq112x112 memory priceAverage = FixedPoint.uq112x112(\n            uint224(\n                (priceCumulative - config.priceCumulativeLast) / timeElapsed\n            )\n        );\n        uint256 rawUniswapPriceMantissa = priceAverage.decode112with18();\n\n        // Divide by 1e28 because it's decoded to 18 and then we want 8 decimal places of precision out so 18+18-8\n        return mul(rawUniswapPriceMantissa, config.baseUnit) / 1e28;\n    }\n\n    // This actually calculate the latest price from outside oracles\n    // It's a view but substantially more costly in terms of calculation\n    function price(string calldata symbol) external view returns (uint256) {\n        bytes32 tokenSymbolHash = keccak256(abi.encodePacked(symbol));\n        uint256 ethPrice = ethPriceOracle.price(ethSymbol); // grab the eth price from the open oracle\n\n        if (ethHash == tokenSymbolHash) {\n            return ethPrice;\n        } else {\n            SwapConfig storage config = swaps[tokenSymbolHash];\n            uint256 priceCumulative = currentCumulativePrice(config);\n\n            require(\n                priceCumulative > config.priceCumulativeLast,\n                \"There has been no cumulative change\"\n            );\n            // This should be impossible, but better safe than sorry\n            require(\n                block.timestamp > config.blockTimestampLast,\n                \"now must come after before\"\n            );\n            uint256 timeElapsed = block.timestamp - config.blockTimestampLast;\n\n            // overflow is desired, casting never truncates\n            // cumulative price is in (uq112x112 price * seconds) units so we simply wrap it after division by time elapsed\n            FixedPoint.uq112x112 memory priceAverage = FixedPoint.uq112x112(\n                uint224(\n                    (priceCumulative - config.priceCumulativeLast) / timeElapsed\n                )\n            );\n            uint256 rawUniswapPriceMantissa = priceAverage.decode112with18();\n\n            uint256 unscaledPriceMantissa = mul(\n                rawUniswapPriceMantissa,\n                ethPrice\n            );\n\n            return mul(unscaledPriceMantissa, config.baseUnit) / 1e36;\n        }\n    }\n\n    function debugPrice(string calldata symbol)\n        external\n        view\n        returns (\n            uint256,\n            uint256,\n            uint256,\n            uint256\n        )\n    {\n        bytes32 tokenSymbolHash = keccak256(abi.encodePacked(symbol));\n        uint256 ethPrice = ethPriceOracle.price(ethSymbol); // grab the eth price from the open oracle\n\n        SwapConfig storage config = swaps[tokenSymbolHash];\n        uint256 priceCumulative = currentCumulativePrice(config);\n\n        require(\n            priceCumulative > config.priceCumulativeLast,\n            \"There has been no cumulative change\"\n        );\n        // This should be impossible, but better safe than sorry\n        require(\n            block.timestamp > config.blockTimestampLast,\n            \"now must come after before\"\n        );\n        uint256 timeElapsed = block.timestamp - config.blockTimestampLast;\n        FixedPoint.uq112x112 memory priceAverage = FixedPoint.uq112x112(\n            uint224(\n                (priceCumulative - config.priceCumulativeLast) / timeElapsed\n            )\n        );\n        uint256 rawUniswapPriceMantissa = priceAverage.decode112with18();\n\n        uint256 unscaledPriceMantissa = mul(rawUniswapPriceMantissa, ethPrice);\n\n        // overflow is desired, casting never truncates\n        // cumulative price is in (uq112x112 price * seconds) units so we simply wrap it after division by time elapsed\n\n        return (\n            priceCumulative - config.priceCumulativeLast,\n            timeElapsed,\n            rawUniswapPriceMantissa,\n            unscaledPriceMantissa\n        );\n    }\n\n    function openPrice(string calldata symbol) external view returns (uint256) {\n        return ethPriceOracle.price(symbol);\n    }\n\n    function getSwapConfig(string calldata symbol)\n        external\n        view\n        returns (SwapConfig memory)\n    {\n        bytes32 tokenSymbolHash = keccak256(abi.encodePacked(symbol));\n        return swaps[tokenSymbolHash];\n    }\n\n    /// @dev Overflow proof multiplication\n    function mul(uint256 a, uint256 b) internal pure returns (uint256) {\n        if (a == 0) return 0;\n        uint256 c = a * b;\n        require(c / a == b, \"multiplication overflow\");\n        return c;\n    }\n}\n\ncontract SymboledERC20 {\n    function symbol() public view returns (string memory);\n\n    function decimals() public view returns (uint8);\n}\n"}, "contracts/proxies/InitializeGovernedUpgradeabilityProxy.sol": {"content": "pragma solidity 0.5.11;\n\nimport { Governable } from \"../governance/Governable.sol\";\nimport {\n    BaseUpgradeabilityProxy\n} from \"@openzeppelin/upgrades/contracts/upgradeability/BaseUpgradeabilityProxy.sol\";\n\n/**\n * @title BaseGovernedUpgradeabilityProxy\n * @dev This contract combines an upgradeability proxy with our governor system\n * <AUTHOR> Protocol Inc\n */\ncontract InitializeGovernedUpgradeabilityProxy is\n    Governable,\n    BaseUpgradeabilityProxy\n{\n    /**\n     * @dev Contract initializer with Governor enforcement\n     * @param _logic Address of the initial implementation.\n     * @param _initGovernor Address of the initial Governor.\n     * @param _data Data to send as msg.data to the implementation to initialize the proxied contract.\n     * It should include the signature and the parameters of the function to be called, as described in\n     * https://solidity.readthedocs.io/en/v0.4.24/abi-spec.html#function-selector-and-argument-encoding.\n     * This parameter is optional, if no data is given the initialization call to proxied contract will be skipped.\n     */\n    function initialize(\n        address _logic,\n        address _initGovernor,\n        bytes memory _data\n    ) public payable onlyGovernor {\n        require(_implementation() == address(0));\n        assert(\n            IMPLEMENTATION_SLOT ==\n                bytes32(uint256(keccak256(\"eip1967.proxy.implementation\")) - 1)\n        );\n        _setImplementation(_logic);\n        if (_data.length > 0) {\n            (bool success, ) = _logic.delegatecall(_data);\n            require(success);\n        }\n        _changeGovernor(_initGovernor);\n    }\n\n    /**\n     * @return The address of the proxy admin/it's also the governor.\n     */\n    function admin() external view returns (address) {\n        return _governor();\n    }\n\n    /**\n     * @return The address of the implementation.\n     */\n    function implementation() external view returns (address) {\n        return _implementation();\n    }\n\n    /**\n     * @dev Upgrade the backing implementation of the proxy.\n     * Only the admin can call this function.\n     * @param newImplementation Address of the new implementation.\n     */\n    function upgradeTo(address newImplementation) external onlyGovernor {\n        _upgradeTo(newImplementation);\n    }\n\n    /**\n     * @dev Upgrade the backing implementation of the proxy and call a function\n     * on the new implementation.\n     * This is useful to initialize the proxied contract.\n     * @param newImplementation Address of the new implementation.\n     * @param data Data to send as msg.data in the low level call.\n     * It should include the signature and the parameters of the function to be called, as described in\n     * https://solidity.readthedocs.io/en/v0.4.24/abi-spec.html#function-selector-and-argument-encoding.\n     */\n    function upgradeToAndCall(address newImplementation, bytes calldata data)\n        external\n        payable\n        onlyGovernor\n    {\n        _upgradeTo(newImplementation);\n        (bool success, ) = newImplementation.delegatecall(data);\n        require(success);\n    }\n}\n"}, "@openzeppelin/upgrades/contracts/upgradeability/BaseUpgradeabilityProxy.sol": {"content": "pragma solidity ^0.5.0;\n\nimport './Proxy.sol';\nimport '../utils/Address.sol';\n\n/**\n * @title BaseUpgradeabilityProxy\n * @dev This contract implements a proxy that allows to change the\n * implementation address to which it will delegate.\n * Such a change is called an implementation upgrade.\n */\ncontract BaseUpgradeabilityProxy is Proxy {\n  /**\n   * @dev Emitted when the implementation is upgraded.\n   * @param implementation Address of the new implementation.\n   */\n  event Upgraded(address indexed implementation);\n\n  /**\n   * @dev Storage slot with the address of the current implementation.\n   * This is the keccak-256 hash of \"eip1967.proxy.implementation\" subtracted by 1, and is\n   * validated in the constructor.\n   */\n  bytes32 internal constant IMPLEMENTATION_SLOT = 0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc;\n\n  /**\n   * @dev Returns the current implementation.\n   * @return Address of the current implementation\n   */\n  function _implementation() internal view returns (address impl) {\n    bytes32 slot = IMPLEMENTATION_SLOT;\n    assembly {\n      impl := sload(slot)\n    }\n  }\n\n  /**\n   * @dev Upgrades the proxy to a new implementation.\n   * @param newImplementation Address of the new implementation.\n   */\n  function _upgradeTo(address newImplementation) internal {\n    _setImplementation(newImplementation);\n    emit Upgraded(newImplementation);\n  }\n\n  /**\n   * @dev Sets the implementation address of the proxy.\n   * @param newImplementation Address of the new implementation.\n   */\n  function _setImplementation(address newImplementation) internal {\n    require(OpenZeppelinUpgradesAddress.isContract(newImplementation), \"Cannot set a proxy implementation to a non-contract address\");\n\n    bytes32 slot = IMPLEMENTATION_SLOT;\n\n    assembly {\n      sstore(slot, newImplementation)\n    }\n  }\n}\n"}, "@openzeppelin/upgrades/contracts/upgradeability/Proxy.sol": {"content": "pragma solidity ^0.5.0;\n\n/**\n * @title Proxy\n * @dev Implements delegation of calls to other contracts, with proper\n * forwarding of return values and bubbling of failures.\n * It defines a fallback function that delegates all calls to the address\n * returned by the abstract _implementation() internal function.\n */\ncontract Proxy {\n  /**\n   * @dev Fallback function.\n   * Implemented entirely in `_fallback`.\n   */\n  function () payable external {\n    _fallback();\n  }\n\n  /**\n   * @return The Address of the implementation.\n   */\n  function _implementation() internal view returns (address);\n\n  /**\n   * @dev Delegates execution to an implementation contract.\n   * This is a low level function that doesn't return to its internal call site.\n   * It will return to the external caller whatever the implementation returns.\n   * @param implementation Address to delegate.\n   */\n  function _delegate(address implementation) internal {\n    assembly {\n      // Copy msg.data. We take full control of memory in this inline assembly\n      // block because it will not return to Solidity code. We overwrite the\n      // Solidity scratch pad at memory position 0.\n      calldatacopy(0, 0, calldatasize)\n\n      // Call the implementation.\n      // out and outsize are 0 because we don't know the size yet.\n      let result := delegatecall(gas, implementation, 0, calldatasize, 0, 0)\n\n      // Copy the returned data.\n      returndatacopy(0, 0, returndatasize)\n\n      switch result\n      // delegatecall returns 0 on error.\n      case 0 { revert(0, returndatasize) }\n      default { return(0, returndatasize) }\n    }\n  }\n\n  /**\n   * @dev Function that is run as the first thing in the fallback function.\n   * Can be redefined in derived contracts to add functionality.\n   * Redefinitions must call super._willFallback().\n   */\n  function _willFallback() internal {\n  }\n\n  /**\n   * @dev fallback implementation.\n   * Extracted to enable manual triggering.\n   */\n  function _fallback() internal {\n    _willFallback();\n    _delegate(_implementation());\n  }\n}\n"}, "@openzeppelin/upgrades/contracts/utils/Address.sol": {"content": "pragma solidity ^0.5.0;\n\n/**\n * Utility library of inline functions on addresses\n *\n * Source https://raw.githubusercontent.com/OpenZeppelin/openzeppelin-solidity/v2.1.3/contracts/utils/Address.sol\n * This contract is copied here and renamed from the original to avoid clashes in the compiled artifacts\n * when the user imports a zos-lib contract (that transitively causes this contract to be compiled and added to the\n * build/artifacts folder) as well as the vanilla Address implementation from an openzeppelin version.\n */\nlibrary OpenZeppelinUpgradesAddress {\n    /**\n     * Returns whether the target address is a contract\n     * @dev This function will return false if invoked during the constructor of a contract,\n     * as the code is not actually created until after the constructor finishes.\n     * @param account address of the account to check\n     * @return whether the target address is a contract\n     */\n    function isContract(address account) internal view returns (bool) {\n        uint256 size;\n        // XXX Currently there is no better way to check if there is a contract in an address\n        // than to check the size of the code at that address.\n        // See https://ethereum.stackexchange.com/a/14016/36603\n        // for more details about how this works.\n        // TODO Check this again before the Serenity release, because all addresses will be\n        // contracts then.\n        // solhint-disable-next-line no-inline-assembly\n        assembly { size := extcodesize(account) }\n        return size > 0;\n    }\n}\n"}, "contracts/proxies/Proxies.sol": {"content": "pragma solidity 0.5.11;\n\nimport {\n    InitializeGovernedUpgradeabilityProxy\n} from \"./InitializeGovernedUpgradeabilityProxy.sol\";\n\n/**\n * @notice OUSDProxy delegates calls to an OUSD implementation\n */\ncontract OUSDProxy is InitializeGovernedUpgradeabilityProxy {\n\n}\n\n/**\n * @notice VaultProxy delegates calls to a Vault implementation\n */\ncontract VaultProxy is InitializeGovernedUpgradeabilityProxy {\n\n}\n\n/**\n * @notice CompoundStrategyProxy delegates calls to a CompoundStrategy implementation\n */\ncontract CompoundStrategyProxy is InitializeGovernedUpgradeabilityProxy {\n\n}\n\n/**\n * @notice ThreePoolStrategyProxy delegates calls to a ThreePoolStrategy implementation\n */\ncontract ThreePoolStrategyProxy is InitializeGovernedUpgradeabilityProxy {\n\n}\n"}, "contracts/strategies/CompoundStrategy.sol": {"content": "pragma solidity 0.5.11;\n\n/**\n * @title OUSD Compound Strategy\n * @notice Investment strategy for investing stablecoins via Compound\n * <AUTHOR> Protocol Inc\n */\nimport { ICERC20 } from \"./ICompound.sol\";\nimport {\n    IERC20,\n    InitializableAbstractStrategy\n} from \"../utils/InitializableAbstractStrategy.sol\";\n\ncontract CompoundStrategy is InitializableAbstractStrategy {\n    event RewardTokenCollected(address recipient, uint256 amount);\n    event SkippedWithdrawal(address asset, uint256 amount);\n\n    /**\n     * @dev Collect accumulated reward token (COMP) and send to Vault.\n     */\n    function collectRewardToken() external onlyVault {\n        IERC20 compToken = IERC20(rewardTokenAddress);\n        uint256 balance = compToken.balanceOf(address(this));\n        require(\n            compToken.transfer(vaultAddress, balance),\n            \"Reward token transfer failed\"\n        );\n\n        emit RewardTokenCollected(vaultAddress, balance);\n    }\n\n    /**\n     * @dev Deposit asset into Compound\n     * @param _asset Address of asset to deposit\n     * @param _amount Amount of asset to deposit\n     * @return amountDeposited Amount of asset that was deposited\n     */\n    function deposit(address _asset, uint256 _amount)\n        external\n        onlyVault\n        returns (uint256 amountDeposited)\n    {\n        require(_amount > 0, \"Must deposit something\");\n\n        ICERC20 cToken = _getCTokenFor(_asset);\n        require(cToken.mint(_amount) == 0, \"cToken mint failed\");\n\n        amountDeposited = _amount;\n\n        emit Deposit(_asset, address(cToken), amountDeposited);\n    }\n\n    /**\n     * @dev Withdraw asset from Compound\n     * @param _recipient Address to receive withdrawn asset\n     * @param _asset Address of asset to withdraw\n     * @param _amount Amount of asset to withdraw\n     * @return amountWithdrawn Amount of asset that was withdrawn\n     */\n    function withdraw(\n        address _recipient,\n        address _asset,\n        uint256 _amount\n    ) external onlyVault returns (uint256 amountWithdrawn) {\n        require(_amount > 0, \"Must withdraw something\");\n        require(_recipient != address(0), \"Must specify recipient\");\n\n        ICERC20 cToken = _getCTokenFor(_asset);\n        // If redeeming 0 cTokens, just skip, else COMP will revert\n        uint256 cTokensToRedeem = _convertUnderlyingToCToken(cToken, _amount);\n        if (cTokensToRedeem == 0) {\n            emit SkippedWithdrawal(_asset, _amount);\n            return 0;\n        }\n\n        amountWithdrawn = _amount;\n\n        require(cToken.redeemUnderlying(_amount) == 0, \"Redeem failed\");\n\n        IERC20(_asset).safeTransfer(_recipient, amountWithdrawn);\n\n        emit Withdrawal(_asset, address(cToken), amountWithdrawn);\n    }\n\n    /**\n     * @dev Remove all assets from platform and send them to Vault contract.\n     */\n    function liquidate() external onlyVaultOrGovernor {\n        for (uint256 i = 0; i < assetsMapped.length; i++) {\n            // Redeem entire balance of cToken\n            ICERC20 cToken = _getCTokenFor(assetsMapped[i]);\n            if (cToken.balanceOf(address(this)) > 0) {\n                cToken.redeem(cToken.balanceOf(address(this)));\n                // Transfer entire balance to Vault\n                IERC20 asset = IERC20(assetsMapped[i]);\n                asset.safeTransfer(\n                    vaultAddress,\n                    asset.balanceOf(address(this))\n                );\n            }\n        }\n    }\n\n    /**\n     * @dev Get the total asset value held in the platform\n     *      This includes any interest that was generated since depositing\n     *      Compound exchange rate between the cToken and asset gradually increases,\n     *      causing the cToken to be worth more corresponding asset.\n     * @param _asset      Address of the asset\n     * @return balance    Total value of the asset in the platform\n     */\n    function checkBalance(address _asset)\n        external\n        view\n        returns (uint256 balance)\n    {\n        // Balance is always with token cToken decimals\n        ICERC20 cToken = _getCTokenFor(_asset);\n        balance = _checkBalance(cToken);\n    }\n\n    /**\n     * @dev Get the total asset value held in the platform\n     *      underlying = (cTokenAmt * exchangeRate) / 1e18\n     * @param _cToken     cToken for which to check balance\n     * @return balance    Total value of the asset in the platform\n     */\n    function _checkBalance(ICERC20 _cToken)\n        internal\n        view\n        returns (uint256 balance)\n    {\n        uint256 cTokenBalance = _cToken.balanceOf(address(this));\n        uint256 exchangeRate = _cToken.exchangeRateStored();\n        // e.g. 50e8*205316390724364402565641705 / 1e18 = 1.0265..e18\n        balance = cTokenBalance.mul(exchangeRate).div(1e18);\n    }\n\n    /**\n     * @dev Retuns bool indicating whether asset is supported by strategy\n     * @param _asset Address of the asset\n     */\n    function supportsAsset(address _asset) external view returns (bool) {\n        return assetToPToken[_asset] != address(0);\n    }\n\n    /**\n     * @dev Approve the spending of all assets by their corresponding cToken,\n     *      if for some reason is it necessary.\n     */\n    function safeApproveAllTokens() external {\n        uint256 assetCount = assetsMapped.length;\n        for (uint256 i = 0; i < assetCount; i++) {\n            address asset = assetsMapped[i];\n            address cToken = assetToPToken[asset];\n            // Safe approval\n            IERC20(asset).safeApprove(cToken, 0);\n            IERC20(asset).safeApprove(cToken, uint256(-1));\n        }\n    }\n\n    /**\n     * @dev Internal method to respond to the addition of new asset / cTokens\n     *      We need to approve the cToken and give it permission to spend the asset\n     * @param _asset Address of the asset to approve\n     * @param _cToken This cToken has the approval approval\n     */\n    function _abstractSetPToken(address _asset, address _cToken) internal {\n        // Safe approval\n        IERC20(_asset).safeApprove(_cToken, 0);\n        IERC20(_asset).safeApprove(_cToken, uint256(-1));\n    }\n\n    /**\n     * @dev Get the cToken wrapped in the ICERC20 interface for this asset.\n     *      Fails if the pToken doesn't exist in our mappings.\n     * @param _asset Address of the asset\n     * @return Corresponding cToken to this asset\n     */\n    function _getCTokenFor(address _asset) internal view returns (ICERC20) {\n        address cToken = assetToPToken[_asset];\n        require(cToken != address(0), \"cToken does not exist\");\n        return ICERC20(cToken);\n    }\n\n    /**\n     * @dev Converts an underlying amount into cToken amount\n     *      cTokenAmt = (underlying * 1e18) / exchangeRate\n     * @param _cToken     cToken for which to change\n     * @param _underlying Amount of underlying to convert\n     * @return amount     Equivalent amount of cTokens\n     */\n    function _convertUnderlyingToCToken(ICERC20 _cToken, uint256 _underlying)\n        internal\n        view\n        returns (uint256 amount)\n    {\n        uint256 exchangeRate = _cToken.exchangeRateStored();\n        // e.g. 1e18*1e18 / 205316390724364402565641705 = 50e8\n        // e.g. 1e8*1e18 / 205316390724364402565641705 = 0.45 or 0\n        amount = _underlying.mul(1e18).div(exchangeRate);\n    }\n}\n"}, "contracts/utils/InitializableAbstractStrategy.sol": {"content": "pragma solidity 0.5.11;\n\nimport {\n    Initializable\n} from \"@openzeppelin/upgrades/contracts/Initializable.sol\";\nimport { IERC20 } from \"@openzeppelin/contracts/token/ERC20/IERC20.sol\";\nimport { SafeERC20 } from \"@openzeppelin/contracts/token/ERC20/SafeERC20.sol\";\nimport { SafeMath } from \"@openzeppelin/contracts/math/SafeMath.sol\";\n\nimport { Governable } from \"../governance/Governable.sol\";\n\ncontract InitializableAbstractStrategy is Initializable, Governable {\n    using SafeERC20 for IERC20;\n    using SafeMath for uint256;\n\n    event PTokenAdded(address indexed _asset, address _pToken);\n    event Deposit(address indexed _asset, address _pToken, uint256 _amount);\n    event Withdrawal(address indexed _asset, address _pToken, uint256 _amount);\n\n    // Core address for the given platform\n    address public platformAddress;\n\n    address public vaultAddress;\n\n    // asset => pToken (Platform Specific Token Address)\n    mapping(address => address) public assetToPToken;\n\n    // Full list of all assets supported here\n    address[] internal assetsMapped;\n\n    // Reward token address\n    address public rewardTokenAddress;\n    uint256 public rewardLiquidationThreshold;\n\n    /**\n     * @dev Internal initialize function, to set up initial internal state\n     * @param _platformAddress jGeneric platform address\n     * @param _vaultAddress Address of the Vault\n     * @param _rewardTokenAddress Address of reward token for platform\n     * @param _assets Addresses of initial supported assets\n     * @param _pTokens Platform Token corresponding addresses\n     */\n    function initialize(\n        address _platformAddress,\n        address _vaultAddress,\n        address _rewardTokenAddress,\n        address[] calldata _assets,\n        address[] calldata _pTokens\n    ) external onlyGovernor initializer {\n        InitializableAbstractStrategy._initialize(\n            _platformAddress,\n            _vaultAddress,\n            _rewardTokenAddress,\n            _assets,\n            _pTokens\n        );\n    }\n\n    function _initialize(\n        address _platformAddress,\n        address _vaultAddress,\n        address _rewardTokenAddress,\n        address[] memory _assets,\n        address[] memory _pTokens\n    ) internal {\n        platformAddress = _platformAddress;\n        vaultAddress = _vaultAddress;\n        rewardTokenAddress = _rewardTokenAddress;\n        uint256 assetCount = _assets.length;\n        require(assetCount == _pTokens.length, \"Invalid input arrays\");\n        for (uint256 i = 0; i < assetCount; i++) {\n            _setPTokenAddress(_assets[i], _pTokens[i]);\n        }\n    }\n\n    /**\n     * @dev Single asset variant of the internal initialize.\n     */\n    function _initialize(\n        address _platformAddress,\n        address _vaultAddress,\n        address _rewardTokenAddress,\n        address _asset,\n        address _pToken\n    ) internal {\n        platformAddress = _platformAddress;\n        vaultAddress = _vaultAddress;\n        rewardTokenAddress = _rewardTokenAddress;\n        _setPTokenAddress(_asset, _pToken);\n    }\n\n    /**\n     * @dev Verifies that the caller is the Vault.\n     */\n    modifier onlyVault() {\n        require(msg.sender == vaultAddress, \"Caller is not the Vault\");\n        _;\n    }\n\n    /**\n     * @dev Verifies that the caller is the Vault or Governor.\n     */\n    modifier onlyVaultOrGovernor() {\n        require(\n            msg.sender == vaultAddress || msg.sender == governor(),\n            \"Caller is not the Vault or Governor\"\n        );\n        _;\n    }\n\n    /**\n     * @dev Set the reward token address.\n     * @param _rewardTokenAddress Address of the reward token\n     */\n    function setRewardTokenAddress(address _rewardTokenAddress)\n        external\n        onlyGovernor\n    {\n        rewardTokenAddress = _rewardTokenAddress;\n    }\n\n    /**\n     * @dev Set the reward token liquidation threshold.\n     * @param _threshold Threshold amount in decimals of reward token that will\n     * cause the Vault to claim and liquidate on allocate() calls.\n     */\n    function setRewardLiquidationThreshold(uint256 _threshold)\n        external\n        onlyGovernor\n    {\n        rewardLiquidationThreshold = _threshold;\n    }\n\n    /**\n     * @dev Provide support for asset by passing its pToken address.\n     *      This method can only be called by the system Governor\n     * @param _asset    Address for the asset\n     * @param _pToken   Address for the corresponding platform token\n     */\n    function setPTokenAddress(address _asset, address _pToken)\n        external\n        onlyGovernor\n    {\n        _setPTokenAddress(_asset, _pToken);\n    }\n\n    /**\n     * @dev Provide support for asset by passing its pToken address.\n     *      Add to internal mappings and execute the platform specific,\n     * abstract method `_abstractSetPToken`\n     * @param _asset    Address for the asset\n     * @param _pToken   Address for the corresponding platform token\n     */\n    function _setPTokenAddress(address _asset, address _pToken) internal {\n        require(assetToPToken[_asset] == address(0), \"pToken already set\");\n        require(\n            _asset != address(0) && _pToken != address(0),\n            \"Invalid addresses\"\n        );\n\n        assetToPToken[_asset] = _pToken;\n        assetsMapped.push(_asset);\n\n        emit PTokenAdded(_asset, _pToken);\n\n        _abstractSetPToken(_asset, _pToken);\n    }\n\n    /**\n     * @dev Transfer token to governor. Intended for recovering tokens stuck in\n     *      strategy contracts, i.e. mistaken sends.\n     * @param _asset Address for the asset\n     * @param _amount Amount of the asset to transfer\n     */\n    function transferToken(address _asset, uint256 _amount)\n        public\n        onlyGovernor\n    {\n        IERC20(_asset).transfer(governor(), _amount);\n    }\n\n    /***************************************\n                 Abstract\n    ****************************************/\n\n    function _abstractSetPToken(address _asset, address _pToken) internal;\n\n    function safeApproveAllTokens() external;\n\n    /**\n     * @dev Deposit a amount of asset into the platform\n     * @param _asset               Address for the asset\n     * @param _amount              Units of asset to deposit\n     * @return amountDeposited     Quantity of asset that was deposited\n     */\n    function deposit(address _asset, uint256 _amount)\n        external\n        returns (uint256 amountDeposited);\n\n    /**\n     * @dev Withdraw an amount of asset from the platform.\n     * @param _recipient         Address to which the asset should be sent\n     * @param _asset             Address of the asset\n     * @param _amount            Units of asset to withdraw\n     * @return amountWithdrawn   Quantity of asset that was withdrawn\n     */\n    function withdraw(\n        address _recipient,\n        address _asset,\n        uint256 _amount\n    ) external returns (uint256 amountWithdrawn);\n\n    /**\n     * @dev Liquidate entire contents of strategy sending assets to Vault.\n     */\n    function liquidate() external;\n\n    /**\n     * @dev Get the total asset value held in the platform.\n     *      This includes any interest that was generated since depositing.\n     * @param _asset      Address of the asset\n     * @return balance    Total value of the asset in the platform\n     */\n    function checkBalance(address _asset)\n        external\n        view\n        returns (uint256 balance);\n\n    /**\n     * @dev Check if an asset is supported.\n     * @param _asset    Address of the asset\n     * @return bool     Whether asset is supported\n     */\n    function supportsAsset(address _asset) external view returns (bool);\n}\n"}, "contracts/strategies/ICRVMinter.sol": {"content": "pragma solidity 0.5.11;\n\ninterface ICRVMinter {\n    function mint(address gaugeAddress) external;\n}\n"}, "contracts/strategies/ThreePoolStrategy.sol": {"content": "pragma solidity 0.5.11;\n\n/**\n * @title Curve 3Pool Strategy\n * @notice Investment strategy for investing stablecoins via Curve 3Pool\n * <AUTHOR> Protocol Inc\n */\n\nimport { ICurvePool } from \"./ICurvePool.sol\";\nimport { ICurveGauge } from \"./ICurveGauge.sol\";\nimport { ICRVMinter } from \"./ICRVMinter.sol\";\nimport {\n    IERC20,\n    InitializableAbstractStrategy\n} from \"../utils/InitializableAbstractStrategy.sol\";\nimport { Helpers } from \"../utils/Helpers.sol\";\n\ncontract ThreePoolStrategy is InitializableAbstractStrategy {\n\n    event RewardTokenCollected(address recipient, uint256 amount);\n\n    address crvGaugeAddress;\n    address crvMinterAddress;\n    int128 poolCoinIndex = -1;\n\n    /**\n     * Initializer for setting up strategy internal state. This overrides the\n     * InitializableAbstractStrategy initializer as Curve strategies don't fit\n     * well within that abstraction.\n     * @param _platformAddress Address of the Curve 3pool\n     * @param _vaultAddress Address of the vault\n     * @param _rewardTokenAddress Address of CRV\n     * @param _asset Address of the supported asset\n     * @param _pToken Correspond platform token addres (i.e. 3Crv)\n     * @param _crvGaugeAddress Address of the Curve DAO gauge for this pool\n     * @param _crvMinterAddress Address of the CRV minter for rewards\n     */\n    function initialize(\n        address _platformAddress, // 3Pool address\n        address _vaultAddress,\n        address _rewardTokenAddress, // CRV\n        address _asset,\n        address _pToken,\n        address _crvGaugeAddress,\n        address _crvMinterAddress\n    ) external onlyGovernor initializer {\n        ICurvePool threePool = ICurvePool(_platformAddress);\n        for (int128 i = 0; i < 3; i++) {\n            if (threePool.coins(uint256(i)) == _asset) poolCoinIndex = i;\n        }\n        require(poolCoinIndex != -1, \"Invalid 3pool asset\");\n        crvGaugeAddress = _crvGaugeAddress;\n        crvMinterAddress = _crvMinterAddress;\n        InitializableAbstractStrategy._initialize(\n            _platformAddress,\n            _vaultAddress,\n            _rewardTokenAddress,\n            _asset,\n            _pToken\n        );\n    }\n\n    /**\n     * @dev Collect accumulated CRV and send to Vault.\n     */\n    function collectRewardToken() external onlyVault {\n        ICRVMinter minter = ICRVMinter(crvMinterAddress);\n        minter.mint(crvGaugeAddress);\n        IERC20 crvToken = IERC20(rewardTokenAddress);\n        uint256 balance = crvToken.balanceOf(address(this));\n        require(\n            crvToken.transfer(vaultAddress, balance),\n            \"Reward token transfer failed\"\n        );\n        emit RewardTokenCollected(vaultAddress, balance);\n    }\n\n    /**\n     * @dev Deposit asset into the Curve 3Pool\n     * @param _asset Address of asset to deposit\n     * @param _amount Amount of asset to deposit\n     * @return amountDeposited Amount of asset that was deposited\n     */\n    function deposit(address _asset, uint256 _amount)\n        external\n        onlyVault\n        returns (uint256 amountDeposited)\n    {\n        require(_amount > 0, \"Must deposit something\");\n        // 3Pool requires passing deposit amounts for all 3 assets, set to 0 for\n        // all\n        uint256[3] memory _amounts;\n        // Set the amount on the asset we want to deposit\n        _amounts[uint256(poolCoinIndex)] = _amount;\n        // Do the deposit to 3pool\n        ICurvePool(platformAddress).add_liquidity(_amounts, 0);\n        // Deposit into Gauge\n        IERC20 pToken = IERC20(assetToPToken[_asset]);\n        ICurveGauge(crvGaugeAddress).deposit(\n            pToken.balanceOf(address(this)),\n            address(this)\n        );\n        amountDeposited = _amount;\n        emit Deposit(_asset, address(platformAddress), amountDeposited);\n    }\n\n    /**\n     * @dev Withdraw asset from Curve 3Pool\n     * @param _recipient Address to receive withdrawn asset\n     * @param _asset Address of asset to withdraw\n     * @param _amount Amount of asset to withdraw\n     * @return amountWithdrawn Amount of asset that was withdrawn\n     */\n    function withdraw(\n        address _recipient,\n        address _asset,\n        uint256 _amount\n    ) external onlyVault returns (uint256 amountWithdrawn) {\n        require(_recipient != address(0), \"Invalid recipient\");\n        require(_amount > 0, \"Invalid amount\");\n        // Calculate how much of the pool token we need to withdraw\n        (\n            uint256 contractPTokens,\n            uint256 gaugePTokens,\n            uint256 totalPTokens\n        ) = _getTotalPTokens();\n        // Calculate the max amount of the asset we'd get if we withdrew all the\n        // platform tokens\n        ICurvePool curvePool = ICurvePool(platformAddress);\n        uint256 maxAmount = curvePool.calc_withdraw_one_coin(\n            totalPTokens,\n            poolCoinIndex\n        );\n        // Calculate how many platform tokens we need to withdraw the asset amount\n        uint256 withdrawPTokens = totalPTokens.mul(_amount).div(maxAmount);\n        if (contractPTokens < withdrawPTokens) {\n            // Not enough of pool token exists on this contract, must be staked\n            // in Gauge, unstake\n            ICurveGauge(crvGaugeAddress).withdraw(withdrawPTokens);\n        }\n        curvePool.remove_liquidity_one_coin(\n            withdrawPTokens,\n            poolCoinIndex,\n            0\n        );\n        IERC20(_asset).transfer(_recipient, _amount);\n        // Transfer any leftover dust back to the vault buffer.\n        uint256 dust = IERC20(_asset).balanceOf(address(this));\n        if (dust > 0) {\n            IERC20(_asset).safeTransfer(vaultAddress, dust);\n        }\n        amountWithdrawn = _amount;\n        emit Withdrawal(\n            _asset,\n            address(assetToPToken[_asset]),\n            amountWithdrawn\n        );\n    }\n\n    /**\n     * @dev Remove all assets from platform and send them to Vault contract.\n     */\n    function liquidate() external onlyVaultOrGovernor {\n        // Withdraw all from Gauge\n        (, uint256 gaugePTokens, ) = _getTotalPTokens();\n        ICurveGauge(crvGaugeAddress).withdraw(gaugePTokens);\n        // Remove entire balance, 3pool strategies only support a single asset\n        // so safe to use assetsMapped[0]\n        IERC20 asset = IERC20(assetsMapped[0]);\n        uint256 pTokenBalance = IERC20(assetToPToken[address(asset)]).balanceOf(\n            address(this)\n        );\n        ICurvePool(platformAddress).remove_liquidity_one_coin(\n            pTokenBalance,\n            poolCoinIndex,\n            0\n        );\n        // Transfer the asset out to Vault\n        asset.safeTransfer(vaultAddress, asset.balanceOf(address(this)));\n    }\n\n    /**\n     * @dev Get the total asset value held in the platform\n     *  This includes any interest that was generated since depositing\n     *  We calculate this by calculating a what we would get if we liquidated\n     *  the allocated percentage of this asset.\n     * @param _asset      Address of the asset\n     * @return balance    Total value of the asset in the platform\n     */\n    function checkBalance(address _asset)\n        external\n        view\n        returns (uint256 balance)\n    {\n        // LP tokens in this contract. This should generally be nothing as we\n        // should always stake the full balance in the Gauge, but include for\n        // safety\n        (, , uint256 totalPTokens) = _getTotalPTokens();\n        balance = 0;\n        if (totalPTokens > 0) {\n            balance += ICurvePool(platformAddress).calc_withdraw_one_coin(\n                totalPTokens,\n                poolCoinIndex\n            );\n        }\n    }\n\n    /**\n     * @dev Retuns bool indicating whether asset is supported by strategy\n     * @param _asset Address of the asset\n     */\n    function supportsAsset(address _asset) external view returns (bool) {\n        return assetToPToken[_asset] != address(0);\n    }\n\n    /**\n     * @dev Approve the spending of all assets by their corresponding pool tokens,\n     *      if for some reason is it necessary.\n     */\n    function safeApproveAllTokens() external {\n        // This strategy is a special case since it only supports one asset\n        address assetAddress = assetsMapped[0];\n        _abstractSetPToken(assetAddress, assetToPToken[assetAddress]);\n    }\n\n    /**\n     * @dev Calculate the total platform token balance (i.e. 3CRV) that exist in\n     * this contract or is staked in the Gauge (or in other words, the total\n     * amount platform tokens we own).\n     * @return totalPTokens Total amount of platform tokens in native decimals\n     */\n    function _getTotalPTokens()\n        internal\n        view\n        returns (\n            uint256 contractPTokens,\n            uint256 gaugePTokens,\n            uint256 totalPTokens\n        )\n    {\n        contractPTokens = IERC20(assetToPToken[assetsMapped[0]]).balanceOf(\n            address(this)\n        );\n        ICurveGauge gauge = ICurveGauge(crvGaugeAddress);\n        gaugePTokens = gauge.balanceOf(address(this));\n        totalPTokens = contractPTokens.add(gaugePTokens);\n    }\n\n    /**\n     * @dev Call the necessary approvals for the Curve pool and gauge\n     * @param _asset Address of the asset\n     * @param _pToken Address of the corresponding platform token (i.e. 3CRV)\n     */\n    function _abstractSetPToken(address _asset, address _pToken) internal {\n        IERC20 asset = IERC20(_asset);\n        IERC20 pToken = IERC20(_pToken);\n        // 3Pool for asset (required for adding liquidity)\n        asset.safeApprove(platformAddress, 0);\n        asset.safeApprove(platformAddress, uint256(-1));\n        // 3Pool for LP token (required for removing liquidity)\n        pToken.safeApprove(platformAddress, 0);\n        pToken.safeApprove(platformAddress, uint256(-1));\n        // Gauge for LP token\n        pToken.safeApprove(crvGaugeAddress, 0);\n        pToken.safeApprove(crvGaugeAddress, uint256(-1));\n    }\n}\n"}, "contracts/timelock/MinuteTimelock.sol": {"content": "pragma solidity 0.5.11;\n\nimport {\n    Initializable\n} from \"@openzeppelin/upgrades/contracts/Initializable.sol\";\n\nimport \"@openzeppelin/contracts/math/SafeMath.sol\";\n\n// Modeled off of Compound's Timelock\n//    https://github.com/compound-finance/compound-protocol/blob/master/contracts/Timelock.sol\ncontract MinuteTimelock is Initializable {\n    using SafeMath for uint256;\n\n    event NewAdmin(address indexed newAdmin);\n    event NewPendingAdmin(address indexed newPendingAdmin);\n    event NewDelay(uint256 indexed newDelay);\n    event CancelTransaction(\n        bytes32 indexed txHash,\n        address indexed target,\n        uint256 value,\n        string signature,\n        bytes data,\n        uint256 eta\n    );\n    event ExecuteTransaction(\n        bytes32 indexed txHash,\n        address indexed target,\n        uint256 value,\n        string signature,\n        bytes data,\n        uint256 eta\n    );\n    event QueueTransaction(\n        bytes32 indexed txHash,\n        address indexed target,\n        uint256 value,\n        string signature,\n        bytes data,\n        uint256 eta\n    );\n\n    uint256 public constant GRACE_PERIOD = 3 days;\n    uint256 public constant MINIMUM_DELAY = 1 minutes;\n    uint256 public constant MAXIMUM_DELAY = 2 days;\n\n    address public admin;\n    address public pendingAdmin;\n    uint256 public delay;\n\n    mapping(bytes32 => bool) public queuedTransactions;\n\n    constructor(uint256 delay_) public {\n        require(\n            delay_ >= MINIMUM_DELAY,\n            \"Timelock::constructor: Delay must exceed minimum delay.\"\n        );\n        require(\n            delay_ <= MAXIMUM_DELAY,\n            \"Timelock::setDelay: Delay must not exceed maximum delay.\"\n        );\n\n        admin = msg.sender;\n        delay = delay_;\n    }\n\n    function initialize(address _admin) external initializer {\n        require(\n            msg.sender == admin,\n            \"Timelock::initialize: Call must come from admin.\"\n        );\n        admin = _admin;\n    }\n\n    function() external payable {}\n\n    function setDelay(uint256 delay_) public {\n        require(\n            msg.sender == address(this),\n            \"Timelock::setDelay: Call must come from Timelock.\"\n        );\n        require(\n            delay_ >= MINIMUM_DELAY,\n            \"Timelock::setDelay: Delay must exceed minimum delay.\"\n        );\n        require(\n            delay_ <= MAXIMUM_DELAY,\n            \"Timelock::setDelay: Delay must not exceed maximum delay.\"\n        );\n        delay = delay_;\n\n        emit NewDelay(delay);\n    }\n\n    function acceptAdmin() public {\n        require(\n            msg.sender == pendingAdmin,\n            \"Timelock::acceptAdmin: Call must come from pendingAdmin.\"\n        );\n        admin = msg.sender;\n        pendingAdmin = address(0);\n\n        emit NewAdmin(admin);\n    }\n\n    function setPendingAdmin(address pendingAdmin_) public {\n        require(\n            msg.sender == address(this),\n            \"Timelock::setPendingAdmin: Call must come from Timelock.\"\n        );\n        pendingAdmin = pendingAdmin_;\n\n        emit NewPendingAdmin(pendingAdmin);\n    }\n\n    function queueTransaction(\n        address target,\n        uint256 value,\n        string memory signature,\n        bytes memory data,\n        uint256 eta\n    ) public returns (bytes32) {\n        require(\n            msg.sender == admin,\n            \"Timelock::queueTransaction: Call must come from admin.\"\n        );\n        require(\n            eta >= getBlockTimestamp().add(delay),\n            \"Timelock::queueTransaction: Estimated execution block must satisfy delay.\"\n        );\n\n        bytes32 txHash = keccak256(\n            abi.encode(target, value, signature, data, eta)\n        );\n        queuedTransactions[txHash] = true;\n\n        emit QueueTransaction(txHash, target, value, signature, data, eta);\n        return txHash;\n    }\n\n    function cancelTransaction(\n        address target,\n        uint256 value,\n        string memory signature,\n        bytes memory data,\n        uint256 eta\n    ) public {\n        require(\n            msg.sender == admin,\n            \"Timelock::cancelTransaction: Call must come from admin.\"\n        );\n\n        bytes32 txHash = keccak256(\n            abi.encode(target, value, signature, data, eta)\n        );\n        queuedTransactions[txHash] = false;\n\n        emit CancelTransaction(txHash, target, value, signature, data, eta);\n    }\n\n    function executeTransaction(\n        address target,\n        uint256 value,\n        string memory signature,\n        bytes memory data,\n        uint256 eta\n    ) public payable returns (bytes memory) {\n        bytes32 txHash = keccak256(\n            abi.encode(target, value, signature, data, eta)\n        );\n        require(\n            queuedTransactions[txHash],\n            \"Timelock::executeTransaction: Transaction hasn't been queued.\"\n        );\n        require(\n            getBlockTimestamp() >= eta,\n            \"Timelock::executeTransaction: Transaction hasn't surpassed time lock.\"\n        );\n        require(\n            getBlockTimestamp() <= eta.add(GRACE_PERIOD),\n            \"Timelock::executeTransaction: Transaction is stale.\"\n        );\n\n        queuedTransactions[txHash] = false;\n\n        bytes memory callData;\n\n        if (bytes(signature).length == 0) {\n            callData = data;\n        } else {\n            callData = abi.encodePacked(\n                bytes4(keccak256(bytes(signature))),\n                data\n            );\n        }\n\n        // solium-disable-next-line security/no-call-value\n        (bool success, bytes memory returnData) = target.call.value(value)(\n            callData\n        );\n        require(\n            success,\n            \"Timelock::executeTransaction: Transaction execution reverted.\"\n        );\n\n        emit ExecuteTransaction(txHash, target, value, signature, data, eta);\n\n        return returnData;\n    }\n\n    function getBlockTimestamp() internal view returns (uint256) {\n        // solium-disable-next-line security/no-block-members\n        return block.timestamp;\n    }\n}\n"}, "contracts/timelock/Timelock.sol": {"content": "pragma solidity 0.5.11;\n\n/**\n * @title OUSD Timelock Contract\n * <AUTHOR> Protocol Inc\n */\nimport \"@openzeppelin/contracts/math/SafeMath.sol\";\n\ninterface IGovernable {\n    function claimGovernance() external;\n}\n\ninterface DepositPausable {\n    function pauseDeposits() external;\n\n    function unpauseDeposits() external;\n}\n\ncontract Timelock {\n    using SafeMath for uint256;\n\n    event NewAdmin(address indexed newAdmin);\n    event NewPendingAdmin(address indexed newPendingAdmin);\n    event NewDelay(uint256 indexed newDelay);\n    event CancelTransaction(\n        bytes32 indexed txHash,\n        address indexed target,\n        uint256 value,\n        string signature,\n        bytes data,\n        uint256 eta\n    );\n    event ExecuteTransaction(\n        bytes32 indexed txHash,\n        address indexed target,\n        uint256 value,\n        string signature,\n        bytes data,\n        uint256 eta\n    );\n    event QueueTransaction(\n        bytes32 indexed txHash,\n        address indexed target,\n        uint256 value,\n        string signature,\n        bytes data,\n        uint256 eta\n    );\n\n    uint256 public constant GRACE_PERIOD = 14 days;\n    uint256 public constant MINIMUM_DELAY = 2 days;\n    uint256 public constant MAXIMUM_DELAY = 30 days;\n\n    address public admin;\n    address public pendingAdmin;\n    uint256 public delay;\n\n    mapping(bytes32 => bool) public queuedTransactions;\n\n    constructor(address admin_, uint256 delay_) public {\n        require(\n            delay_ >= MINIMUM_DELAY,\n            \"Timelock::constructor: Delay must exceed minimum delay.\"\n        );\n        require(\n            delay_ <= MAXIMUM_DELAY,\n            \"Timelock::setDelay: Delay must not exceed maximum delay.\"\n        );\n\n        admin = admin_;\n        delay = delay_;\n    }\n\n    function() external payable {}\n\n    function setDelay(uint256 delay_) public {\n        require(\n            msg.sender == address(this),\n            \"Timelock::setDelay: Call must come from Timelock.\"\n        );\n        require(\n            delay_ >= MINIMUM_DELAY,\n            \"Timelock::setDelay: Delay must exceed minimum delay.\"\n        );\n        require(\n            delay_ <= MAXIMUM_DELAY,\n            \"Timelock::setDelay: Delay must not exceed maximum delay.\"\n        );\n        delay = delay_;\n\n        emit NewDelay(delay);\n    }\n\n    function acceptAdmin() public {\n        require(\n            msg.sender == pendingAdmin,\n            \"Timelock::acceptAdmin: Call must come from pendingAdmin.\"\n        );\n        admin = msg.sender;\n        pendingAdmin = address(0);\n\n        emit NewAdmin(admin);\n    }\n\n    function setPendingAdmin(address pendingAdmin_) public {\n        require(\n            msg.sender == address(this),\n            \"Timelock::setPendingAdmin: Call must come from Timelock.\"\n        );\n        pendingAdmin = pendingAdmin_;\n\n        emit NewPendingAdmin(pendingAdmin);\n    }\n\n    function queueTransaction(\n        address target,\n        uint256 value,\n        string memory signature,\n        bytes memory data,\n        uint256 eta\n    ) public returns (bytes32) {\n        require(\n            msg.sender == admin,\n            \"Timelock::queueTransaction: Call must come from admin.\"\n        );\n        require(\n            eta >= getBlockTimestamp().add(delay),\n            \"Timelock::queueTransaction: Estimated execution block must satisfy delay.\"\n        );\n\n        bytes32 txHash = keccak256(\n            abi.encode(target, value, signature, data, eta)\n        );\n        queuedTransactions[txHash] = true;\n\n        emit QueueTransaction(txHash, target, value, signature, data, eta);\n        return txHash;\n    }\n\n    function cancelTransaction(\n        address target,\n        uint256 value,\n        string memory signature,\n        bytes memory data,\n        uint256 eta\n    ) public {\n        require(\n            msg.sender == admin,\n            \"Timelock::cancelTransaction: Call must come from admin.\"\n        );\n\n        bytes32 txHash = keccak256(\n            abi.encode(target, value, signature, data, eta)\n        );\n        queuedTransactions[txHash] = false;\n\n        emit CancelTransaction(txHash, target, value, signature, data, eta);\n    }\n\n    function executeTransaction(\n        address target,\n        uint256 value,\n        string memory signature,\n        bytes memory data,\n        uint256 eta\n    ) public payable returns (bytes memory) {\n        bytes32 txHash = keccak256(\n            abi.encode(target, value, signature, data, eta)\n        );\n        require(\n            queuedTransactions[txHash],\n            \"Timelock::executeTransaction: Transaction hasn't been queued.\"\n        );\n        require(\n            getBlockTimestamp() >= eta,\n            \"Timelock::executeTransaction: Transaction hasn't surpassed time lock.\"\n        );\n        require(\n            getBlockTimestamp() <= eta.add(GRACE_PERIOD),\n            \"Timelock::executeTransaction: Transaction is stale.\"\n        );\n\n        queuedTransactions[txHash] = false;\n\n        bytes memory callData;\n\n        if (bytes(signature).length == 0) {\n            callData = data;\n        } else {\n            callData = abi.encodePacked(\n                bytes4(keccak256(bytes(signature))),\n                data\n            );\n        }\n\n        // solium-disable-next-line security/no-call-value\n        (bool success, bytes memory returnData) = target.call.value(value)(\n            callData\n        );\n        require(\n            success,\n            \"Timelock::executeTransaction: Transaction execution reverted.\"\n        );\n\n        emit ExecuteTransaction(txHash, target, value, signature, data, eta);\n\n        return returnData;\n    }\n\n    function getBlockTimestamp() internal view returns (uint256) {\n        // solium-disable-next-line security/no-block-members\n        return block.timestamp;\n    }\n\n    function pauseDeposits(address target) external {\n        require(\n            msg.sender == admin,\n            \"Timelock::pauseDeposits: Call must come from admin.\"\n        );\n        DepositPausable(target).pauseDeposits();\n    }\n\n    function unpauseDeposits(address target) external {\n        require(\n            msg.sender == admin,\n            \"Timelock::unpauseDeposits: Call must come from admin.\"\n        );\n        DepositPausable(target).unpauseDeposits();\n    }\n}\n"}, "contracts/utils/RebaseHooks.sol": {"content": "pragma solidity 0.5.11;\n\nimport { IUniswapV2Pair } from \"../interfaces/uniswap/IUniswapV2Pair.sol\";\nimport { Governable } from \"../governance/Governable.sol\";\n\ncontract RebaseHooks is Governable {\n    // Array of Uniswap pairs for OUSD. Used for calling sync() on each.\n    address[] public uniswapPairs;\n\n    function setUniswapPairs(address[] calldata _uniswapPairs)\n        external\n        onlyGovernor\n    {\n        uniswapPairs = _uniswapPairs;\n    }\n\n    function postRebase(bool sync) external {\n        if (sync) {\n            // Sync Uniswap pairs\n            for (uint256 i = 0; i < uniswapPairs.length; i++) {\n                IUniswapV2Pair(uniswapPairs[i]).sync();\n            }\n        }\n    }\n}\n"}, "contracts/vault/Vault.sol": {"content": "pragma solidity 0.5.11;\n\n/**\n * @title OUSD VaultInitializer Contract\n * @notice The VaultInitializer sets up the initial contract.\n * <AUTHOR> Protocol Inc\n */\nimport { VaultInitializer } from \"./VaultInitializer.sol\";\nimport { VaultAdmin } from \"./VaultAdmin.sol\";\n\ncontract Vault is VaultInitializer, VaultAdmin {}\n"}, "contracts/vault/VaultAdmin.sol": {"content": "pragma solidity 0.5.11;\n\n/**\n * @title OUSD Vault Admin Contract\n * @notice The VaultAdmin contract makes configuration and admin calls on the vault.\n * <AUTHOR> Protocol Inc\n */\n\nimport \"./VaultStorage.sol\";\nimport { IMinMaxOracle } from \"../interfaces/IMinMaxOracle.sol\";\nimport { IUniswapV2Router } from \"../interfaces/uniswap/IUniswapV2Router02.sol\";\n\ncontract VaultAdmin is VaultStorage {\n    /***************************************\n                 Configuration\n    ****************************************/\n\n    /**\n     * @dev Set address of price provider.\n     * @param _priceProvider Address of price provider\n     */\n    function setPriceProvider(address _priceProvider) external onlyGovernor {\n        priceProvider = _priceProvider;\n    }\n\n    /**\n     * @dev Set a fee in basis points to be charged for a redeem.\n     * @param _redeemFeeBps Basis point fee to be charged\n     */\n    function setRedeemFeeBps(uint256 _redeemFeeBps) external onlyGovernor {\n        redeemFeeBps = _redeemFeeBps;\n    }\n\n    /**\n     * @dev Set a buffer of assets to keep in the Vault to handle most\n     * redemptions without needing to spend gas unwinding assets from a Strategy.\n     * @param _vaultBuffer Percentage using 18 decimals. 100% = 1e18.\n     */\n    function setVaultBuffer(uint256 _vaultBuffer) external onlyGovernor {\n        vaultBuffer = _vaultBuffer;\n    }\n\n    /**\n     * @dev Sets the minimum amount of OUSD in a mint to trigger an\n     * automatic allocation of funds afterwords.\n     * @param _threshold OUSD amount with 18 fixed decimals.\n     */\n    function setAutoAllocateThreshold(uint256 _threshold)\n        external\n        onlyGovernor\n    {\n        autoAllocateThreshold = _threshold;\n    }\n\n    /**\n     * @dev Set a minimum amount of OUSD in a mint or redeem that triggers a\n     * rebase\n     * @param _threshold OUSD amount with 18 fixed decimals.\n     */\n    function setRebaseThreshold(uint256 _threshold) external onlyGovernor {\n        rebaseThreshold = _threshold;\n    }\n\n    /**\n     * @dev Set address of RebaseHooks contract which provides hooks for rebase\n     * so things like AMMs can be synced with updated balances.\n     * @param _address Address of RebaseHooks contract\n     */\n    function setRebaseHooksAddr(address _address) external onlyGovernor {\n        rebaseHooksAddr = _address;\n    }\n\n    /**\n     * @dev Set address of Uniswap for performing liquidation of strategy reward\n     * tokens\n     * @param _address Address of Uniswap\n     */\n    function setUniswapAddr(address _address) external onlyGovernor {\n        uniswapAddr = _address;\n    }\n\n    /** @dev Add a supported asset to the contract, i.e. one that can be\n     *         to mint OUSD.\n     * @param _asset Address of asset\n     */\n    function supportAsset(address _asset) external onlyGovernor {\n        require(!assets[_asset].isSupported, \"Asset already supported\");\n\n        assets[_asset] = Asset({ isSupported: true });\n        allAssets.push(_asset);\n\n        emit AssetSupported(_asset);\n    }\n\n    /**\n     * @dev Add a strategy to the Vault.\n     * @param _addr Address of the strategy to add\n     * @param _targetWeight Target percentage of asset allocation to strategy\n     */\n    function addStrategy(address _addr, uint256 _targetWeight)\n        external\n        onlyGovernor\n    {\n        require(!strategies[_addr].isSupported, \"Strategy already added\");\n\n        strategies[_addr] = Strategy({\n            isSupported: true,\n            targetWeight: _targetWeight\n        });\n        allStrategies.push(_addr);\n\n        emit StrategyAdded(_addr);\n    }\n\n    /**\n     * @dev Remove a strategy from the Vault. Removes all invested assets and\n     * returns them to the Vault.\n     * @param _addr Address of the strategy to remove\n     */\n\n    function removeStrategy(address _addr) external onlyGovernor {\n        require(strategies[_addr].isSupported, \"Strategy not added\");\n\n        // Initialize strategyIndex with out of bounds result so function will\n        // revert if no valid index found\n        uint256 strategyIndex = allStrategies.length;\n        for (uint256 i = 0; i < allStrategies.length; i++) {\n            if (allStrategies[i] == _addr) {\n                strategyIndex = i;\n                break;\n            }\n        }\n\n        assert(strategyIndex < allStrategies.length);\n\n        allStrategies[strategyIndex] = allStrategies[allStrategies.length - 1];\n        allStrategies.length--;\n\n        // Liquidate all assets\n        IStrategy strategy = IStrategy(_addr);\n        strategy.liquidate();\n\n        emit StrategyRemoved(_addr);\n    }\n\n    /**\n     * @notice Set the weights for multiple strategies.\n     * @param _strategyAddresses Array of strategy addresses\n     * @param _weights Array of corresponding weights, with 18 decimals.\n     *                 For ex. 100%=1e18, 30%=3e17.\n     */\n    function setStrategyWeights(\n        address[] calldata _strategyAddresses,\n        uint256[] calldata _weights\n    ) external onlyGovernor {\n        require(\n            _strategyAddresses.length == _weights.length,\n            \"Parameter length mismatch\"\n        );\n\n        for (uint256 i = 0; i < _strategyAddresses.length; i++) {\n            strategies[_strategyAddresses[i]].targetWeight = _weights[i];\n        }\n\n        emit StrategyWeightsUpdated(_strategyAddresses, _weights);\n    }\n\n    /***************************************\n                    Pause\n    ****************************************/\n\n    /**\n     * @dev Set the deposit paused flag to true to prevent rebasing.\n     */\n    function pauseRebase() external onlyGovernor {\n        rebasePaused = true;\n    }\n\n    /**\n     * @dev Set the deposit paused flag to true to allow rebasing.\n     */\n    function unpauseRebase() external onlyGovernor {\n        rebasePaused = false;\n    }\n\n    /**\n     * @dev Set the deposit paused flag to true to prevent deposits.\n     */\n    function pauseDeposits() external onlyGovernor {\n        depositPaused = true;\n\n        emit DepositsPaused();\n    }\n\n    /**\n     * @dev Set the deposit paused flag to false to enable deposits.\n     */\n    function unpauseDeposits() external onlyGovernor {\n        depositPaused = false;\n\n        emit DepositsUnpaused();\n    }\n\n    /***************************************\n                    Rewards\n    ****************************************/\n\n    /**\n     * @dev Transfer token to governor. Intended for recovering tokens stuck in\n     *      contract, i.e. mistaken sends.\n     * @param _asset Address for the asset\n     * @param _amount Amount of the asset to transfer\n     */\n    function transferToken(address _asset, uint256 _amount)\n        external\n        onlyGovernor\n    {\n        IERC20(_asset).transfer(governor(), _amount);\n    }\n\n    /**\n     * @dev Collect reward tokens from all strategies and swap for supported\n     *      stablecoin via Uniswap\n     */\n    function harvest() external onlyGovernor {\n        for (uint256 i = 0; i < allStrategies.length; i++) {\n            _harvest(allStrategies[i]);\n        }\n    }\n\n    /**\n     * @dev Collect reward tokens for a specific strategy and swap for supported\n     *      stablecoin via Uniswap\n     * @param _strategyAddr Address of the strategy to collect rewards from\n     */\n    function harvest(address _strategyAddr) external onlyGovernor {\n        _harvest(_strategyAddr);\n    }\n\n    /**\n     * @dev Collect reward tokens from a single strategy and swap them for a\n     *      supported stablecoin via Uniswap\n     * @param _strategyAddr Address of the strategy to collect rewards from\n     */\n    function _harvest(address _strategyAddr) internal {\n        IStrategy strategy = IStrategy(_strategyAddr);\n        strategy.collectRewardToken();\n\n        if (uniswapAddr != address(0)) {\n            IERC20 rewardToken = IERC20(strategy.rewardTokenAddress());\n            uint256 rewardTokenAmount = rewardToken.balanceOf(address(this));\n            if (rewardTokenAmount > 0) {\n                // Give Uniswap full amount allowance\n                rewardToken.safeApprove(uniswapAddr, 0);\n                rewardToken.safeApprove(uniswapAddr, rewardTokenAmount);\n\n                // Uniswap redemption path\n                address[] memory path = new address[](3);\n                path[0] = strategy.rewardTokenAddress();\n                path[1] = IUniswapV2Router(uniswapAddr).WETH();\n                path[2] = allAssets[1]; // USDT\n\n                IUniswapV2Router(uniswapAddr).swapExactTokensForTokens(\n                    rewardTokenAmount,\n                    uint256(0),\n                    path,\n                    address(this),\n                    now.add(1800)\n                );\n            }\n        }\n    }\n\n    /***************************************\n                    Pricing\n    ****************************************/\n\n    /**\n     * @dev Returns the total price in 18 digit USD for a given asset.\n     *      Using Min since min is what we use for mint pricing\n     * @param symbol String symbol of the asset\n     * @return uint256 USD price of 1 of the asset\n     */\n    function priceUSDMint(string calldata symbol) external returns (uint256) {\n        return _priceUSDMint(symbol);\n    }\n\n    /**\n     * @dev Returns the total price in 18 digit USD for a given asset.\n     *      Using Min since min is what we use for mint pricing\n     * @param symbol String symbol of the asset\n     * @return uint256 USD price of 1 of the asset\n     */\n    function _priceUSDMint(string memory symbol) internal returns (uint256) {\n        // Price from Oracle is returned with 8 decimals\n        // scale to 18 so 18-8=10\n        return IMinMaxOracle(priceProvider).priceMin(symbol).scaleBy(10);\n    }\n\n    /**\n     * @dev Returns the total price in 18 digit USD for a given asset.\n     *      Using Max since max is what we use for redeem pricing\n     * @param symbol String symbol of the asset\n     * @return uint256 USD price of 1 of the asset\n     */\n    function priceUSDRedeem(string calldata symbol) external returns (uint256) {\n        // Price from Oracle is returned with 8 decimals\n        // scale to 18 so 18-8=10\n        return _priceUSDRedeem(symbol);\n    }\n\n    /**\n     * @dev Returns the total price in 18 digit USD for a given asset.\n     *      Using Max since max is what we use for redeem pricing\n     * @param symbol String symbol of the asset\n     * @return uint256 USD price of 1 of the asset\n     */\n    function _priceUSDRedeem(string memory symbol) internal returns (uint256) {\n        // Price from Oracle is returned with 8 decimals\n        // scale to 18 so 18-8=10\n        return IMinMaxOracle(priceProvider).priceMax(symbol).scaleBy(10);\n    }\n}\n"}}, "settings": {"metadata": {"useLiteralContent": false}, "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "<PERSON>v<PERSON><PERSON>", "userdoc", "storageLayout", "evm.gasEstimates"], "": ["id", "ast"]}}}}