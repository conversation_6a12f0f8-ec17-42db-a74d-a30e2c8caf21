/**
 *Submitted for verification at Etherscan.io on 2020-05-30
 */

/**
Matic network contracts
*/

pragma solidity ^0.5.2;

contract Ownable {
  address private _owner;

  event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);

  /**
   * @dev The Ownable constructor sets the original `owner` of the contract to the sender
   * account.
   */
  constructor() internal {
    _owner = msg.sender;
    emit OwnershipTransferred(address(0), _owner);
  }

  /**
   * @return the address of the owner.
   */
  function owner() public view returns (address) {
    return _owner;
  }

  /**
   * @dev Throws if called by any account other than the owner.
   */
  modifier onlyOwner() {
    require(isOwner());
    _;
  }

  /**
   * @return true if `msg.sender` is the owner of the contract.
   */
  function isOwner() public view returns (bool) {
    return msg.sender == _owner;
  }

  /**
   * @dev Allows the current owner to relinquish control of the contract.
   * It will not be possible to call the functions with the `onlyOwner`
   * modifier anymore.
   * @notice Renouncing ownership will leave the contract without an owner,
   * thereby removing any functionality that is only available to the owner.
   */
  function renounceOwnership() public onlyOwner {
    emit OwnershipTransferred(_owner, address(0));
    _owner = address(0);
  }

  /**
   * @dev Allows the current owner to transfer control of the contract to a newOwner.
   * @param newOwner The address to transfer ownership to.
   */
  function transferOwnership(address newOwner) public onlyOwner {
    _transferOwnership(newOwner);
  }

  /**
   * @dev Transfers control of the contract to a newOwner.
   * @param newOwner The address to transfer ownership to.
   */
  function _transferOwnership(address newOwner) internal {
    require(newOwner != address(0));
    emit OwnershipTransferred(_owner, newOwner);
    _owner = newOwner;
  }
}

library SafeMath {
  /**
   * @dev Multiplies two unsigned integers, reverts on overflow.
   */
  function mul(uint256 a, uint256 b) internal pure returns (uint256) {
    // Gas optimization: this is cheaper than requiring 'a' not being zero, but the
    // benefit is lost if 'b' is also tested.
    // See: https://github.com/OpenZeppelin/openzeppelin-solidity/pull/522
    if (a == 0) {
      return 0;
    }

    uint256 c = a * b;
    require(c / a == b);

    return c;
  }

  /**
   * @dev Integer division of two unsigned integers truncating the quotient, reverts on division by zero.
   */
  function div(uint256 a, uint256 b) internal pure returns (uint256) {
    // Solidity only automatically asserts when dividing by 0
    require(b > 0);
    uint256 c = a / b;
    // assert(a == b * c + a % b); // There is no case in which this doesn't hold

    return c;
  }

  /**
   * @dev Subtracts two unsigned integers, reverts on overflow (i.e. if subtrahend is greater than minuend).
   */
  function sub(uint256 a, uint256 b) internal pure returns (uint256) {
    require(b <= a);
    uint256 c = a - b;

    return c;
  }

  /**
   * @dev Adds two unsigned integers, reverts on overflow.
   */
  function add(uint256 a, uint256 b) internal pure returns (uint256) {
    uint256 c = a + b;
    require(c >= a);

    return c;
  }

  /**
   * @dev Divides two unsigned integers and returns the remainder (unsigned integer modulo),
   * reverts when dividing by zero.
   */
  function mod(uint256 a, uint256 b) internal pure returns (uint256) {
    require(b != 0);
    return a % b;
  }
}

contract CustomPolygonMapping is Ownable {
  using SafeMath for uint256;

  uint256 public counter;
  mapping(address => address) public registrations;

  event NewRegistration(address indexed user, address indexed sender, address indexed receiver);
  event RegistrationUpdated(address indexed user, address indexed sender, address indexed receiver);
  event StateSynced(uint256 indexed id, address indexed contractAddress, bytes data);

  modifier onlyRegistered(address receiver) {
    require(registrations[receiver] == msg.sender, 'Invalid sender');
    _;
  }

  function syncState(address receiver, bytes calldata data) external onlyRegistered(receiver) {
    counter = counter.add(1);
    emit StateSynced(counter, receiver, data);
    // THIS IS THE ONLY CUSTOM PART
    bool success;
    bytes memory resultData;
    (success, resultData) = receiver.call(
      abi.encodeWithSignature('onStateReceive(uint256,bytes)', counter, data)
    );
    require(success, 'FAILED_ACTION_EXECUTION_CUSTOM_MAPPING');
    // END THE CUSTOM PART
  }

  // register new contract for state sync
  function register(address sender, address receiver) public {
    require(
      isOwner() || registrations[receiver] == msg.sender,
      'StateSender.register: Not authorized to register'
    );
    registrations[receiver] = sender;
    if (registrations[receiver] == address(0)) {
      emit NewRegistration(msg.sender, sender, receiver);
    } else {
      emit RegistrationUpdated(msg.sender, sender, receiver);
    }
  }
}
