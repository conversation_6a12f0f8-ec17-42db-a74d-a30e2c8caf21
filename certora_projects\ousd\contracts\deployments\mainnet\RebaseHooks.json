{"abi": [{"constant": true, "inputs": [], "name": "governor", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "bool", "name": "sync", "type": "bool"}], "name": "postRebase", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [], "name": "claimGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "uniswapPairs", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address[]", "name": "_uniswapPairs", "type": "address[]"}], "name": "setUniswapPairs", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "isGovernor", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_newGovernor", "type": "address"}], "name": "transferGovernance", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "PendingGovernorshipTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousGovernor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGovernor", "type": "address"}], "name": "GovernorshipTransferred", "type": "event"}], "receipt": {"to": null, "from": "0xAed9fDc9681D61edB5F8B8E421f5cEe8D7F4B04f", "contractAddress": "0x3dcd70E6A3fB474cFd7567A021864066Fdef6C5c", "transactionIndex": 66, "gasUsed": "561478", "logsBloom": "0x08000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000048000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000800000000000000000000000000000000000004000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000001", "blockHash": "0x4b1aea0c448d828fcbe8f538e6843c415c7dd66b1d1a015d8d8428a3af45ebc0", "transactionHash": "0xbe1a1e9ce97f4e324d649e5724a595a7814eaa608352712cdaeb550f94c13ae7", "logs": [{"transactionIndex": 66, "blockNumber": 10953780, "transactionHash": "0xbe1a1e9ce97f4e324d649e5724a595a7814eaa608352712cdaeb550f94c13ae7", "address": "0x3dcd70E6A3fB474cFd7567A021864066Fdef6C5c", "topics": ["0xc7c0c772add429241571afb3805861fb3cfa2af374534088b76cdb4325a87e9a", "0x0000000000000000000000000000000000000000000000000000000000000000", "0x000000000000000000000000aed9fdc9681d61edb5f8b8e421f5cee8d7f4b04f"], "data": "0x", "logIndex": 134, "blockHash": "0x4b1aea0c448d828fcbe8f538e6843c415c7dd66b1d1a015d8d8428a3af45ebc0"}], "blockNumber": 10953780, "cumulativeGasUsed": "5717403", "status": 1, "byzantium": true}, "address": "0x3dcd70E6A3fB474cFd7567A021864066Fdef6C5c", "args": [], "solcInputHash": "0x220836c1329dc54af0d88aee0cedc3583e8babfcd78e6fa38ecf569d5d97f8fe", "metadata": "{\"compiler\":{\"version\":\"0.5.11+commit.c082d0b4\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"constant\":true,\"inputs\":[],\"name\":\"governor\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"bool\",\"name\":\"sync\",\"type\":\"bool\"}],\"name\":\"postRebase\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"claimGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"uniswapPairs\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"_uniswapPairs\",\"type\":\"address[]\"}],\"name\":\"setUniswapPairs\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"isGovernor\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_newGovernor\",\"type\":\"address\"}],\"name\":\"transferGovernance\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"PendingGovernorshipTransfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousGovernor\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newGovernor\",\"type\":\"address\"}],\"name\":\"GovernorshipTransferred\",\"type\":\"event\"}],\"devdoc\":{\"methods\":{\"claimGovernance()\":{\"details\":\"Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor.\"},\"governor()\":{\"details\":\"Returns the address of the current Governor.\"},\"isGovernor()\":{\"details\":\"Returns true if the caller is the current Governor.\"},\"transferGovernance(address)\":{\"details\":\"Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete\",\"params\":{\"_newGovernor\":\"Address of the new Governor\"}}}},\"userdoc\":{\"methods\":{}}},\"settings\":{\"compilationTarget\":{\"contracts/utils/RebaseHooks.sol\":\"RebaseHooks\"},\"evmVersion\":\"petersburg\",\"libraries\":{},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"contracts/governance/Governable.sol\":{\"keccak256\":\"0x342fa1b2e2cbe8d2d904c31e5a2b182446d3737db2f6704d3f247c6c733084ec\",\"urls\":[\"bzz-raw://93918076cf5ef49658f8dd78ac7aafe30a1e233cc49e70a642ee9559b78c3f28\",\"dweb:/ipfs/Qmcca1ncSQnxSRXs5FZHzH9dwfZ1KcuUfcAiBwgkbzfTeW\"]},\"contracts/interfaces/uniswap/IUniswapV2Pair.sol\":{\"keccak256\":\"0xf722b9b3a04647d5a596b92dbc9aa6208aa999a51a4380197d2762be9591a436\",\"urls\":[\"bzz-raw://127a6bfbefbbc07ce0e3c72c02280fb097c062f4ca33a2ec97edb8cc916e36c7\",\"dweb:/ipfs/QmZ9TffGaf9uXgsbxK5ns9UfNhpkngeqWS2LNdhCbN133X\"]},\"contracts/utils/RebaseHooks.sol\":{\"keccak256\":\"0x2ced7177631a4564560d504e6174bd03b702c86c679974b6420365df71630eb3\",\"urls\":[\"bzz-raw://9a98b3f6e8ad494b630470e612af94cb7f3b8c0c11891588ae0b0651bf1199c0\",\"dweb:/ipfs/Qmcf3RJVyZTQTAzvQ33XiFJyp6aNr8uu9pshS1csTn44Xh\"]}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"methods": {"claimGovernance()": {"details": "Claim Governance of the contract to a new account (`newGovernor`). Can only be called by the new Governor."}, "governor()": {"details": "Returns the address of the current Governor."}, "isGovernor()": {"details": "Returns true if the caller is the current Governor."}, "transferGovernance(address)": {"details": "Transfers Governance of the contract to a new account (`newGovernor`). Can only be called by the current Governor. Must be claimed for this to complete", "params": {"_newGovernor": "Address of the new Governor"}}}}, "userdoc": {"methods": {}}, "gasEstimates": {"creation": {"codeDepositCost": "448400", "executionCost": "infinite", "totalCost": "infinite"}, "external": {"claimGovernance()": "infinite", "governor()": "480", "isGovernor()": "540", "postRebase(bool)": "infinite", "setUniswapPairs(address[])": "infinite", "transferGovernance(address)": "infinite", "uniswapPairs(uint256)": "817"}}}