{"abi": [{"constant": true, "inputs": [], "name": "getReserves", "outputs": [{"internalType": "uint112", "name": "", "type": "uint112"}, {"internalType": "uint112", "name": "", "type": "uint112"}, {"internalType": "uint32", "name": "", "type": "uint32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "token0", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "price0CumulativeLast", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "price1CumulativeLast", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint112", "name": "_reserve0", "type": "uint112"}, {"internalType": "uint112", "name": "_reserve1", "type": "uint112"}], "name": "setReserves", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "token1", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_token0", "type": "address"}, {"internalType": "address", "name": "_token1", "type": "address"}, {"internalType": "uint112", "name": "_reserve0", "type": "uint112"}, {"internalType": "uint112", "name": "_reserve1", "type": "uint112"}], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}], "receipt": {"to": null, "from": "0xD85A569F3C26f81070544451131c742283360400", "contractAddress": "0xB48248D2e90f53E03dC12F4143626A165B547278", "transactionIndex": 7, "gasUsed": "450364", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0xa092338e31edd6a30b9961c4763144b5f49cf08c38e2433a042f151c899fa69e", "transactionHash": "0x56986881a40bafc0ca421e753a85c482c4d517ae548536c653216b5fd9d6930d", "logs": [], "blockNumber": 7216812, "cumulativeGasUsed": "861628", "status": 1, "byzantium": true}, "address": "0xB48248D2e90f53E03dC12F4143626A165B547278", "args": ["0xCE15990d54510B8Fa81ad9B70bEC5F4E395eeb82", "0x2A019403290D258b3bDF14051eF1791c8804E307", "100000000", "1000000000000000000"], "solcInputHash": "0xb3650c08a4e2e14ecccb8f0a47293db3f4f322627843b6172e27a33d674d0b0a", "metadata": "{\"compiler\":{\"version\":\"0.5.11+commit.c082d0b4\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"constant\":true,\"inputs\":[],\"name\":\"getReserves\",\"outputs\":[{\"internalType\":\"uint112\",\"name\":\"\",\"type\":\"uint112\"},{\"internalType\":\"uint112\",\"name\":\"\",\"type\":\"uint112\"},{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"token0\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"price0CumulativeLast\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"price1CumulativeLast\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint112\",\"name\":\"_reserve0\",\"type\":\"uint112\"},{\"internalType\":\"uint112\",\"name\":\"_reserve1\",\"type\":\"uint112\"}],\"name\":\"setReserves\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"token1\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_token0\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_token1\",\"type\":\"address\"},{\"internalType\":\"uint112\",\"name\":\"_reserve0\",\"type\":\"uint112\"},{\"internalType\":\"uint112\",\"name\":\"_reserve1\",\"type\":\"uint112\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"}],\"devdoc\":{\"methods\":{}},\"userdoc\":{\"methods\":{}}},\"settings\":{\"compilationTarget\":{\"contracts/mocks/MockUniswapPair.sol\":\"MockUniswapPair\"},\"evmVersion\":\"petersburg\",\"libraries\":{},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"contracts/mocks/MockUniswapPair.sol\":{\"keccak256\":\"0xbaa1898077c343eaf69cbeb6cbbca19781157ece79ec278a4efc180471c2914f\",\"urls\":[\"bzz-raw://5cca7726294706651ac993b0b8764be7a7e75769aa64a84bd7523eca564b632f\",\"dweb:/ipfs/QmQP8inw8baQ4Mxewi6tDDpfrxrTeDDD2i8BtvsPaBEss9\"]},\"contracts/oracle/UniswapLib.sol\":{\"keccak256\":\"0xef725a68d8d5afc071b89d7428ff75f9482162372710ab0e77af1f509198a103\",\"urls\":[\"bzz-raw://3505dc007ed400db738c6356ad4154f4bf50b1d17d024e4e904fa36a2a90dadb\",\"dweb:/ipfs/QmYxGKey3Sx5VknKDnV65dsJtwsKn6RfJvYBSvMiWWXR4M\"]}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"methods": {}}, "userdoc": {"methods": {}}, "gasEstimates": {"creation": {"codeDepositCost": "284000", "executionCost": "infinite", "totalCost": "infinite"}, "external": {"getReserves()": "1017", "price0CumulativeLast()": "1224", "price1CumulativeLast()": "1246", "setReserves(uint112,uint112)": "60932", "token0()": "461", "token1()": "549"}}}