/* IMPORTANT these are duplicated in `dapp/src/constants/contractAddresses` changes here should
 * also be done there.
 */

const addresses = {};

// Utility addresses
addresses.zero = "0x0000000000000000000000000000000000000000";
addresses.dead = "0x0000000000000000000000000000000000000001";

addresses.mainnet = {};
// Native stablecoins
addresses.mainnet.Binance = "0x3f5CE5FBFe3E9af3971dD833D26bA9b5C936f0bE";
addresses.mainnet.DAI = "0x6b175474e89094c44da98b954eedeac495271d0f";
addresses.mainnet.USDC = "0xA0b86991c6218b36c1d19D4a2e9Eb0cE3606eB48";
addresses.mainnet.USDT = "0xdAC17F958D2ee523a2206206994597C13D831ec7";
addresses.mainnet.TUSD = "0x0000000000085d4780B73119b644AE5ecd22b376";
// AAVE
addresses.mainnet.AAVE_ADDRESS_PROVIDER =
  "0x24a42fD28C976A61Df5D00D0599C34c4f90748c8";
addresses.mainnet.Aave = "0x7Fc66500c84A76Ad7e9c93437bFc5Ac33E2DDaE9";
addresses.mainnet.aTUSD = "0x4DA9b813057D04BAef4e5800E36083717b4a0341";
addresses.mainnet.aUSDT = "0x71fc860F7D3A592A4a98740e39dB31d25db65ae8";
addresses.mainnet.aDAI = "0xfC1E690f61EFd961294b3e1Ce3313fBD8aa4f85d";
addresses.mainnet.aUSDC = "0x9bA00D6856a4eDF4665BcA2C2309936572473B7E";

// Compound
addresses.mainnet.COMP = "0xc00e94Cb662C3520282E6f5717214004A7f26888";
addresses.mainnet.cDAI = "0x5d3a536e4d6dbd6114cc1ead35777bab948e3643";
addresses.mainnet.cUSDC = "0x39aa39c021dfbae8fac545936693ac917d5e7563";
addresses.mainnet.cUSDT = "0xf650c3d88d12db855b8bf7d11be6c55a4e07dcc9";
// Curve
addresses.mainnet.CRV = "0xd533a949740bb3306d119cc777fa900ba034cd52";
addresses.mainnet.CRVMinter = "0xd061D61a4d941c39E5453435B6345Dc261C2fcE0";
addresses.mainnet.ThreePool = "0xbEbc44782C7dB0a1A60Cb6fe97d0b483032FF1C7";
addresses.mainnet.ThreePoolToken = "******************************************";
addresses.mainnet.ThreePoolGauge = "******************************************";
// Open Oracle
addresses.mainnet.openOracle = "******************************************";
// OGN
addresses.mainnet.OGN = "******************************************";

// Uniswap router
addresses.mainnet.uniswapRouter = "******************************************";
// Chainlink feeds
addresses.mainnet.chainlinkETH_USD =
  "******************************************";
addresses.mainnet.chainlinkDAI_ETH =
  "******************************************";
addresses.mainnet.chainlinkUSDC_ETH =
  "******************************************";
addresses.mainnet.chainlinkUSDT_ETH =
  "******************************************";
// WETH Token
addresses.mainnet.WETH = "******************************************";
// Deployed OUSD contracts
addresses.mainnet.VaultProxy = "******************************************";
addresses.mainnet.Vault = "******************************************";
addresses.mainnet.OUSDProxy = "******************************************";
addresses.mainnet.OUSD = "******************************************";
addresses.mainnet.CompoundStrategyProxy =
  "******************************************";
addresses.mainnet.CompoundStrategy =
  "******************************************";
addresses.mainnet.CurveUSDCStrategyProxy =
  "******************************************";
addresses.mainnet.CurveUSDCStrategy =
  "******************************************";
addresses.mainnet.CurveUSDTStrategyProxy =
  "******************************************";
addresses.mainnet.CurveUSDTStrategy =
  "******************************************";

addresses.mainnet.MixOracle = "******************************************";
addresses.mainnet.ChainlinkOracle =
  "0x8DE3Ac42F800a1186b6D70CB91e0D6876cC36759";
addresses.mainnet.UniswapOracle = "0xc15169Bad17e676b3BaDb699DEe327423cE6178e";

/* --- RINKEBY --- */
addresses.rinkeby = {};

addresses.rinkeby.OGN = "0xA115e16ef6e217f7a327a57031F75cE0487AaDb8";

// Compound
addresses.rinkeby.cDAI = "0x6d7f0754ffeb405d23c51ce938289d4835be3b14";
addresses.rinkeby.cUSDC = "0x5b281a6dda0b271e91ae35de655ad301c976edb1";
addresses.rinkeby.cUSDT = "0x2fb298bdbef468638ad6653ff8376575ea41e768";

module.exports = addresses;
